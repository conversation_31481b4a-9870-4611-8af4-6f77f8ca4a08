# OSX trash
.DS_Store

# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, build with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Files generated by JetBrains IDEs
.idea/
*.iml

# Vscode files
.vscode

# Emacs save files
*~
\#*\#
.\#*

# Vim-related files
[._]*.s[a-w][a-z]
[._]s[a-w][a-z]
*.un~
Session.vim
.netrwhist

# make-related metadata
/.make/

# go project structure
vendor
bin
/log/
setting/dev.json
play/
scratch*

conf/*/*.properties
conf/local/
conf/test/
go.sum
plugins/go.sum
main
*.log
*.lock
*.apollo.json

api_server

conf/_config.sync.json
conf/_config_*
conf/*.json
scripts/*/
conf/smartrouting*
*.sh
