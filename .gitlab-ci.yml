# This file is a template, and might need editing before it works on your project.
image: "harbor.shopeemobile.com/shopee/sls-cicd-base:go1.17.12"

before_script:
  - echo "before_script"
  - export GO111MODULE="on"
  - export GOPRIVATE="git.garena.com"
  - go version
  - go env



    ####                                            ####
    ### Start modify your CI configuration from here ###
    ###
  ####
#variables:
#  MY_VERSION: $CI_BUILD_REF_NAME

####                                            ####
### End modify your CI configuration from here   ###
###                                             ####

stages:
  - codecheck
  - push
  - build


codecheck:
  stage: codecheck
  script:
    - test -z "$(gofmt -l $(find . -name '*.go' -type f -print) | tee /dev/stderr)"

build-admin:
  stage: build
  script:
    - go mod tidy
    - go build ./cmd/admin_server

build-grpc:
  stage: build
  script:
    - go mod tidy
    - go build ./cmd/grpc_server

build-task:
  stage: build
  script:
    - go mod tidy
    - go build ./cmd/task_server

build-batask:
  stage: build
  script:
    - go mod tidy
    - go build ./cmd/batask_server