// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/adminfacade"
	allocation3 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/adminfacade/allocation"
	routing3 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/adminfacade/routing"
	volume_dashboard2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/adminfacade/volume_dashboard"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/auditclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/ccclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/chargeclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/dataclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lcosclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lfsclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/llsclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lnpclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/locationclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lpsclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/spex_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/wbcclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/address"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/allocate_order_data_repo"
	batch_allocate2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/batch_allocate"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/forecast"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/forecast/repo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/forecast_unit"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/model"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/parcel_type_definition"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/pickup_priority"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rulevolume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/whitelist"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/audit_log"
	repo2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/available_lh/repo"
	allocation2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/batch_allocate/allocation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/batch_allocate/allocation/order"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/batch_allocate/batch_minute_order_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/business_audit"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/cc_routing_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/export_task"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/forecasting_sub_task"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/lane"
	repo3 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/lh_capacity/repo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/locationzone"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/locationzone/zone"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/masking_forecast"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/masking_forecast/forecast_volume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/outercheck"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/product"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/productpriority"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/forecast/forecastrepo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/forecast/forecastservice"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/routing_config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/routing_log"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/routing_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/rule_data"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/schedule_factor"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/sync_lfs_order"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/schedule_visual"
	repository2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/schedule_visual/repository"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/schedule_visual/schedule_stat"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/smart_routing_forecast"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volume_counter"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volume_dashboard"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volume_dashboard/repository"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volumerouting/vrrepo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volumerouting/vrservice"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/saturn_facade/allocate_volume_counter"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/saturn_facade/audit_log_task"
	batch_allocate3 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/saturn_facade/batch_allocate"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/saturn_facade/batch_allocate_forecast"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/saturn_facade/forecast_chain"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/saturn_facade/masking"
	routing2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/saturn_facade/routing"
	masking2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/admin/masking"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/batch_allocate"
	service2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/batch_allocate/service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/batch_allocate/service/split_batch_chain"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/forecast/service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/masking_priority"
	parcel_type_definition2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/parcel_type_definition"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/pickup_efficiency_counter"
	rulevolume2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/rulevolume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/volumecounter"
	whitelist2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/whitelist"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocpath"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/available_lh"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/business_audit/approval_listener"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/business_audit/approval_manager"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/cc_routing"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/ilh_forecast_task"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/lh_capacity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/masking_result_panel"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/routing_visualization"
	schedule_visual2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/schedule_visual"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/select_lane"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/sync_data"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/volumerouting"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/cacheutil/layercache"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/redisutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/redisutil/counter"
)

import (
	_ "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/cacheutil/localcache"
	_ "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/cacheutil/localcache/lcregistry"
)

// Injectors from injector.go:

func InitAdminFacade() (*adminfacade.AdminFacade, error) {
	pingFacade := &adminfacade.PingFacade{}
	levelCache, err := layercache.NewLayerCache()
	if err != nil {
		return nil, err
	}
	locationClientImpl := locationclient.NewLocationClientImpl(levelCache)
	addrRepoImpl := address.NewAddrRepoImpl(locationClientImpl)
	redisCounter := counter.NewRedisCounterImpl()
	lpsApiImpl := lpsclient.NewLpsApiImpl()
	chargeApiImpl := chargeclient.NewChargeApiImpl()
	iMaskRuleVolumeRepo := rulevolume.NewMaskRuleVolumeRepoImpl(lpsApiImpl)
	allocateForecastTaskConfigRepoImpl := repo.NewAllocateForecastTaskConfigRepoImpl()
	checkVolumeFinderImpl := rulevolume2.NewCheckVolumeFinderImpl(iMaskRuleVolumeRepo)
	auditApiImpl := auditclient.NewAuditApiImpl()
	businessAuditRepoImpl := business_audit.NewBusinessAuditRepoImpl()
	approvalExecutorImpl := approval_manager.NewApprovalExecutorImpl(auditApiImpl, businessAuditRepoImpl)
	maskRuleVolumeServiceImpl := rulevolume2.NewMaskRuleVolumeServiceImpl(addrRepoImpl, lpsApiImpl, iMaskRuleVolumeRepo, levelCache, checkVolumeFinderImpl, approvalExecutorImpl, businessAuditRepoImpl)
	loadForecastVolumeConfig := masking.NewLoadForecastVolumeConfig(iMaskRuleVolumeRepo, allocateForecastTaskConfigRepoImpl, maskRuleVolumeServiceImpl)
	allocateShippingFeeImpl := repo.NewAllocateShippingFeeServiceImpl(chargeApiImpl, lpsApiImpl)
	allocationRuleImpl := rule.NewAllocationRuleImpl(lpsApiImpl)
	allocateForecastRankRepoImpl := repo.NewAllocateForecastRankImpl()
	priorityRepoImpl := productpriority.NewPriorityRepoImpl(lpsApiImpl)
	priorityBusinessImpl := productpriority.NewPriorityBusinessImpl(priorityRepoImpl, lpsApiImpl)
	forecastingSubTaskRepoImpl := forecasting_sub_task.NewForecastingSubTaskRepoImpl()
	forecastingSubTaskServiceImpl := forecasting_sub_task.NewForecastingSubTaskServiceImpl(forecastingSubTaskRepoImpl)
	allocateForecastTaskConfigServiceImpl := service.NewAllocateForecastTaskConfigServiceImpl(allocateForecastTaskConfigRepoImpl, lpsApiImpl, allocateShippingFeeImpl, iMaskRuleVolumeRepo, allocationRuleImpl, maskRuleVolumeServiceImpl, allocateForecastRankRepoImpl, priorityBusinessImpl, forecastingSubTaskServiceImpl, forecastingSubTaskRepoImpl)
	allocationConfigImpl := config.NewAllocationConfigImpl(lpsApiImpl)
	allocateOrderDataRepoImpl := allocate_order_data_repo.NewAllocateOrderDataRepoImpl(addrRepoImpl)
	allocateDateRankRepoImpl := repo.NewAllocateDateRankRepo()
	allocateHistoricalRankRepoImpl := repo.NewAllocateHistoricalRankRepo()
	allocateRankServiceImpl := service.NewAllocateRankServiceImpl(lpsApiImpl, allocateDateRankRepoImpl, allocateHistoricalRankRepoImpl, allocateForecastRankRepoImpl, allocateForecastTaskConfigServiceImpl)
	allOuterCheckServiceImpl := outercheck.NewAllOuterCheckServiceImpl(priorityBusinessImpl)
	forecastVolumeRepo := forecast_volume.NewForecastVolumeRepo()
	forecastLocationVolumeService := forecast_volume.NewForecastLocationVolumeServiceImpl(forecastVolumeRepo)
	maskVolumeCounterImpl := volumecounter.NewMaskVolumeCounterImpl()
	shopWhitelistRepoImpl := whitelist.NewShopWhitelistRepoImpl()
	shopWhitelistServiceImpl := whitelist2.NewShopWhitelistServiceImpl(shopWhitelistRepoImpl)
	softRuleService := allocation.NewSoftRuleService(maskVolumeCounterImpl, chargeApiImpl, shopWhitelistServiceImpl)
	maskConfigRepoImpl := config.NewMaskConfigRepo(allocationRuleImpl, allocationConfigImpl)
	maskRuleConfRepo := rule.NewMaskRuleConfRepo()
	pickupPriorityRepoImpl := pickup_priority.NewPickupPriorityRepoImpl()
	maskRuleRepoImpl := rule.NewRuleRepo(iMaskRuleVolumeRepo, maskRuleVolumeServiceImpl, maskRuleConfRepo, lpsApiImpl, allocationRuleImpl, priorityRepoImpl, approvalExecutorImpl, businessAuditRepoImpl, pickupPriorityRepoImpl)
	lfsApiImpl := lfsclient.NewLfsApiImpl()
	llsApiImpl := llsclient.NewLlsApiImpl()
	laneServiceImpl := lane.NewLaneService(lfsApiImpl, llsApiImpl)
	volumeCounterImpl := volume_counter.NewVolumeCounterImpl(redisCounter, laneServiceImpl)
	client, err := redisutil.Client()
	if err != nil {
		return nil, err
	}
	maskingScheduleVisualStat := schedule_stat.NewMaskingScheduleVisualStat(client)
	maskingScheduleVisualStatV2 := schedule_stat.NewMaskingScheduleVisualStatV2(client)
	maskingForecastScheduleVisualStat := schedule_stat.NewMaskingForecastScheduleVisualStat(client)
	scheduleVisualSet := schedule_stat.NewScheduleVisualSet(maskingScheduleVisualStat, maskingScheduleVisualStatV2, maskingForecastScheduleVisualStat)
	scheduleCountStat := schedule_visual.NewScheduleCountStat(scheduleVisualSet)
	batchAllocateOrderRepoImpl := order.NewBatchAllocateOrderRepo()
	greyServiceImpl := service2.NewGreyServiceImpl()
	parcelTypeDefinitionRepoImpl := parcel_type_definition.NewParcelTypeDefinitionRepoImpl()
	parcelTypeDefinitionServiceImpl := parcel_type_definition2.NewParcelTypeDefinitionServiceImpl(parcelTypeDefinitionRepoImpl, lpsApiImpl)
	pickupEffCounterImpl := pickup_efficiency_counter.NewPickupEffCounterImpl()
	spexServiceImpl := spex_service.NewSpexServiceImpl()
	lcosApiImpl := lcosclient.NewLcosApiImpl()
	allocationServiceImpl := allocation.NewAllocationService(softRuleService, maskRuleVolumeServiceImpl, maskConfigRepoImpl, maskRuleRepoImpl, allOuterCheckServiceImpl, maskVolumeCounterImpl, volumeCounterImpl, scheduleCountStat, iMaskRuleVolumeRepo, batchAllocateOrderRepoImpl, greyServiceImpl, parcelTypeDefinitionServiceImpl, pickupEffCounterImpl, spexServiceImpl, lcosApiImpl, lpsApiImpl, llsApiImpl, addrRepoImpl)
	allocateForecastServiceImpl := masking_forecast.NewAllocateForecastTaskServiceImpl(allocationConfigImpl, allocateOrderDataRepoImpl, allocateRankServiceImpl, allOuterCheckServiceImpl, maskRuleVolumeServiceImpl, forecastLocationVolumeService, allocationServiceImpl, lpsApiImpl, addrRepoImpl, client, scheduleCountStat, chargeApiImpl)
	jobChainServiceImpl := forecast_chain.NewJobChainServiceImpl(forecastingSubTaskServiceImpl, allocateForecastTaskConfigServiceImpl, allocateRankServiceImpl, lpsApiImpl, allocationConfigImpl, client, addrRepoImpl, allocateForecastServiceImpl, allocateOrderDataRepoImpl, scheduleCountStat)
	maskingForecast := masking.NewMaskingForecast(allocateForecastTaskConfigServiceImpl, allocateForecastTaskConfigRepoImpl, allocateForecastServiceImpl, forecastingSubTaskRepoImpl, jobChainServiceImpl)
	allocateStoreConsumer := masking.NewAllocateStoreConsumer(allocateOrderDataRepoImpl, allocateRankServiceImpl)
	auditLogRepoImpl := audit_log.NewAuditLogRepoImpl()
	auditLogServiceImpl := audit_log.NewAuditLogServiceImpl(auditLogRepoImpl)
	auditLogTaskServer := audit_log_task.NewAuditLogTaskServer(auditLogServiceImpl)
	deleteMaskingSubTaskImpl := masking.NewDeleteMaskingSubTaskImpl(forecastingSubTaskServiceImpl)
	checkMaskingProcessTaskImpl := masking.NewCheckMaskingProcessTaskImpl(forecastingSubTaskServiceImpl, allocateForecastTaskConfigRepoImpl)
	getForecastTotalCountImpl := masking.NewGetForecastTotalCountImpl(allocateForecastTaskConfigRepoImpl, allocateOrderDataRepoImpl, forecastingSubTaskServiceImpl)
	allocateStoreHbaseConsumer := masking.NewAllocateStoreHbaseConsumer(allocateOrderDataRepoImpl)
	lineCheapestShippingFeeFactor := schedule_factor.NewLineCheapestShippingFeeFactor(chargeApiImpl, laneServiceImpl)
	preCalFeeServiceImpl := routing.NewRoutingPreCalFeeServiceImpl(lineCheapestShippingFeeFactor)
	ilhWeightCounterImpl := volume_counter.NewILHWeightCounterImpl()
	routingServiceImpl := routing.NewRoutingServiceImpl(laneServiceImpl, preCalFeeServiceImpl, ilhWeightCounterImpl)
	locationZoneDaoImpl := zone.NewLocationZoneDaoImpl()
	zoneRepoImpl := locationzone.NewZoneRepoImpl(locationZoneDaoImpl, lpsApiImpl, addrRepoImpl, levelCache)
	softRuleRepoImpl := ruledata.NewSoftRuleRepoImpl(lpsApiImpl)
	routingRuleRepoImpl := routing.NewRoutingRuleRepo(zoneRepoImpl, lpsApiImpl, laneServiceImpl, llsApiImpl, softRuleRepoImpl)
	routingConfigRepoImpl := ruledata.NewRoutingConfigRepoImpl(lpsApiImpl)
	routingConfigServiceImpl := routing_config.NewRoutingConfigServiceImpl(routingConfigRepoImpl, lpsApiImpl, softRuleRepoImpl, levelCache)
	dgFactor := schedule_factor.NewDgFactor()
	minVolumeFactor := schedule_factor.NewMinVolumeFactor(volumeCounterImpl)
	maxCapacityFactor := schedule_factor.NewMaxCapacityFactor(volumeCounterImpl, parcelTypeDefinitionServiceImpl)
	minWeightFactor := schedule_factor.NewMinWeightFactor(volumeCounterImpl)
	maxWeightFactor := schedule_factor.NewMaxWeightFactor(volumeCounterImpl)
	linePriorityFactor := schedule_factor.NewLinePriorityFactor()
	defaultWeightageFactor := schedule_factor.NewDefaultWeightageFactor()
	defaultPriorityFactor := schedule_factor.NewDefaultPriorityFactor()
	zoneRuleRepoImpl := vrrepo.NewZoneRuleRepoImpl()
	zoneGroupRepoImpl := vrrepo.NewZoneGroupRepoImpl(zoneRuleRepoImpl, routingConfigRepoImpl)
	vrrepoZoneRepoImpl := vrrepo.NewZoneRepoImpl()
	taskRepoImpl := vrrepo.NewTaskRepoImpl()
	serviceImpl := vrservice.NewServiceImpl(zoneGroupRepoImpl, vrrepoZoneRepoImpl, zoneRuleRepoImpl, taskRepoImpl, addrRepoImpl, redisCounter)
	lnpApiImpl := lnpclient.NewLnpApiImpl()
	zoneRuleMgrImpl := volumerouting.NewZoneRuleMgrImpl(serviceImpl, zoneRuleRepoImpl, zoneGroupRepoImpl, vrrepoZoneRepoImpl, taskRepoImpl, lpsApiImpl, routingConfigRepoImpl, lnpApiImpl, parcelTypeDefinitionServiceImpl)
	minVolumeV2 := schedule_factor.NewMinVolumeV2(zoneRuleMgrImpl)
	maxVolumeV2 := schedule_factor.NewMaxVolumeV2(zoneRuleMgrImpl)
	ilhParcelMinVolumeFactor := schedule_factor.NewILHParcelMinVolumeFactor(volumeCounterImpl)
	ilhParcelMaxCapacityFactor := schedule_factor.NewILHParcelMaxCapacityFactor(volumeCounterImpl)
	combinationPriorityFactor := schedule_factor.NewCombinationPriorityFactor()
	factorSet := schedule_factor.NewFactorSet(dgFactor, minVolumeFactor, maxCapacityFactor, minWeightFactor, maxWeightFactor, linePriorityFactor, lineCheapestShippingFeeFactor, defaultWeightageFactor, defaultPriorityFactor, minVolumeV2, maxVolumeV2, ilhParcelMinVolumeFactor, ilhParcelMaxCapacityFactor, combinationPriorityFactor)
	routingLogRepoImpl := routing_log.NewRoutingLogRepoImpl()
	routingLogServiceImpl := routing_log.NewRoutingLogServiceImpl(volumeCounterImpl, laneServiceImpl, lpsApiImpl, routingLogRepoImpl)
	ccRoutingRuleRepoImpl := cc_routing_rule.NewCCRoutingRuleRepoImpl()
	ccApiImpl := ccclient.NewCCApiImpl()
	ccRoutingServiceImpl := cc_routing.NewCCRoutingServiceImpl(ccRoutingRuleRepoImpl, ccApiImpl, lpsApiImpl)
	availableLHRepoImpl := repo2.NewAvailableLHRepoImpl()
	availableLHServiceImpl := available_lh.NewAvailableLHServiceImpl(availableLHRepoImpl)
	lhCapacityRepoImpl := repo3.NewLHCapacityRepoImpl()
	lhCapacityServiceImpl := lh_capacity.NewLHCapacityServiceImpl(lhCapacityRepoImpl, laneServiceImpl)
	ilhRoutingServiceImpl := routing.NewILHRoutingServiceImpl(ilhWeightCounterImpl)
	smartRoutingServiceImpl := select_lane.NewSmartRoutingServiceImpl(routingServiceImpl, routingRuleRepoImpl, lfsApiImpl, routingConfigServiceImpl, addrRepoImpl, factorSet, routingLogServiceImpl, laneServiceImpl, ccRoutingServiceImpl, availableLHServiceImpl, lhCapacityServiceImpl, ilhRoutingServiceImpl, parcelTypeDefinitionServiceImpl)
	forecastrepoServiceImpl := forecastrepo.NewServiceImpl(zoneRuleRepoImpl)
	dataApi := dataclient.NewDataApi()
	wbcApiImpl := wbcclient.NewWbcApi()
	forecastTaskServiceImpl := forecastservice.NewForecastTaskServiceImpl(routingConfigServiceImpl, forecastrepoServiceImpl, lpsApiImpl, ccRoutingServiceImpl, lineCheapestShippingFeeFactor, routingLogServiceImpl, dataApi, wbcApiImpl, chargeApiImpl)
	orderSyncRepoImpl := sync_lfs_order.NewOrderSyncRepoImpl()
	ilhForecastTaskRepoImpl := forecastrepo.NewILHForecastTaskRepoImpl()
	ilhForecastTaskServiceImpl := ilh_forecast_task.NewILHForecastTaskServiceImpl(ilhForecastTaskRepoImpl, availableLHRepoImpl, lhCapacityRepoImpl, laneServiceImpl, lpsApiImpl)
	smartRoutingForecastServiceImpl := smart_routing_forecast.NewSmartRoutingForecastServiceImpl(forecastrepoServiceImpl, orderSyncRepoImpl, routingRuleRepoImpl, routingServiceImpl, laneServiceImpl, zoneRepoImpl, lfsApiImpl, lpsApiImpl, forecastTaskServiceImpl, softRuleRepoImpl, zoneRuleRepoImpl, zoneGroupRepoImpl, vrrepoZoneRepoImpl, ilhForecastTaskServiceImpl, availableLHServiceImpl, lhCapacityServiceImpl, parcelTypeDefinitionServiceImpl)
	maskProductOrderNumRepoImpl := repository.NewMaskProductOrderNumRepoImpl()
	exportTaskRepoImpl := export_task.NewExportTaskRepoImpl()
	maskingVolumeServiceImpl := volume_dashboard.NewMaskingVolumeServiceImpl(volumeCounterImpl, maskProductOrderNumRepoImpl, lpsApiImpl, exportTaskRepoImpl, maskRuleVolumeServiceImpl)
	reportMaskingVolumeTask := masking.NewReportMaskingVolumeTask(maskingVolumeServiceImpl)
	syncDataImpl := sync_data.NewSyncDataImpl(allocateOrderDataRepoImpl)
	batchAllocateForecastRepoImpl := repo.NewBatchAllocateForecastRepoImpl()
	orderCollectorImpl := model.NewOrderCollectorImpl(addrRepoImpl)
	splittingRuleImpl := model.NewSplittingRuleImpl(orderCollectorImpl)
	batchAllocateForecastVolumeImpl := rulevolume.NewBatchAllocateForecastVolumeImpl()
	batchUnitTargetResultRepoImpl := repo.NewBatchUnitTargetResultRepoImap()
	batchUnitFeeResultRepoImpl := repo.NewBatchUnitFeeResultRepoImpl()
	batchAllocateForecastUnitResultRepoImpl := repo.NewBatchAllocateForecastUnitResultRepoImpl()
	batchForecastUnitServiceImpl := forecast_unit.NewBatchForecastUnitServiceImpl()
	batchMinuteOrderConfRepoImpl := batch_minute_order_conf.NewBatchMinuteOrderConfRepoImpl()
	batchMinuteOrderConfServiceImpl := batch_allocate.NewBatchMinuteOrderConfServiceImpl(batchMinuteOrderConfRepoImpl)
	startBatchForecastUnitImpl := masking.NewStartBatchForecastUnitImpl(batchAllocateForecastRepoImpl, splittingRuleImpl, batchAllocateForecastVolumeImpl, lpsApiImpl, chargeApiImpl, allocateForecastTaskConfigRepoImpl, batchUnitTargetResultRepoImpl, batchUnitFeeResultRepoImpl, batchAllocateForecastUnitResultRepoImpl, forecastLocationVolumeService, batchForecastUnitServiceImpl, batchMinuteOrderConfServiceImpl, shopWhitelistServiceImpl)
	createBASubTask := batch_allocate_forecast.NewCreateBASubTask(allocateForecastTaskConfigServiceImpl)
	allocateHistoryOutlineImpl := repo.NewAllocateHistoryOutlineImpl()
	allocateHistoryOutLine := masking.NewAllocateHistory(allocateHistoryOutlineImpl)
	batchAllocateSubTaskOutlineRepoImpl := repo.NewBatchAllocateSubTaskOutlineRepoImpl()
	batchAllocateForecastImpl := service.NewBatchAllocateForecastImpl(allocateForecastTaskConfigRepoImpl, addrRepoImpl, lpsApiImpl, batchAllocateForecastVolumeImpl, batchAllocateForecastRepoImpl, allocateDateRankRepoImpl, allocateHistoryOutlineImpl, batchUnitFeeResultRepoImpl, batchAllocateForecastUnitResultRepoImpl, batchAllocateSubTaskOutlineRepoImpl, batchAllocateOrderRepoImpl, pickupEffCounterImpl)
	parseBatchVolume := masking.NewParseBatchVolume(allocateForecastTaskConfigRepoImpl, batchAllocateForecastImpl, batchAllocateForecastVolumeImpl)
	batchAllocateForecastUnitRepoImpl := repo.NewBatchAllocateForecastUnitRepoImpl()
	batchAllocateSubTaskRepoImpl := repo.NewBatchAllocateSubTaskRepoImpl()
	batchAllocateForecastServiceImpl := forecast.NewBatchAllocateForecastServiceImpl(allocateForecastTaskConfigRepoImpl, batchAllocateForecastUnitRepoImpl, batchAllocateSubTaskRepoImpl, batchUnitFeeResultRepoImpl, batchAllocateForecastUnitResultRepoImpl)
	updateBatchAllocateForecastTaskImpl := masking.NewUpdateBatchAllocateForecastTaskImpl(batchAllocateForecastServiceImpl)
	allocateScheduleVisualTask := masking.NewAllocateScheduleVisualTask(scheduleCountStat)
	scheduleCountStatTask := masking.NewScheduleCountStatTask(scheduleCountStat)
	baForecastToolProgressImpl := batch_allocate_forecast.NewBAForecastToolProgressImpl(allocateForecastTaskConfigRepoImpl, batchAllocateForecastRepoImpl, batchUnitFeeResultRepoImpl)
	splitBatchRepoImpl := batch_allocate2.NewSplitBatchRepoImpl()
	executorImpl := split_batch_chain.NewExecutorImpl(splitBatchRepoImpl, batchAllocateOrderRepoImpl, maskRuleRepoImpl)
	splitBatchServerImpl := service2.NewSplitBatchServerImpl(executorImpl)
	splitBatchAllocateOrdersTask := masking.NewSplitBatchAllocateOrdersTask(splitBatchServerImpl, lpsApiImpl)
	batchAllocateServiceImpl := allocation2.NewBatchAllocateService(batchAllocateOrderRepoImpl, maskRuleVolumeServiceImpl, maskRuleRepoImpl, maskVolumeCounterImpl, splitBatchRepoImpl, batchMinuteOrderConfServiceImpl, lpsApiImpl, levelCache, pickupEffCounterImpl, shopWhitelistServiceImpl)
	batchAllocateTask := batch_allocate3.NewBatchAllocateTask(batchAllocateServiceImpl)
	batchAbnormalInspectionTask := batch_allocate3.NewBatchAbnormalInspectionTask(batchAllocateServiceImpl)
	pushOrderResultTask := batch_allocate3.NewPushOrderResultTask(batchAllocateServiceImpl)
	abnormalBatchAllocateTask := batch_allocate3.NewAbnormalBatchAllocateTask(batchAllocateServiceImpl)
	batchAllocateHoldOrderConsumer := batch_allocate3.NewBatchAllocateHoldOrderConsumer(batchAllocateOrderRepoImpl)
	clearOrderAndResultTask := batch_allocate3.NewClearOrderAndResultTask(batchAllocateOrderRepoImpl)
	reportOrderCount := routing2.NewReportOrderCount(routingLogServiceImpl)
	scheduleRule := routing2.NewScheduleRule(routingRuleRepoImpl, maskRuleRepoImpl, lhCapacityServiceImpl, availableLHServiceImpl)
	batchAllocateMonitor := batch_allocate3.NewBatchAllocateMonitor(splitBatchRepoImpl, batchAllocateOrderRepoImpl)
	clearMaskingVolumeTask := masking.NewClearMaskingVolumeTask(maskingVolumeServiceImpl)
	checkoutFulfillmentProductCounter := allocate_volume_counter.NewCheckoutFulfillmentProductCounter(allocationServiceImpl)
	deductVolumeCounter := allocate_volume_counter.NewDeductVolumeCounter(allocationServiceImpl)
	prodRepoImpl := product.NewProdRepoImpl()
	allocationPathSrvImpl := allocpath.NewAllocationPathSrvImpl(dataApi, lpsApiImpl, prodRepoImpl, maskRuleVolumeServiceImpl)
	makeUpAsyncAllocationLog := batch_allocate3.NewMakeUpAsyncAllocationLog(allocationPathSrvImpl)
	ilhForecastTask := routing2.NewILHForecastTask(ilhForecastTaskServiceImpl, routingServiceImpl, smartRoutingForecastServiceImpl)
	debugFacade := &adminfacade.DebugFacade{
		Location:                            addrRepoImpl,
		RedisCounter:                        redisCounter,
		LpsApi:                              lpsApiImpl,
		RateClient:                          chargeApiImpl,
		LoadForecastVolumeConfig:            loadForecastVolumeConfig,
		MaskingForecast:                     maskingForecast,
		AllocateDataStore:                   allocateStoreConsumer,
		AuditLogTaskServer:                  auditLogTaskServer,
		DeleteMaskingSubTaskImpl:            deleteMaskingSubTaskImpl,
		CheckMaskingProcessTaskImpl:         checkMaskingProcessTaskImpl,
		GetForecastTotalCountImpl:           getForecastTotalCountImpl,
		AllocateStoreHbaseConsumer:          allocateStoreHbaseConsumer,
		SmartRoutingService:                 smartRoutingServiceImpl,
		ForecastTaskSrv:                     forecastTaskServiceImpl,
		AllocateForecastService:             allocateForecastServiceImpl,
		SmartRoutingForecastSrv:             smartRoutingForecastServiceImpl,
		AllocateOrderDataRepo:               allocateOrderDataRepoImpl,
		ReportMaskingVolumeTask:             reportMaskingVolumeTask,
		SyncDataService:                     syncDataImpl,
		BatchAllocateService:                startBatchForecastUnitImpl,
		CreateBASubTask:                     createBASubTask,
		AllocateHistoryOutLine:              allocateHistoryOutLine,
		ParseBatchVolumeService:             parseBatchVolume,
		UpdateBatchAllocateForecastTaskImpl: updateBatchAllocateForecastTaskImpl,
		AllocateScheduleVisualTask:          allocateScheduleVisualTask,
		ScheduleCountStatTask:               scheduleCountStatTask,
		BAForecastToolProgressService:       baForecastToolProgressImpl,
		SplitBatchAllocateOrdersTask:        splitBatchAllocateOrdersTask,
		BatchAllocateTask:                   batchAllocateTask,
		BatchAbnormalInspectionTask:         batchAbnormalInspectionTask,
		PushOrderResultTask:                 pushOrderResultTask,
		AbnormalBatchAllocateTask:           abnormalBatchAllocateTask,
		BatchAllocateHoldOrderConsumer:      batchAllocateHoldOrderConsumer,
		ClearOrderAndResultTask:             clearOrderAndResultTask,
		ReportOrderCountTask:                reportOrderCount,
		ScheduleRuleTask:                    scheduleRule,
		BatchAllocateOrderRepo:              batchAllocateOrderRepoImpl,
		BatchAllocateMonitorTask:            batchAllocateMonitor,
		ClearMaskingVolumeTask:              clearMaskingVolumeTask,
		CheckoutFulfillmentProductCounter:   checkoutFulfillmentProductCounter,
		DeductVolumeCounter:                 deductVolumeCounter,
		MakeUpAsyncAllocationLog:            makeUpAsyncAllocationLog,
		ILHForecastTask:                     ilhForecastTask,
	}
	zoneGroupManagerImpl := volumerouting.NewZoneGroupManagerImpl(zoneGroupRepoImpl, vrrepoZoneRepoImpl, prodRepoImpl)
	zoneManagerImpl := volumerouting.NewZoneManagerImpl(serviceImpl, vrrepoZoneRepoImpl, taskRepoImpl, zoneRuleRepoImpl)
	defaultSelectGroupRepoImpl := vrrepo.NewDefaultSelectGroupRepoImpl()
	defaultSelectGroupServiceImpl := vrservice.NewDefaultSelectGroupServiceImpl(defaultSelectGroupRepoImpl, lpsApiImpl)
	volumeRoutingFacade := &adminfacade.VolumeRoutingFacade{
		ZoneGroupMgr:              zoneGroupManagerImpl,
		ZoneMgr:                   zoneManagerImpl,
		ZoneRuleMgr:               zoneRuleMgrImpl,
		DefaultSelectGroupService: defaultSelectGroupServiceImpl,
	}
	softRuleImpl := routing_rule.NewSoftRuleImpl(laneServiceImpl, llsApiImpl, lfsApiImpl, routingConfigServiceImpl, softRuleRepoImpl, lpsApiImpl, zoneRuleRepoImpl)
	softRoutingFacade := &adminfacade.SoftRoutingFacade{
		SoftRuleServer:              softRuleImpl,
		RoutingConfigServer:         routingConfigServiceImpl,
		ZoneRepo:                    zoneRepoImpl,
		ForecastTaskSrv:             forecastTaskServiceImpl,
		ParcelTypeDefinitionService: parcelTypeDefinitionServiceImpl,
	}
	maskingConvertorMap := dataclient.NewMaskingConvertorMap()
	keyDataImpl := masking_result_panel.NewKeyDataImpl(dataApi, exportTaskRepoImpl, prodRepoImpl, maskingConvertorMap, lpsApiImpl)
	maskingResultPanelFacade := &allocation3.MaskingResultPanelFacade{
		KeyDataManager: keyDataImpl,
	}
	maskingPathFacade := &allocation3.MaskingPathFacade{
		AllocationPathSrv: allocationPathSrvImpl,
	}
	pickupPriorityServiceImpl := masking_priority.NewPickupPriorityServiceImpl(pickupPriorityRepoImpl, allocationRuleImpl)
	maskingRuleFacade := &allocation3.MaskingRuleFacade{
		RuleRepo:                    maskRuleRepoImpl,
		VolumeCounter:               maskVolumeCounterImpl,
		RuleConfRepo:                maskRuleConfRepo,
		ProdRepo:                    prodRepoImpl,
		MaskConfigRepo:              maskConfigRepoImpl,
		LpsApi:                      lpsApiImpl,
		ShopWhitelistService:        shopWhitelistServiceImpl,
		PickupPriorityService:       pickupPriorityServiceImpl,
		ParcelTypeDefinitionService: parcelTypeDefinitionServiceImpl,
	}
	forecastserviceServiceImpl := forecastservice.NewForecastServiceImpl(forecastrepoServiceImpl, lfsApiImpl, routingRuleRepoImpl, zoneRepoImpl, volumeCounterImpl, laneServiceImpl, addrRepoImpl, zoneRuleMgrImpl, zoneRuleRepoImpl, lpsApiImpl, softRuleRepoImpl, dataApi)
	localForecastServiceImpl := smart_routing_forecast.NewLocalForecastServiceImpl(routingRuleRepoImpl, routingServiceImpl, zoneRepoImpl, laneServiceImpl, zoneGroupRepoImpl, vrrepoZoneRepoImpl)
	forecastFacade := &routing3.ForecastFacade{
		TaskService:     forecastserviceServiceImpl,
		ForecastTaskSrv: forecastTaskServiceImpl,
		ForecastSrv:     localForecastServiceImpl,
	}
	ccRoutingFacade := &adminfacade.CCRoutingFacade{
		CCRoutingSrv: ccRoutingServiceImpl,
	}
	routingVisualServiceImpl := routing_visualization.NewRoutingVisualServiceImpl(dataApi, wbcApiImpl, routingConfigServiceImpl)
	routingVisual := &adminfacade.RoutingVisual{
		RoutingVisualService: routingVisualServiceImpl,
	}
	maskingForecastFacade := &allocation3.MaskingForecastFacade{
		AllocateForecastTaskConfigService: allocateForecastTaskConfigServiceImpl,
		AllocateShippingFeeRepo:           allocateShippingFeeImpl,
		AllocateRankService:               allocateRankServiceImpl,
		BatchAllocateForecastService:      batchAllocateForecastImpl,
		BatchMinuteOrderConfService:       batchMinuteOrderConfServiceImpl,
	}
	maskingRuleVolumeFacade := &allocation3.MaskingRuleVolumeFacade{
		MaskRuleVolumeService: maskRuleVolumeServiceImpl,
	}
	productPriorityService := masking2.NewProductPriorityService(lpsApiImpl, priorityRepoImpl)
	productPriorityFacade := &adminfacade.ProductPriorityFacade{
		ProductPriorityService: productPriorityService,
	}
	scheduleVisualRepo := repository2.NewScheduleVisualRepo()
	scheduleVisualService := schedule_visual2.NewScheduleVisualService(scheduleVisualRepo, lpsApiImpl)
	scheduleVisualFacade := &adminfacade.ScheduleVisualFacade{
		ScheduleVisualService: scheduleVisualService,
	}
	maskingVolumeDashboardApiFacade := &volume_dashboard2.MaskingVolumeDashboardApiFacade{
		MaskingVolumeService: maskingVolumeServiceImpl,
	}
	routingVolumeRepoImpl := repository.NewRoutingVolumeRepoImpl()
	volumeChangeServiceImpl := volume_dashboard.NewVolumeChangeServiceImpl(routingConfigServiceImpl, softRuleRepoImpl)
	routingVolumeServiceImpl := volume_dashboard.NewRoutingVolumeServiceImpl(laneServiceImpl, lpsApiImpl, volumeCounterImpl, routingVolumeRepoImpl, exportTaskRepoImpl, volumeChangeServiceImpl)
	routingVolumeDashboardApiFacade := &volume_dashboard2.RoutingVolumeDashboardApiFacade{
		RoutingVolumeService: routingVolumeServiceImpl,
	}
	postCodeFacade := &adminfacade.PostCodeFacade{
		ZoneRepo: zoneRepoImpl,
	}
	listenerExecutorImpl := approval_listener.NewListenerExecutorImpl(businessAuditRepoImpl)
	businessAuditFacade := &adminfacade.BusinessAuditFacade{
		ListenerExecutor: listenerExecutorImpl,
	}
	availableLHFacade := &adminfacade.AvailableLHFacade{
		AvailableLHService: availableLHServiceImpl,
	}
	lhCapacityFacade := &adminfacade.LHCapacityFacade{
		LHCapacityService: lhCapacityServiceImpl,
	}
	ilhForecastTaskFacade := &adminfacade.ILHForecastTaskFacade{
		ILHForecastTaskService: ilhForecastTaskServiceImpl,
	}
	adminFacade := &adminfacade.AdminFacade{
		PingFacade:                      pingFacade,
		DebugFacade:                     debugFacade,
		VolumeRoutingFacade:             volumeRoutingFacade,
		SoftRoutingFacade:               softRoutingFacade,
		MaskingResultPanelFacade:        maskingResultPanelFacade,
		AllocPathFacade:                 maskingPathFacade,
		AllocRuleFacade:                 maskingRuleFacade,
		RoutingForecastFacade:           forecastFacade,
		CCRoutingRuleFacade:             ccRoutingFacade,
		RoutingVisual:                   routingVisual,
		MaskingForecastFacade:           maskingForecastFacade,
		MaskingRuleVolumeFacade:         maskingRuleVolumeFacade,
		ProductPriorityFacade:           productPriorityFacade,
		ScheduleVisualFacade:            scheduleVisualFacade,
		MaskingVolumeDashboardApiFacade: maskingVolumeDashboardApiFacade,
		RoutingVolumeDashboardApiFacade: routingVolumeDashboardApiFacade,
		PostCodeFacade:                  postCodeFacade,
		BusinessAuditFacade:             businessAuditFacade,
		AvailableLHFacade:               availableLHFacade,
		LHCapacityFacade:                lhCapacityFacade,
		ILHForecastTaskFacade:           ilhForecastTaskFacade,
	}
	return adminFacade, nil
}
