// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/auditclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/ccclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/chargeclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lcosclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lfsclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/llsclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lnpclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/locationclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lpsclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/spex_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/address"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/parcel_type_definition"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/pickup_priority"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rulevolume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/whitelist"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/available_lh/repo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/batch_allocate/allocation/order"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/business_audit"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/cc_routing_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/lane"
	repo2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/lh_capacity/repo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/locationzone"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/locationzone/zone"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/outercheck"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/productpriority"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/routing_config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/routing_log"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/rule_data"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/schedule_factor"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/schedule_visual"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/schedule_visual/schedule_stat"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volume_counter"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volumerouting/vrrepo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volumerouting/vrservice"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/grpcfacade"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/batch_allocate/service"
	parcel_type_definition2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/parcel_type_definition"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/pickup_efficiency_counter"
	rulevolume2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/rulevolume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/volumecounter"
	whitelist2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/whitelist"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/available_lh"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/business_audit/approval_manager"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/cc_routing"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/lh_capacity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/select_lane"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/smartrouting_debug"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/volume_counter_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/volumerouting"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/cacheutil/layercache"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/redisutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/redisutil/counter"
)

// Injectors from injector.go:

func InitGrpc() (*grpc_api.GrpcAPI, error) {
	lfsApiImpl := lfsclient.NewLfsApiImpl()
	llsApiImpl := llsclient.NewLlsApiImpl()
	laneServiceImpl := lane.NewLaneService(lfsApiImpl, llsApiImpl)
	chargeApiImpl := chargeclient.NewChargeApiImpl()
	lineCheapestShippingFeeFactor := schedule_factor.NewLineCheapestShippingFeeFactor(chargeApiImpl, laneServiceImpl)
	preCalFeeServiceImpl := routing.NewRoutingPreCalFeeServiceImpl(lineCheapestShippingFeeFactor)
	ilhWeightCounterImpl := volume_counter.NewILHWeightCounterImpl()
	routingServiceImpl := routing.NewRoutingServiceImpl(laneServiceImpl, preCalFeeServiceImpl, ilhWeightCounterImpl)
	locationZoneDaoImpl := zone.NewLocationZoneDaoImpl()
	lpsApiImpl := lpsclient.NewLpsApiImpl()
	levelCache, err := layercache.NewLayerCache()
	if err != nil {
		return nil, err
	}
	locationClientImpl := locationclient.NewLocationClientImpl(levelCache)
	addrRepoImpl := address.NewAddrRepoImpl(locationClientImpl)
	zoneRepoImpl := locationzone.NewZoneRepoImpl(locationZoneDaoImpl, lpsApiImpl, addrRepoImpl, levelCache)
	softRuleRepoImpl := ruledata.NewSoftRuleRepoImpl(lpsApiImpl)
	routingRuleRepoImpl := routing.NewRoutingRuleRepo(zoneRepoImpl, lpsApiImpl, laneServiceImpl, llsApiImpl, softRuleRepoImpl)
	routingConfigRepoImpl := ruledata.NewRoutingConfigRepoImpl(lpsApiImpl)
	routingConfigServiceImpl := routing_config.NewRoutingConfigServiceImpl(routingConfigRepoImpl, lpsApiImpl, softRuleRepoImpl, levelCache)
	dgFactor := schedule_factor.NewDgFactor()
	redisCounter := counter.NewRedisCounterImpl()
	volumeCounterImpl := volume_counter.NewVolumeCounterImpl(redisCounter, laneServiceImpl)
	minVolumeFactor := schedule_factor.NewMinVolumeFactor(volumeCounterImpl)
	parcelTypeDefinitionRepoImpl := parcel_type_definition.NewParcelTypeDefinitionRepoImpl()
	parcelTypeDefinitionServiceImpl := parcel_type_definition2.NewParcelTypeDefinitionServiceImpl(parcelTypeDefinitionRepoImpl, lpsApiImpl)
	maxCapacityFactor := schedule_factor.NewMaxCapacityFactor(volumeCounterImpl, parcelTypeDefinitionServiceImpl)
	minWeightFactor := schedule_factor.NewMinWeightFactor(volumeCounterImpl)
	maxWeightFactor := schedule_factor.NewMaxWeightFactor(volumeCounterImpl)
	linePriorityFactor := schedule_factor.NewLinePriorityFactor()
	defaultWeightageFactor := schedule_factor.NewDefaultWeightageFactor()
	defaultPriorityFactor := schedule_factor.NewDefaultPriorityFactor()
	zoneRuleRepoImpl := vrrepo.NewZoneRuleRepoImpl()
	zoneGroupRepoImpl := vrrepo.NewZoneGroupRepoImpl(zoneRuleRepoImpl, routingConfigRepoImpl)
	vrrepoZoneRepoImpl := vrrepo.NewZoneRepoImpl()
	taskRepoImpl := vrrepo.NewTaskRepoImpl()
	serviceImpl := vrservice.NewServiceImpl(zoneGroupRepoImpl, vrrepoZoneRepoImpl, zoneRuleRepoImpl, taskRepoImpl, addrRepoImpl, redisCounter)
	lnpApiImpl := lnpclient.NewLnpApiImpl()
	zoneRuleMgrImpl := volumerouting.NewZoneRuleMgrImpl(serviceImpl, zoneRuleRepoImpl, zoneGroupRepoImpl, vrrepoZoneRepoImpl, taskRepoImpl, lpsApiImpl, routingConfigRepoImpl, lnpApiImpl, parcelTypeDefinitionServiceImpl)
	minVolumeV2 := schedule_factor.NewMinVolumeV2(zoneRuleMgrImpl)
	maxVolumeV2 := schedule_factor.NewMaxVolumeV2(zoneRuleMgrImpl)
	ilhParcelMinVolumeFactor := schedule_factor.NewILHParcelMinVolumeFactor(volumeCounterImpl)
	ilhParcelMaxCapacityFactor := schedule_factor.NewILHParcelMaxCapacityFactor(volumeCounterImpl)
	combinationPriorityFactor := schedule_factor.NewCombinationPriorityFactor()
	factorSet := schedule_factor.NewFactorSet(dgFactor, minVolumeFactor, maxCapacityFactor, minWeightFactor, maxWeightFactor, linePriorityFactor, lineCheapestShippingFeeFactor, defaultWeightageFactor, defaultPriorityFactor, minVolumeV2, maxVolumeV2, ilhParcelMinVolumeFactor, ilhParcelMaxCapacityFactor, combinationPriorityFactor)
	routingLogRepoImpl := routing_log.NewRoutingLogRepoImpl()
	routingLogServiceImpl := routing_log.NewRoutingLogServiceImpl(volumeCounterImpl, laneServiceImpl, lpsApiImpl, routingLogRepoImpl)
	ccRoutingRuleRepoImpl := cc_routing_rule.NewCCRoutingRuleRepoImpl()
	ccApiImpl := ccclient.NewCCApiImpl()
	ccRoutingServiceImpl := cc_routing.NewCCRoutingServiceImpl(ccRoutingRuleRepoImpl, ccApiImpl, lpsApiImpl)
	availableLHRepoImpl := repo.NewAvailableLHRepoImpl()
	availableLHServiceImpl := available_lh.NewAvailableLHServiceImpl(availableLHRepoImpl)
	lhCapacityRepoImpl := repo2.NewLHCapacityRepoImpl()
	lhCapacityServiceImpl := lh_capacity.NewLHCapacityServiceImpl(lhCapacityRepoImpl, laneServiceImpl)
	ilhRoutingServiceImpl := routing.NewILHRoutingServiceImpl(ilhWeightCounterImpl)
	smartRoutingServiceImpl := select_lane.NewSmartRoutingServiceImpl(routingServiceImpl, routingRuleRepoImpl, lfsApiImpl, routingConfigServiceImpl, addrRepoImpl, factorSet, routingLogServiceImpl, laneServiceImpl, ccRoutingServiceImpl, availableLHServiceImpl, lhCapacityServiceImpl, ilhRoutingServiceImpl, parcelTypeDefinitionServiceImpl)
	volumeCounterService := volume_counter_service.NewVolumeCounterService(zoneRuleRepoImpl, zoneGroupRepoImpl, volumeCounterImpl, vrrepoZoneRepoImpl, routingConfigServiceImpl, zoneRepoImpl, laneServiceImpl)
	maskVolumeCounterImpl := volumecounter.NewMaskVolumeCounterImpl()
	shopWhitelistRepoImpl := whitelist.NewShopWhitelistRepoImpl()
	shopWhitelistServiceImpl := whitelist2.NewShopWhitelistServiceImpl(shopWhitelistRepoImpl)
	softRuleService := allocation.NewSoftRuleService(maskVolumeCounterImpl, chargeApiImpl, shopWhitelistServiceImpl)
	iMaskRuleVolumeRepo := rulevolume.NewMaskRuleVolumeRepoImpl(lpsApiImpl)
	checkVolumeFinderImpl := rulevolume2.NewCheckVolumeFinderImpl(iMaskRuleVolumeRepo)
	auditApiImpl := auditclient.NewAuditApiImpl()
	businessAuditRepoImpl := business_audit.NewBusinessAuditRepoImpl()
	approvalExecutorImpl := approval_manager.NewApprovalExecutorImpl(auditApiImpl, businessAuditRepoImpl)
	maskRuleVolumeServiceImpl := rulevolume2.NewMaskRuleVolumeServiceImpl(addrRepoImpl, lpsApiImpl, iMaskRuleVolumeRepo, levelCache, checkVolumeFinderImpl, approvalExecutorImpl, businessAuditRepoImpl)
	allocationRuleImpl := rule.NewAllocationRuleImpl(lpsApiImpl)
	allocationConfigImpl := config.NewAllocationConfigImpl(lpsApiImpl)
	maskConfigRepoImpl := config.NewMaskConfigRepo(allocationRuleImpl, allocationConfigImpl)
	maskRuleConfRepo := rule.NewMaskRuleConfRepo()
	priorityRepoImpl := productpriority.NewPriorityRepoImpl(lpsApiImpl)
	pickupPriorityRepoImpl := pickup_priority.NewPickupPriorityRepoImpl()
	maskRuleRepoImpl := rule.NewRuleRepo(iMaskRuleVolumeRepo, maskRuleVolumeServiceImpl, maskRuleConfRepo, lpsApiImpl, allocationRuleImpl, priorityRepoImpl, approvalExecutorImpl, businessAuditRepoImpl, pickupPriorityRepoImpl)
	priorityBusinessImpl := productpriority.NewPriorityBusinessImpl(priorityRepoImpl, lpsApiImpl)
	allOuterCheckServiceImpl := outercheck.NewAllOuterCheckServiceImpl(priorityBusinessImpl)
	client, err := redisutil.Client()
	if err != nil {
		return nil, err
	}
	maskingScheduleVisualStat := schedule_stat.NewMaskingScheduleVisualStat(client)
	maskingScheduleVisualStatV2 := schedule_stat.NewMaskingScheduleVisualStatV2(client)
	maskingForecastScheduleVisualStat := schedule_stat.NewMaskingForecastScheduleVisualStat(client)
	scheduleVisualSet := schedule_stat.NewScheduleVisualSet(maskingScheduleVisualStat, maskingScheduleVisualStatV2, maskingForecastScheduleVisualStat)
	scheduleCountStat := schedule_visual.NewScheduleCountStat(scheduleVisualSet)
	batchAllocateOrderRepoImpl := order.NewBatchAllocateOrderRepo()
	greyServiceImpl := service.NewGreyServiceImpl()
	pickupEffCounterImpl := pickup_efficiency_counter.NewPickupEffCounterImpl()
	spexServiceImpl := spex_service.NewSpexServiceImpl()
	lcosApiImpl := lcosclient.NewLcosApiImpl()
	allocationServiceImpl := allocation.NewAllocationService(softRuleService, maskRuleVolumeServiceImpl, maskConfigRepoImpl, maskRuleRepoImpl, allOuterCheckServiceImpl, maskVolumeCounterImpl, volumeCounterImpl, scheduleCountStat, iMaskRuleVolumeRepo, batchAllocateOrderRepoImpl, greyServiceImpl, parcelTypeDefinitionServiceImpl, pickupEffCounterImpl, spexServiceImpl, lcosApiImpl, lpsApiImpl, llsApiImpl, addrRepoImpl)
	volumeRoutingServer := grpc_api.NewVolumeRoutingServer(zoneRuleMgrImpl, volumeCounterImpl, laneServiceImpl, volumeCounterService, allocationServiceImpl, lfsApiImpl, ilhWeightCounterImpl, lhCapacityServiceImpl)
	smartRoutingDebug := smartrouting_debug.NewSmartRoutingDebug()
	smartRoutingDebugServer := grpc_api.NewSmartRoutingDebugServer(smartRoutingDebug)
	grpcAPI := &grpc_api.GrpcAPI{
		SmartRoutingServer:      smartRoutingServiceImpl,
		VolumeRoutingService:    volumeRoutingServer,
		MaskAllocateServer:      allocationServiceImpl,
		SmartRoutingDebugServer: smartRoutingDebugServer,
	}
	return grpcAPI, nil
}
