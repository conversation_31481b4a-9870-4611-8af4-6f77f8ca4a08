## CC Routing Admin API 文档

- 基础路径: `/api/admin/cc_routing`
- 版本: v1
- 更新时间: 自动生成于当前开发分支

### 总览

- POST `/rule/create`: 创建CC路由规则
- POST `/rule/update`: 更新CC路由规则
- GET `/rule/view`: 查看CC路由规则详情
- POST `/rule/delete`: 删除CC路由规则
- GET `/rule/list`: 获取CC路由规则列表
- POST `/rule/import`: 统一批量导入（3:shop_group/4:category/5:weight_category）
- GET `/rule/template`: 下载 Excel 模板（带下拉）
- GET `/cc_list`: 获取可选 CC 列表（Allocate to 下拉）

---

### 1) 批量导入规则

- 方法与路径: `POST /rule/import`
- 功能: 统一批量导入接口，支持三种类型：`3` (shop_group) | `4` (category) | `5` (weight_category)
- 请求
  - Content-Type: `multipart/form-data`
  - 表单字段
    - `file` 必填，.xlsx 或 .csv，最大 10MB
    - `validate_only` 可选，布尔字符串 `true`/`false`（默认 `false`）。`true` 表示仅校验不落库
  - 兼容 JSON 字段（当表单未提供时作为兜底解析），对应 schema `ImportRuleReq`：
    - `product_id` int required
    - `routing_type` int required，枚举值: `3` (shop_group) | `4` (category) | `5` (weight_category)
    - `validate_only` bool optional
- 响应
  - 200 application/json
    - `message` string
    - `product_id` int
    - `rule_type` int
    - `validate_only` bool
  - 非 200: 统一错误结构（`code`/`message`）

示例（curl）

```bash
curl -X POST 'https://<host>/api/admin/cc_routing/rule/import' \
  -F 'file=@shop_group_template.xlsx' \
  -F 'validate_only=false' \
  -F 'product_id=8' \
  -F 'routing_type=3'
```

成功响应示例

```json
{
  "message": "shop group rules imported successfully",
  "product_id": 8,
  "rule_type": "shop_group",
  "validate_only": false
}
```

仅校验成功示例

```json
{
  "message": "shop group rules validation passed",
  "product_id": 8,
  "rule_type": "shop_group",
  "validate_only": true
}
```

请求 JSON schema（兜底解析）

```json
// ImportRuleReq
{
  "product_id": 8,
  "routing_type": 3, // 枚举值: 3 (shop_group) | 4 (category) | 5 (weight_category)
  "validate_only": false
}
```

---

### 2) 下载模板

- 方法与路径: `GET /rule/template`
- 功能: 下载 Excel 模板（内置 Allocate to 下拉，Default 行红色样式）
- 请求
  - Query 参数（`DownloadTemplateReq`）
    - `routing_type` int required，枚举值: `3` (shop_group) | `4` (category) | `5` (weight_category)
- 响应
  - 200 文件下载
    - Content-Type: `application/vnd.openxmlformats-officedocument.spreadsheetml.sheet`
    - Content-Disposition: `attachment; filename="<type>_template.xlsx"`
  - 文件名
    - `shop_group_template.xlsx`
    - `category_template.xlsx`
    - `weight_category_template.xlsx`

示例

```bash
curl -X GET 'https://<host>/api/admin/cc_routing/rule/template?routing_type=4' -O
```

模板字段规范

- Shop Group 模板
  - 列: `Client Group` | `Allocate to`
  - 第一行必须为 `Default`（红色）
- Category 模板
  - 列: `Global Category ID` | `Allocate to`
  - 第一行必须为 `Default`（红色）
- Category + Weight 模板
  - 列: `Category ID` | `Weight Min (Not included)` | `Weight Max (Included)` | `Allocate to`
  - 第一行必须为 `Default`（红色）
  - 同一 Category 下重量区间需连续且不重不漏

---

### 3) 创建 CC 路由规则

- 方法与路径: `POST /rule/create`
- 功能: 创建新的 CC 路由规则（支持 Fixed、Weight、Shop Group、Category 和 Weight+Category 类型）
- 请求 (`CreateCCRoutingRuleReq`)
  - Content-Type: `application/json`
  - 字段:
    - `product_id` int required: 产品ID
    - `routing_type` int required: 路由类型枚举值，`1` (Fixed) | `2` (Weight) | `3` (Shop Group) | `4` (Category) | `5` (Weight+Category)
    - `rule_detail` object required: 根据 `routing_type` 提供不同结构的详情
- 响应
  - 200 application/json: `{"id": <rule_id>}`
  - 非 200: 统一错误结构（`code`/`message`）

示例请求 (Shop Group 类型):

```json
{
  "product_id": 8,
  "routing_type": 3,
  "rule_detail": {
    "shop_group_rule_detail": {
      "rules": [
        {
          "client_tag_id": 8,
          "client_group_id": "G123",
          "customs_clearance": "CC_SG1"
        }
      ],
      "default_customs_clearance": "CC_DEFAULT"
    }
  }
}
```

示例请求 (Category 类型):

```json
{
  "product_id": 8,
  "routing_type": 4,
  "rule_detail": {
    "category_rule_detail": {
      "rules": [
        {
          "category_id": 12345,
          "customs_clearance": "CC_CAT1"
        }
      ],
      "default_customs_clearance": "CC_DEFAULT"
    }
  }
}
```

示例请求 (Weight+Category 类型):

```json
{
  "product_id": 8,
  "routing_type": 5,
  "rule_detail": {
    "weight_category_rule_detail": {
      "rules": [
        {
          "category_id": 12345,
          "min_weight": 0,
          "max_weight": 400,
          "customs_clearance": "CC_CAT_W1"
        },
        {
          "category_id": 12345,
          "min_weight": 400,
          "max_weight": 1000,
          "customs_clearance": "CC_CAT_W2"
        }
      ],
      "default_customs_clearance": "CC_DEFAULT"
    }
  }
}
```

---

### 4) 更新 CC 路由规则

- 方法与路径: `POST /rule/update`
- 功能: 更新现有的 CC 路由规则
- 请求 (`UpdateCCRoutingRuleReq`)
  - Content-Type: `application/json`
  - 字段:
    - `id` int required: 规则ID
    - `routing_type` int required: 路由类型枚举值，`1` (Fixed) | `2` (Weight) | `3` (Shop Group) | `4` (Category) | `5` (Weight+Category)
    - `rule_detail` object required: 根据 `routing_type` 提供不同结构的详情
- 响应
  - 200 application/json: `{}`
  - 非 200: 统一错误结构（`code`/`message`）

---

### 5) 查看 CC 路由规则

- 方法与路径: `GET /rule/view`
- 功能: 获取单个 CC 路由规则的详细信息
- 请求 (`GetCCRoutingRuleReq`)
  - Query 参数:
    - `id` int required: 规则ID
- 响应
  - 200 application/json: 完整的规则对象，包含 `id`、`product_id`、`routing_type` 和 `rule_detail`
  - 非 200: 统一错误结构（`code`/`message`）

响应示例:

```json
{
  "id": 42,
  "product_id": 8,
  "routing_type": 3,
  "rule_detail": {
    "shop_group_rule_detail": {
      "rules": [
        {
          "client_tag_id": 8,
          "client_group_id": "G123",
          "customs_clearance": "CC_SG1"
        }
      ],
      "default_customs_clearance": "CC_DEFAULT"
    }
  }
}
```

---

### 6) 删除 CC 路由规则

- 方法与路径: `POST /rule/delete`
- 功能: 删除指定的 CC 路由规则
- 请求 (`DeleteCCRoutingRuleReq`)
  - Content-Type: `application/json`
  - 字段:
    - `id` int required: 规则ID
- 响应
  - 200 application/json: `{}`
  - 非 200: 统一错误结构（`code`/`message`）

---

### 7) 获取 CC 路由规则列表

- 方法与路径: `GET /rule/list`
- 功能: 分页查询 CC 路由规则列表
- 请求 (`ListCCRoutingRulesReq`)
  - Query 参数:
    - `product_id` int optional: 按产品ID过滤
    - `offset` int64 optional: 分页偏移量，默认0
    - `size` int64 required: 每页条数
- 响应 (`ListCCRoutingRulesRsp`)
  - 200 application/json:
    - `list` array: 规则对象数组
    - `offset` int64: 当前偏移量
    - `total` int64: 总记录数
    - `size` int64: 每页条数
  - 非 200: 统一错误结构（`code`/`message`）

---

### 8) 获取 CC 下拉列表

- 方法与路径: `GET /cc_list`
- 功能: 获取 `Allocate to` 列可选 CC 列表
- 请求: 无
- 响应（`CCListResp`）

```json
{
  "list": [
    { "id": "CC_SG1", "name": "Singapore Customs" },
    { "id": "CC_TW2", "name": "Taiwan Customs #2" }
  ]
}
```

---

### Schema 参考（代码位置）

- `internal/schema/cc_routing.go`
  - `CreateCCRoutingRuleReq`
  - `UpdateCCRoutingRuleReq`
  - `GetCCRoutingRuleReq`
  - `DeleteCCRoutingRuleReq`
  - `ListCCRoutingRulesReq`
  - `ListCCRoutingRulesRsp`
  - `ImportRuleReq`
  - `DownloadTemplateReq`
  - `CCListResp`

---

### 错误码说明（示例）

- `ParamErr`: 参数错误，例如文件缺失、类型不支持、模板字段为空等
- `FormatErr`: 文件解析失败
- `CCRoutingFailed`: 路由/配置校验失败

---

### 备注

- `validate_only=true` 时仅进行格式与强校验，不会落库；`false` 时成功则生效并刷新缓存（若有）
- 模板中的 `Allocate to` 下拉来自 `/cc_list` 实时获取的可用 CC 列表


