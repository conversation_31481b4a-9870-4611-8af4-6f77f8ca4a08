package adminfacade

import (
	"context"
	"fmt"
	"io/ioutil"
	"strconv"
	"strings"

	"git.garena.com/shopee/bg-logistics/go/chassis/protocol/server/restful"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/cc_routing_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/cc_routing"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/apiutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/mockutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
)

type CCRoutingFacade struct {
	CCRoutingSrv cc_routing.CCRoutingService
}

// ImportRuleFunc 导入规则函数类型
type ImportRuleFunc func(ctx context.Context, productId int, fileData []byte, operator string, validateOnly bool) *srerr.Error

func (p *CCRoutingFacade) URLPatterns() []restful.Route {
	ccRouting := restful.NewRouterGroup("/api/admin/cc_routing")
	ccRouting.POST("/rule/create", p.CreateRule)
	ccRouting.POST("/rule/update", p.UpdateRule)
	ccRouting.GET("/rule/view", p.GetRule)
	ccRouting.POST("/rule/delete", p.DeleteRule)
	ccRouting.GET("/rule/list", p.ListRule)

	// 批量导入和模板下载
	ccRouting.POST("/rule/import", p.ImportRules)
	ccRouting.GET("/rule/template", p.DownloadTemplate)
	ccRouting.GET("/cc_list", p.GetCCList)

	return ccRouting.GetRouters()
}

func (p *CCRoutingFacade) CreateRule(ctx *restful.Context) {
	req := new(schema.CreateCCRoutingRuleReq)
	if err := apiutil.SchemaParseValidate(ctx, req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	// 对特定路由类型进行验证
	if err := p.validateRuleDetailByType(req.RoutingType, &req.RuleDetail); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	operator, _ := apiutil.GetUserInfo(ctx.Ctx)
	rule := &cc_routing_rule.CCRoutingRule{
		ProductId:   req.ProductId,
		RoutingType: req.RoutingType,
		RuleDetail:  req.RuleDetail,
	}
	ruleId, err := p.CCRoutingSrv.CreateCCRoutingRule(mockutil.MockCtx(ctx), rule, operator)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, map[string]interface{}{"id": ruleId})
}

func (p *CCRoutingFacade) UpdateRule(ctx *restful.Context) {
	req := new(schema.UpdateCCRoutingRuleReq)
	if err := apiutil.SchemaParseValidate(ctx, req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	// 对特定路由类型进行验证
	if err := p.validateRuleDetailByType(req.RoutingType, &req.RuleDetail); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	operator, _ := apiutil.GetUserInfo(ctx.Ctx)
	rule := &cc_routing_rule.CCRoutingRule{
		Id:          req.Id,
		RoutingType: req.RoutingType,
		RuleDetail:  req.RuleDetail,
	}
	if err := p.CCRoutingSrv.UpdateCCRoutingRule(mockutil.MockCtx(ctx), rule, operator); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, nil)
}

func (p *CCRoutingFacade) GetRule(ctx *restful.Context) {
	req := new(schema.GetCCRoutingRuleReq)
	if err := apiutil.SchemaParseValidate(ctx, req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	ret, err := p.CCRoutingSrv.GetCCRoutingRuleById(mockutil.MockCtx(ctx), req.Id)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, ret)
}

func (p *CCRoutingFacade) ListRule(ctx *restful.Context) {
	req := new(schema.ListCCRoutingRulesReq)
	if err := apiutil.SchemaParseValidate(ctx, req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	condition := make(map[string]interface{})
	if req.ProductId != 0 {
		condition["product_id"] = req.ProductId
	}
	list, count, err := p.CCRoutingSrv.ListCCRoutingRule(mockutil.MockCtx(ctx), condition, req.Offset, req.Size)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	ret := schema.ListCCRoutingRulesRsp{
		List:   list,
		Offset: req.Offset,
		Total:  count,
		Size:   req.Size,
	}

	apiutil.SuccessJSONResp(ctx, ret)
}

func (p *CCRoutingFacade) DeleteRule(ctx *restful.Context) {
	req := new(schema.DeleteCCRoutingRuleReq)
	if err := apiutil.SchemaParseValidate(ctx, req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	if err := p.CCRoutingSrv.DeleteCCRoutingRuleById(mockutil.MockCtx(ctx), req.Id); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, nil)
}

// ImportRules 批量导入规则
func (p *CCRoutingFacade) ImportRules(ctx *restful.Context) {
	req := new(schema.ImportRuleReq)
	if err := apiutil.SchemaParseValidate(ctx, req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	// 根据类型选择对应的导入函数
	var importFunc ImportRuleFunc

	switch req.RoutingType {
	case cc_routing_rule.CCRoutingTypeShopGroup: // 3: shop_group
		importFunc = p.CCRoutingSrv.ImportShopGroupRules
	case cc_routing_rule.CCRoutingTypeCategory: // 4: category
		importFunc = p.CCRoutingSrv.ImportCategoryRules
	case cc_routing_rule.CCRoutingTypeWeightCategory: // 5: weight_category
		importFunc = p.CCRoutingSrv.ImportWeightCategoryRules
	default:
		apiutil.FailJSONResp(ctx, srerr.New(srerr.ParamErr, nil, "unsupported routing type: %d", req.RoutingType), "")
		return
	}

	p.handleImportRulesUnified(ctx, req, importFunc)
}

// DownloadTemplate 下载动态模板
func (p *CCRoutingFacade) DownloadTemplate(ctx *restful.Context) {
	req := new(schema.DownloadTemplateReq)
	if err := apiutil.SchemaParseValidate(ctx, req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	// 直接传递路由类型枚举值给服务层
	content, filename, err := p.CCRoutingSrv.GenerateTemplate(mockutil.MockCtx(ctx), req.RoutingType)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	// 设置Excel下载响应头
	ctx.AddHeader("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	ctx.AddHeader("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s\"", filename))
	ctx.AddHeader("Cache-Control", "no-cache")
	ctx.AddHeader("Pragma", "no-cache")
	ctx.Write(content)
}

// GetCCList 获取可用CC列表
func (p *CCRoutingFacade) GetCCList(ctx *restful.Context) {
	list, err := p.CCRoutingSrv.GetCCList(mockutil.MockCtx(ctx))
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	// 直接返回列表，不包装在结构体中
	apiutil.SuccessJSONResp(ctx, map[string]interface{}{
		"list": list,
	})
}

// handleImportRulesUnified 统一的导入规则处理方法
func (p *CCRoutingFacade) handleImportRulesUnified(ctx *restful.Context, req *schema.ImportRuleReq, importFunc ImportRuleFunc) {
	// 解析multipart文件
	if err := ctx.ReadRequest().ParseMultipartForm(32 * (1 << 20)); err != nil { // 32MB limit
		logger.CtxLogErrorf(ctx.Ctx, "Failed to parse multipart form for routing type %d import: %v", req.RoutingType, err)
		apiutil.FailJSONResp(ctx, srerr.New(srerr.ParamErr, nil, "failed to parse form data"), err.Error())
		return
	}

	file, fileHeader, fErr := ctx.ReadRequest().FormFile("file")
	if fErr != nil {
		logger.CtxLogErrorf(ctx.Ctx, "Failed to get file from form for routing type %d import: %v", req.RoutingType, fErr)
		apiutil.FailJSONResp(ctx, srerr.New(srerr.ParamErr, nil, "file is required"), fErr.Error())
		return
	}
	defer file.Close()

	// 检查文件大小限制(10MB)
	if fileHeader.Size > 10*(1<<20) {
		logger.CtxLogErrorf(ctx.Ctx, "File size too large for routing type %d import: %d bytes", req.RoutingType, fileHeader.Size)
		apiutil.FailJSONResp(ctx, srerr.New(srerr.ParamErr, nil, "file size exceeds 10MB limit"), "")
		return
	}

	// 检查文件类型
	filename := fileHeader.Filename
	if !strings.HasSuffix(strings.ToLower(filename), ".csv") && !strings.HasSuffix(strings.ToLower(filename), ".xlsx") {
		logger.CtxLogErrorf(ctx.Ctx, "Invalid file type for routing type %d import: %s", req.RoutingType, filename)
		apiutil.FailJSONResp(ctx, srerr.New(srerr.ParamErr, nil, "only CSV and Excel files are supported"), "")
		return
	}

	data, readErr := ioutil.ReadAll(file)
	if readErr != nil {
		logger.CtxLogErrorf(ctx.Ctx, "Failed to read file data for routing type %d import: %v", req.RoutingType, readErr)
		apiutil.FailJSONResp(ctx, srerr.New(srerr.ParamErr, nil, "failed to read file"), readErr.Error())
		return
	}

	// 解析参数，优先使用表单参数
	validateOnly := p.parseBoolForm(ctx, "validate_only")

	// 如果表单没有提供，则使用JSON参数作为fallback
	if !validateOnly && req.ValidateOnly {
		validateOnly = req.ValidateOnly
	}

	operator, _ := apiutil.GetUserInfo(ctx.Ctx)

	logger.CtxLogInfof(ctx.Ctx, "Starting routing type %d import for product %d, operator: %s, validateCC: %v, validateOnly: %v, fileSize: %d",
		req.RoutingType, req.ProductId, operator, true, validateOnly, len(data))

	if err := importFunc(mockutil.MockCtx(ctx), req.ProductId, data, operator, validateOnly); err != nil {
		logger.CtxLogErrorf(ctx.Ctx, "Failed to import routing type %d rules for product %d: %v", req.RoutingType, req.ProductId, err)
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	logger.CtxLogInfof(ctx.Ctx, "Successfully imported routing type %d rules for product %d", req.RoutingType, req.ProductId)

	// 根据枚举获取对应的规则类型名称
	var ruleTypeName string
	switch req.RoutingType {
	case cc_routing_rule.CCRoutingTypeShopGroup:
		ruleTypeName = "shop group"
	case cc_routing_rule.CCRoutingTypeCategory:
		ruleTypeName = "category"
	case cc_routing_rule.CCRoutingTypeWeightCategory:
		ruleTypeName = "weight category"
	default:
		ruleTypeName = fmt.Sprintf("type %d", req.RoutingType)
	}

	respMessage := fmt.Sprintf("%s rules imported successfully", ruleTypeName)
	if validateOnly {
		respMessage = fmt.Sprintf("%s rules validation passed", ruleTypeName)
	}

	apiutil.SuccessJSONResp(ctx, map[string]interface{}{
		"message":       respMessage,
		"product_id":    req.ProductId,
		"routing_type":  req.RoutingType,
		"validate_only": validateOnly,
	})
}

// parseBoolForm 从表单解析布尔值
func (p *CCRoutingFacade) parseBoolForm(ctx *restful.Context, key string) bool {
	val := ctx.ReadRequest().FormValue(key)
	if val == "" {
		return false
	}
	b, _ := strconv.ParseBool(val)
	return b
}

// validateRuleDetailByType 根据路由类型验证规则细节
func (p *CCRoutingFacade) validateRuleDetailByType(routingType cc_routing_rule.CCRoutingType, ruleDetail *cc_routing_rule.CCRoutingRuleDetail) *srerr.Error {
	switch routingType {
	case cc_routing_rule.CCRoutingTypeFixed:
		return p.validateFixedRuleDetail(&ruleDetail.FixedRuleDetail)
	case cc_routing_rule.CCRoutingTypeWeight:
		return p.validateWeightRuleDetail(&ruleDetail.WeightRuleDetail)
	case cc_routing_rule.CCRoutingTypeShopGroup:
		return p.validateShopGroupRuleDetail(&ruleDetail.ShopGroupRuleDetail)
	case cc_routing_rule.CCRoutingTypeCategory:
		return p.validateCategoryRuleDetail(&ruleDetail.CategoryRuleDetail)
	case cc_routing_rule.CCRoutingTypeWeightCategory:
		return p.validateWeightCategoryRuleDetail(&ruleDetail.WeightCategoryRuleDetail)
	default:
		return srerr.New(srerr.ParamErr, nil, "unsupported routing type: %d", routingType)
	}
}

// validateFixedRuleDetail 验证固定路由类型规则
func (p *CCRoutingFacade) validateFixedRuleDetail(detail *cc_routing_rule.FixedRuleDetail) *srerr.Error {
	if detail.FixedCustomsClearance == "" {
		return srerr.New(srerr.ParamErr, nil, "fixed_customs_clearance cannot be empty for routing type 'Fixed'")
	}
	return nil
}

// validateWeightRuleDetail 验证重量路由类型规则
func (p *CCRoutingFacade) validateWeightRuleDetail(detail *cc_routing_rule.WeightRuleDetail) *srerr.Error {
	if len(detail.RuleList) == 0 {
		return srerr.New(srerr.ParamErr, nil, "rule_list cannot be empty for routing type 'Weight'")
	}

	// 验证重量区间
	for i, rule := range detail.RuleList {
		if rule.MinWeight >= rule.MaxWeight {
			return srerr.New(srerr.ParamErr, nil, "invalid weight range: min_weight must be less than max_weight at index %d", i)
		}
		if rule.CustomsClearance == "" {
			return srerr.New(srerr.ParamErr, nil, "customs_clearance cannot be empty at index %d", i)
		}
	}

	return nil
}

// validateShopGroupRuleDetail 验证Shop Group类型规则
func (p *CCRoutingFacade) validateShopGroupRuleDetail(detail *cc_routing_rule.ShopGroupRuleDetail) *srerr.Error {
	if detail.DefaultCustomsClearance == "" {
		return srerr.New(srerr.ParamErr, nil, "default_customs_clearance cannot be empty for routing type 'Shop Group'")
	}

	// 验证规则列表
	if len(detail.Rules) == 0 {
		return srerr.New(srerr.ParamErr, nil, "rules cannot be empty for routing type 'Shop Group'")
	}

	// 验证Shop Group规则项
	clientGroupMap := make(map[string]bool)
	for i, rule := range detail.Rules {
		if rule.ClientTagId != 8 {
			return srerr.New(srerr.ParamErr, nil, "client_tag_id must be 8 (CB CC Allocation) at index %d", i)
		}
		if rule.ClientGroupId == "" {
			return srerr.New(srerr.ParamErr, nil, "client_group_id cannot be empty at index %d", i)
		}
		if rule.CustomsClearance == "" {
			return srerr.New(srerr.ParamErr, nil, "customs_clearance cannot be empty at index %d", i)
		}

		// 检查重复的ClientGroupId
		if clientGroupMap[rule.ClientGroupId] {
			return srerr.New(srerr.ParamErr, nil, "duplicate client_group_id '%s' at index %d", rule.ClientGroupId, i)
		}
		clientGroupMap[rule.ClientGroupId] = true
	}

	return nil
}

// validateCategoryRuleDetail 验证Category类型规则
func (p *CCRoutingFacade) validateCategoryRuleDetail(detail *cc_routing_rule.CategoryRuleDetail) *srerr.Error {
	if detail.DefaultCustomsClearance == "" {
		return srerr.New(srerr.ParamErr, nil, "default_customs_clearance cannot be empty for routing type 'Category'")
	}

	// 验证规则列表
	if len(detail.Rules) == 0 {
		return srerr.New(srerr.ParamErr, nil, "rules cannot be empty for routing type 'Category'")
	}

	// 验证Category规则项
	categoryMap := make(map[int]bool)
	for i, rule := range detail.Rules {
		if rule.CategoryId <= 0 {
			return srerr.New(srerr.ParamErr, nil, "category_id must be positive at index %d", i)
		}
		if rule.CustomsClearance == "" {
			return srerr.New(srerr.ParamErr, nil, "customs_clearance cannot be empty at index %d", i)
		}

		// 检查重复的CategoryId
		if categoryMap[rule.CategoryId] {
			return srerr.New(srerr.ParamErr, nil, "duplicate category_id '%d' at index %d", rule.CategoryId, i)
		}
		categoryMap[rule.CategoryId] = true
	}

	return nil
}

// validateWeightCategoryRuleDetail 验证Weight+Category类型规则
func (p *CCRoutingFacade) validateWeightCategoryRuleDetail(detail *cc_routing_rule.WeightCategoryRuleDetail) *srerr.Error {
	if detail.DefaultCustomsClearance == "" {
		return srerr.New(srerr.ParamErr, nil, "default_customs_clearance cannot be empty for routing type 'Weight+Category'")
	}

	// 验证规则列表
	if len(detail.Rules) == 0 {
		return srerr.New(srerr.ParamErr, nil, "rules cannot be empty for routing type 'Weight+Category'")
	}

	// 按CategoryId分组
	categoryRules := make(map[int][]cc_routing_rule.WeightCategoryRuleItem)
	for _, rule := range detail.Rules {
		categoryRules[rule.CategoryId] = append(categoryRules[rule.CategoryId], rule)
	}

	// 验证每个类目的重量区间
	for categoryId, rules := range categoryRules {
		if err := validateWeightRanges(categoryId, rules); err != nil {
			return err
		}
	}

	return nil
}

// validateWeightRanges 验证重量区间的连续性和非重叠性
func validateWeightRanges(categoryId int, rules []cc_routing_rule.WeightCategoryRuleItem) *srerr.Error {
	if len(rules) == 0 {
		return nil
	}

	// 按MinWeight排序
	sortedRules := make([]cc_routing_rule.WeightCategoryRuleItem, len(rules))
	copy(sortedRules, rules)

	for i := 0; i < len(sortedRules); i++ {
		for j := i + 1; j < len(sortedRules); j++ {
			if sortedRules[i].MinWeight > sortedRules[j].MinWeight {
				sortedRules[i], sortedRules[j] = sortedRules[j], sortedRules[i]
			}
		}
	}

	// 检查基本有效性
	for i, rule := range sortedRules {
		if rule.MinWeight >= rule.MaxWeight {
			return srerr.New(srerr.ParamErr, nil,
				"category %d: invalid weight range at index %d: min_weight(%d) must be less than max_weight(%d)",
				categoryId, i, rule.MinWeight, rule.MaxWeight)
		}

		if rule.CustomsClearance == "" {
			return srerr.New(srerr.ParamErr, nil,
				"category %d: customs_clearance cannot be empty at index %d",
				categoryId, i)
		}
	}

	// 检查相邻区间是否连续且不重叠
	for i := 0; i < len(sortedRules)-1; i++ {
		curr := sortedRules[i]
		next := sortedRules[i+1]

		// 检查重叠
		if curr.MaxWeight > next.MinWeight {
			return srerr.New(srerr.ParamErr, nil,
				"category %d: weight range overlapped - range [%d,%d] overlaps with [%d,%d]",
				categoryId, curr.MinWeight, curr.MaxWeight, next.MinWeight, next.MaxWeight)
		}

		// 检查间隙
		if curr.MaxWeight < next.MinWeight {
			return srerr.New(srerr.ParamErr, nil,
				"category %d: weight range incomplete - gap between [%d,%d] and [%d,%d]. Missing range: (%d,%d)",
				categoryId, curr.MinWeight, curr.MaxWeight, next.MinWeight, next.MaxWeight,
				curr.MaxWeight, next.MinWeight)
		}
	}

	return nil
}
