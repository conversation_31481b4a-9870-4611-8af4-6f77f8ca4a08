package ccclient

import (
	"context"
	"fmt"

	jsoniter "github.com/json-iterator/go"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/httputil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/jwtutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/envvar"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
)

type CCApi interface {
	GetDeclarationResult(ctx context.Context, orderSn string) (string, bool, *srerr.Error)
	ListCustomsAgents(ctx context.Context) ([]*CustomsAgent, *srerr.Error)
}

type CCApiImpl struct {
}

func NewCCApiImpl() *CCApiImpl {
	return &CCApiImpl{}
}

func (c *CCApiImpl) GetDeclarationResult(ctx context.Context, orderSn string) (string, bool, *srerr.Error) {
	// return
	// customs clearance result, if has pre auth(if false, needn't cc filter), error

	header, err := c.buildCCHeader(ctx)
	if err != nil {
		return "", false, err
	}

	conf := configutil.GetCustomsServiceConf(ctx)
	host := fmt.Sprintf("%s.%s", conf.Host, constant.ShopeeDomainSuffix[envvar.GetCID()])
	url := objutil.Merge(host, GetDeclarationResultPath)

	req := GetDeclarationResultRequest{
		TicketNumber: orderSn,
		BizType:      BizTypePreAuth,
		DeclareType:  DeclareTypeImport,
	}
	body, jErr := jsoniter.Marshal(req)
	if jErr != nil {
		return "", false, err
	}

	data, gErr := httputil.PostJson(ctx, url, body, int(conf.Timeout), header)
	if gErr != nil {
		return "", false, srerr.With(srerr.CustomsServiceErr, body, gErr)
	}

	var rsp GetDeclarationResultResponse
	if err := jsoniter.Unmarshal(data, &rsp); err != nil {
		return "", false, srerr.With(srerr.FormatErr, nil, err)
	}

	if rsp.Retcode == HasNotPreAuth {
		return "", false, nil
	}

	if rsp.Retcode != 0 {
		return "", false, srerr.New(srerr.CustomsServiceErr, nil, "invalid response:%s", objutil.JsonString(rsp))
	}

	// can get result
	for _, result := range rsp.Data.List {
		if result.DeclareType == DeclareTypeImport {
			return result.CustomsAgentId, true, nil
		}
	}

	return "", false, srerr.New(srerr.CustomsServiceErr, orderSn, "order has no cc pre auth result")
}

// ListCustomsAgents 获取可配置的CC列表，供Admin下拉使用
func (c *CCApiImpl) ListCustomsAgents(ctx context.Context) ([]*CustomsAgent, *srerr.Error) {
	header, err := c.buildCCHeader(ctx)
	if err != nil {
		return nil, err
	}

	conf := configutil.GetCustomsServiceConf(ctx)
	host := fmt.Sprintf("%s.%s", conf.Host, constant.ShopeeDomainSuffix[envvar.GetCID()])
	url := objutil.Merge(host, ListCustomsAgentsPath)

	req := ListCustomsAgentsRequest{
		Region:      envvar.GetCID(),
		DeclareType: DeclareTypeImport,
	}

	body, jErr := jsoniter.Marshal(req)
	if jErr != nil {
		return nil, srerr.With(srerr.FormatErr, nil, jErr)
	}

	data, gErr := httputil.PostJson(ctx, url, body, int(conf.Timeout), header)
	if gErr != nil {
		return nil, srerr.With(srerr.CustomsServiceErr, nil, gErr)
	}

	var rsp ListCustomsAgentsResponse
	if err := jsoniter.Unmarshal(data, &rsp); err != nil {
		return nil, srerr.With(srerr.FormatErr, string(data), err)
	}

	if rsp.Retcode != 0 {
		return nil, srerr.New(srerr.CustomsServiceErr, nil, "list customs agents failed: %s", rsp.Message)
	}

	if rsp.Data.List == nil {
		return []*CustomsAgent{}, nil
	}

	return rsp.Data.List, nil
}

func (c *CCApiImpl) buildCCHeader(ctx context.Context) (map[string]string, *srerr.Error) {
	conf := configutil.GetCustomsServiceConf(ctx)
	jwtToken, err := jwtutil.BuildJWT(ctx, envvar.GetCID(), UserName, conf.JwtOptr, conf.JwtSecret)
	if err != nil {
		return nil, srerr.With(srerr.JwtTokenErr, nil, err)
	}
	return map[string]string{"jwt-token": jwtToken, "biz-ssc-cid": envvar.GetCID()}, nil
}
