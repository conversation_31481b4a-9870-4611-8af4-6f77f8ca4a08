package lfsclient

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	lfspb "git.garena.com/shopee/bg-logistics/logistics/logistics-lane-fulfilment-system/protocol/go"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lfsclient/lfsentity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	cache "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/cacheutil/lrucache"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/requestid"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/envvar"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	"github.com/gogo/protobuf/proto"
	jsoniter "github.com/json-iterator/go"
	uuid "github.com/satori/go.uuid"
	"net/http"
)

type LfsApi interface {
	BatchGetLaneInfo(ctx context.Context, laneCodeList []string) ([]*lfspb.BatchLaneInfoRspItem, *srerr.Error)
	BatchCheckSmartRoutingLaneServiceable(ctx context.Context, req *lfspb.BatchCheckSmartRoutingLaneServiceableReqData) (*lfspb.BatchCheckLaneServiceableRspData, *srerr.Error)
	BatchCheckLaneRoutable(ctx context.Context, req *lfspb.BatchCheckLaneRoutableReqData) (*lfspb.BatchCheckLaneRoutableRespData, *srerr.Error)
	ListSortingCode(ctx context.Context, req *lfsentity.ListSortingCodeRequest) (*lfsentity.ListSortingCodeResponse, *srerr.Error)
	ListBatchLaneTwsInfo(ctx context.Context, req *lfspb.ListBatchLaneTwsInfoReqData) (*lfspb.ListBatchLaneTwsInfoRespData, *srerr.Error)
	GetSortingCodeInfo(ctx context.Context, sortingCode string) (*lfspb.GetSortingCodeInfoResponseData, *srerr.Error)
	GetLaneCodeListBySiteOrLine(ctx context.Context, req *lfsentity.GetLaneCodeBySiteOrLineReq) (*lfsentity.GetLaneCodeBySiteOrLineRsp, *srerr.Error)
}

type LfsApiImpl struct {
}

func NewLfsApiImpl() *LfsApiImpl {
	return &LfsApiImpl{}
}

var (
	BatchQueryLaneInfo = client.GrpcService{
		OperationID: "BatchQueryLaneInfo",
		Scene:       client.MultiAlive,
		System:      constant.SystemLFS,
	}
	BatchCheckSmartRoutingLaneServiceable = client.GrpcService{
		OperationID: "BatchCheckSmartRoutingLaneServiceableV2",
		Scene:       client.DR,
		System:      constant.SystemLFS,
	}
	BatchCheckLaneRoutable = client.GrpcService{
		OperationID: "BatchCheckLaneRoutable",
		Scene:       client.MultiAlive,
		System:      constant.SystemLFS,
	}
	ListBatchLaneTwsInfo = client.GrpcService{
		OperationID: "ListBatchLaneTwsInfo",
		Scene:       client.MultiAlive,
		System:      constant.SystemLFS,
	}
	ListSortingCode = client.HttpService{
		Endpoint: "/admin/api/list_sorting_code_info",
		Scene:    client.DR,
		System:   constant.SystemLFS,
	}
	GetSortingCodeInfo = client.GrpcService{
		OperationID: "GetSortingCodeInfo",
		Scene:       client.DR,
		System:      constant.SystemLFS,
	}
	GetLaneCodeListBySiteOrLine = client.HttpService{
		Endpoint: "/admin/api/lane/list_lane_code",
		Scene:    client.DR,
		System:   constant.SystemLFS,
	}

	lruCache, _ = cache.NewLruCache(cache.LfsClientLruName)
)

func (p *LfsApiImpl) BatchGetLaneInfo(ctx context.Context, laneCodeList []string) ([]*lfspb.BatchLaneInfoRspItem, *srerr.Error) {
	req := lfspb.BatchLaneInfoReq{
		Header: p.getGrpcReqHeader(ctx),
		Data:   &lfspb.BatchLaneInfoReqData{LaneCodes: laneCodeList},
	}
	var rsp lfspb.BatchLaneInfoRsp
	if err := client.GrpcInvoke(ctx, constant.SystemLFS, "protocol.Lane", BatchQueryLaneInfo, &req, &rsp); err != nil {
		logger.CtxLogErrorf(ctx, "BatchQueryLaneInfo fail, request fail, error:%+v", err)
		return nil, srerr.With(srerr.LpsError, nil, err)
	}
	if rsp.Header == nil || rsp.Header.GetRetcode() != 0 || rsp.Data == nil {
		return nil, srerr.New(srerr.LfsError, nil, "invalid response: rsp;%s", objutil.JsonString(rsp))
	}
	return rsp.Data.GetItems(), nil
}

func (p *LfsApiImpl) BatchCheckLaneRoutable(ctx context.Context, req *lfspb.BatchCheckLaneRoutableReqData) (*lfspb.BatchCheckLaneRoutableRespData, *srerr.Error) {
	reqParam := &lfspb.BatchCheckLaneRoutableRequest{
		Header: p.getGrpcReqHeader(ctx),
		Data:   req,
	}
	var rsp lfspb.BatchCheckLaneRoutableResponse
	if err := client.GrpcInvoke(ctx, constant.SystemLFS, "protocol.SmartRouting", BatchCheckLaneRoutable, reqParam, &rsp); err != nil {
		return nil, srerr.New(srerr.LfsError, nil, "batch check lane routable fail, err=%+v", err)
	}
	if rsp.GetHeader() == nil {
		return nil, srerr.New(srerr.LfsError, nil, "batch check lane routable fail, invalid response from lfs")
	}
	if *rsp.GetHeader().Retcode != 0 {
		return nil, srerr.New(srerr.LfsError, nil, "batch check lane routable error, err=%+v", *rsp.GetHeader().Message)
	}
	return rsp.GetData(), nil
}

func (p *LfsApiImpl) ListBatchLaneTwsInfo(ctx context.Context, req *lfspb.ListBatchLaneTwsInfoReqData) (*lfspb.ListBatchLaneTwsInfoRespData, *srerr.Error) {
	reqParam := &lfspb.ListBatchLaneTwsInfoRequest{
		Header: p.getGrpcReqHeader(ctx),
		Data:   req,
	}
	var rsp lfspb.ListBatchLaneTwsInfoResponse
	if err := client.GrpcInvoke(ctx, constant.SystemLFS, "protocol.SmartRouting", ListBatchLaneTwsInfo, reqParam, &rsp); err != nil {
		return nil, srerr.New(srerr.LfsError, nil, "list whs info, err=%v", err)
	}
	if rsp.GetHeader() == nil {
		return nil, srerr.New(srerr.LfsError, nil, "list whs info fail, invalid response from lfs")
	}
	if rsp.GetHeader().GetRetcode() != 0 {
		return nil, srerr.New(srerr.LfsError, nil, "list whs info error, err=%v", rsp.GetHeader().GetMessage())
	}
	return rsp.GetData(), nil
}

func (p *LfsApiImpl) getGrpcReqHeader(ctx context.Context) *lfspb.ReqHeader {
	requestId := requestid.GetFromCtx(ctx)
	if requestId == "" {
		requestId = uuid.NewV4().String() // nolint
	}
	return &lfspb.ReqHeader{
		RequestId:    proto.String(requestId),
		Account:      proto.String(configutil.GetLfsGrpcConf(ctx).Account),
		Token:        proto.String(configutil.GetLfsGrpcConf(ctx).Token),
		Timestamp:    proto.Uint32(uint32(timeutil.GetCurrentUnixTimeStamp(ctx))),
		CallerIp:     proto.String(objutil.GetLocalIP()),
		DeployRegion: proto.String(envvar.GetCID()),
	}
}

func (p *LfsApiImpl) ListSortingCode(ctx context.Context, req *lfsentity.ListSortingCodeRequest) (*lfsentity.ListSortingCodeResponse, *srerr.Error) {
	reqJson, _ := jsoniter.MarshalToString(req)
	cacheKey := fmt.Sprintf("ListSortingCode:%s", reqJson)
	if cacheRsp, exist := lruCache.Get(ctx, cacheKey); exist {
		if rsp, ok := cacheRsp.(*lfsentity.ListSortingCodeResponse); ok {
			return rsp, nil
		}
	}

	var rsp lfsentity.ListSortingCodeResponse
	if err := client.HttpInvoke(ctx, constant.SystemLFS, ListSortingCode,
		http.MethodGet, req, &rsp, LfsHeader(ctx)); err != nil {
		return nil, srerr.With(srerr.LfsError, req, err)
	}

	lruCache.Add(ctx, cacheKey, &rsp)

	return &rsp, nil
}

func (p *LfsApiImpl) GetSortingCodeInfo(ctx context.Context, sortingCode string) (*lfspb.GetSortingCodeInfoResponseData, *srerr.Error) {
	reqParam := &lfspb.GetSortingCodeInfoRequest{
		Header: p.getGrpcReqHeader(ctx),
		Data: &lfspb.GetSortingCodeInfoRequestData{
			ServiceCode: proto.String(sortingCode),
		},
	}

	var rsp lfspb.GetSortingCodeInfoResponse
	if err := client.GrpcInvoke(ctx, constant.SystemLFS, "protocol.ServiceCode", GetSortingCodeInfo, reqParam, &rsp); err != nil {
		return nil, srerr.New(srerr.LfsError, nil, "get sorting code info, err=%v", err)
	}
	if rsp.GetHeader() == nil {
		return nil, srerr.New(srerr.LfsError, nil, "get sorting code info fail, invalid response from lfs")
	}
	if rsp.GetHeader().GetRetcode() != 0 {
		return nil, srerr.New(srerr.LfsError, nil, "get sorting code info error, err=%v", rsp.GetHeader().GetMessage())
	}

	return rsp.GetData(), nil
}

func (p *LfsApiImpl) GetLaneCodeListBySiteOrLine(ctx context.Context, req *lfsentity.GetLaneCodeBySiteOrLineReq) (*lfsentity.GetLaneCodeBySiteOrLineRsp, *srerr.Error) {
	var rsp lfsentity.GetLaneCodeBySiteOrLineRsp
	if err := client.HttpInvoke(ctx, constant.SystemLFS, GetLaneCodeListBySiteOrLine, http.MethodPost, req, &rsp, LfsHeader(ctx)); err != nil {
		return nil, srerr.With(srerr.LfsError, req, err)
	}

	return &rsp, nil
}
