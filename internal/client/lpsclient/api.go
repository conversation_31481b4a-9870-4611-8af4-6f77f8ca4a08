package lpsclient

import (
	"context"
	"fmt"
	"net/http"
	"strconv"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	jsoniter "github.com/json-iterator/go"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/apiutil"
	cache "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/cacheutil/lrucache"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/httputil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/jwtutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/requestid"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/envvar"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/monitoring"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
)

const (
	InitVasProductInfoSwitch = "init_vas_product_info_switch"
)

type LpsApi interface {
	GetAllProductIdNameList(ctx context.Context) (map[int64]string, *srerr.Error)
	GetLineDictList(ctx context.Context, productId int64) (*LineListResult, *srerr.Error)
	GetLaneCodes(ctx context.Context) ([]LaneCodesInfo, *srerr.Error)
	GetRuleVolumeType(ctx context.Context, id int) (string, *srerr.Error)
	GetProductBaseInfoList(ctx context.Context) ([]*LogisticProductTab, *srerr.Error)
	GetProductVasInfoList(ctx context.Context, productType int32, vasEntity int32) ([]*ProductVasInfo, *srerr.Error)
	GetProductDetail(ctx context.Context, productID int) (*ProductDetailInfo, *srerr.Error)
	HardCheck(ctx context.Context, req *HardCheckRequest) ([]*rule.RoutingLaneInfo, int, *srerr.Error)
	ILHHardCheck(ctx context.Context, req *ILHHardCheckRequest) ([]string, *srerr.Error)
	GetClientGroupInfoList(ctx context.Context, clientTagId ClientTag, shopGroupId []int64) ([]*GetClientGroupInfo, *srerr.Error)
	AddHistoryOperation(ctx context.Context, recordKey string, changeData string, operation OperationType, operator string, message string) *srerr.Error
	GetProductRelateShopGroup(ctx context.Context, clientTagId ClientTag, productId int64) ([]*ProductRelateShopGroup, *srerr.Error)
	GetClientGroupTabsByTag(ctx context.Context, tagId uint64) ([]*ClientGroupTab, *srerr.Error)
	GetShopGroupList(ctx context.Context) ([]*ShopGroup, *srerr.Error)
	GetShopGroupListByTag(ctx context.Context, clientTag uint64) ([]ShopGroupUnit, *srerr.Error)
	MaskingHardCheck(ctx context.Context, req *MaskingHardCheckRequest) (*MaskingHardCheckResponseData, *srerr.Error)
	GetShopGroup(ctx context.Context, req *GetShopGroupRequest) (*GetShopGroupResponseData, *srerr.Error)
	GetShopGroupIdsByShopIdsAndTag(ctx context.Context, shopIds []int64, clientTag ClientTag) ([]string, *srerr.Error)
	ConvertProductsToMap(ctx context.Context, tabs []*LogisticProductTab) map[int]*LogisticProductTab
	CreateHistory(ctx context.Context, model dbutil.DBModel, operationType string, changeData string, changeId uint64) *srerr.Error
	GetMaskingProductRefList(ctx context.Context) ([]*GetMaskingProductRefData, *srerr.Error)
	GetWholeShopGroupList(ctx context.Context, req *GetClientGroupTabsReq) (*DisplayClientGroupResp, *srerr.Error)
	CopyShopGroupInfo(ctx context.Context, req *CopyShopGroupReq) *srerr.Error
	GetClientInfoByPage(ctx context.Context, req *GetClientInfoByPageReq) (*ClientInfo, *srerr.Error)
	GetAllClientInfo(ctx context.Context, shopGroupList []int64, version string) (entityList, relationList []Model, err *srerr.Error)
	GetILHByMultiID(ctx context.Context, multiProductID int) (*GetILHByMultiIDRespData, *srerr.Error)
}

type LpsApiImpl struct {
}

var (
	GetProductDetail = client.HttpService{
		Endpoint: "/api/admin/product/get_product_info/for_smr",
		Scene:    client.DR,
		System:   constant.SystemLPSAdmin,
	}
	GetShopGroupListByTag = client.HttpService{
		Endpoint: "/api/admin/product_priority/get_shop_list_by_tag",
		Scene:    client.DR,
		System:   constant.SystemLPSAdmin,
	}
	MaskingHardCheck = client.HttpService{
		Endpoint: "/api/product/smart_routing/masking_hard_check",
		Scene:    client.DR,
		System:   constant.SystemLpsFulfillment,
	}
	GetShopGroup = client.HttpService{
		Endpoint: "/api/product/smart_routing/get_shop_group",
		Scene:    client.DR,
		System:   constant.SystemLPS,
	}
	CreateHistory = client.HttpService{
		Endpoint: "/api/admin/product/add_operation_log/for_smr",
		Scene:    client.DR,
		System:   constant.SystemLPSAdmin,
	}
	GetClientGroup = client.HttpService{
		Endpoint: "/api/admin/product/get_client_group_info/for_smr",
		Scene:    client.DR,
		System:   constant.SystemLPSAdmin,
	}
	AddHistoryOperation = client.HttpService{
		Endpoint: "/api/admin/product/add_operation_log/for_smr",
		Scene:    client.DR,
		System:   constant.SystemLPSAdmin,
	}
	GetProductRelateShopGroup = client.HttpService{
		Endpoint: "/api/admin/product/get_product_relate_client_group/for_smr",
		Scene:    client.DR,
		System:   constant.SystemLPSAdmin,
	}
	GetMaskingProductRef = client.HttpService{
		Endpoint: "/api/admin/product/get_masking_product_ref/for_smr",
		Scene:    client.DR,
		System:   constant.SystemLPSAdmin,
	}
	HardCheck = client.HttpService{
		Endpoint: "/api/product/smart_routing/hard_check",
		Scene:    client.DR,
		System:   constant.SystemLpsFulfillment,
	}
	IlhHardCheck = client.HttpService{
		Endpoint: "/api/product/smart_routing/ilh_hard_check",
		Scene:    client.DR,
		System:   constant.SystemLpsFulfillment,
	}
	GetWholeShopGroupList = client.HttpService{
		Endpoint: "/api/admin/client_mode/get_client_group_tab",
		Scene:    client.DR,
		System:   constant.SystemLPSAdmin,
	}
	CopyShopGroupInfo = client.HttpService{
		Endpoint: "/api/admin/client_mode/copy_shop_group_info",
		Scene:    client.DR,
		System:   constant.SystemLPSAdmin,
	}
	GetClientInfo = client.HttpService{
		Endpoint: "/api/logistics/client/get_info_by_page",
		Scene:    client.DR,
		System:   constant.SystemLpsFulfillment,
	}
	GetVasProductInfoList = client.HttpService{
		Endpoint: "/api/admin/vas_product/list_all_product_base_info",
		Scene:    client.DR,
		System:   constant.SystemLPSAdmin,
	}
	GetILHByMultiID = client.HttpService{
		Endpoint: "/api/admin/multi_product/get_ilh_by_multi_id",
		Scene:    client.DR,
		System:   constant.SystemLPSAdmin,
	}

	lruCache, _ = cache.NewLruCache(cache.LpsClientLruName)
)

func NewLpsApiImpl() *LpsApiImpl {
	return &LpsApiImpl{}
}

// 获取buyer name
func (p *LpsApiImpl) GetAllProductIdNameList(ctx context.Context) (map[int64]string, *srerr.Error) {
	header, err := p.buildLpsAdminHeader(ctx)
	if err != nil {
		return nil, err
	}
	conf := configutil.GetLpsAdminConf(ctx)
	url := objutil.Merge(conf.Host, GetAllProductDictPath)
	data, gErr := httputil.Get(ctx, url, nil, int(conf.Timeout), header)
	if gErr != nil {
		return nil, srerr.With(srerr.LpsError, nil, gErr)
	}
	var rsp ProductDictResponse
	if err := jsoniter.Unmarshal(data, &rsp); err != nil {
		return nil, srerr.With(srerr.FormatErr, nil, err)
	}
	if rsp.RetCode != 0 || rsp.Data == nil {
		return nil, srerr.New(srerr.LpsError, nil, "invalid response:%s", objutil.JsonString(rsp))
	}
	result := make(map[int64]string)
	for _, data := range rsp.Data.DictList {
		result[data.ProductId] = data.ProductName
	}
	// 加上install product信息
	productVasInfoList, vErr := p.GetProductVasInfoList(ctx, int32(AllVasProduct), int32(AllVasEntity))
	if vErr != nil {
		return nil, srerr.New(srerr.LpsError, nil, "get product vas info error, err=%v", vErr)
	}
	for _, productVasInfo := range productVasInfoList {
		result[productVasInfo.ProductId] = productVasInfo.BuyerDisplayName
	}
	return result, nil
}

func (p *LpsApiImpl) GetLineDictList(ctx context.Context, productId int64) (*LineListResult, *srerr.Error) {
	header, err := p.buildLpsAdminHeader(ctx)
	if err != nil {
		return nil, err
	}
	conf := configutil.GetLpsAdminConf(ctx)
	url := objutil.Merge(conf.Host, GetLineDictListPath)
	data, gErr := httputil.Get(ctx, url, map[string]string{"product_id": strconv.FormatInt(productId, 10)}, int(conf.Timeout), header)
	if gErr != nil {
		return nil, srerr.With(srerr.LpsError, nil, gErr)
	}
	var rsp LineListResponse
	if err := jsoniter.Unmarshal(data, &rsp); err != nil {
		return nil, srerr.With(srerr.FormatErr, nil, err)
	}
	if rsp.RetCode != 0 || rsp.Data == nil {
		return nil, srerr.New(srerr.LpsError, nil, "invalid response:%s", objutil.JsonString(rsp))
	}
	return rsp.Data, nil
}

func (p *LpsApiImpl) GetRuleVolumeType(ctx context.Context, id int) (string, *srerr.Error) {
	header, err := p.buildLpsAdminHeader(ctx)
	if err != nil {
		return "", srerr.With(srerr.LpsError, nil, err)
	}
	conf := configutil.GetLpsAdminConf(ctx)
	url := objutil.Merge(conf.Host, GetRuleVolumeDetailPath)
	data, gErr := httputil.Get(ctx, url, map[string]string{"id": strconv.Itoa(id)}, int(conf.Timeout), header)
	if gErr != nil {
		return "", srerr.With(srerr.LpsError, nil, gErr)
	}
	var rsp GetRuleVolumeDetail
	if err := jsoniter.Unmarshal(data, &rsp); err != nil {
		return "", srerr.With(srerr.FormatErr, nil, err)
	}
	if rsp.RetCode != 0 || rsp.Data == nil {
		return "", srerr.New(srerr.LpsError, nil, "invalid response:%s", objutil.JsonString(rsp))
	}
	return rsp.Data.RuleType.String(), nil
}

func (p *LpsApiImpl) GetLaneCodes(ctx context.Context) ([]LaneCodesInfo, *srerr.Error) {
	conf := configutil.GetLpsApiConf(ctx)
	url := objutil.Merge(conf.Host, GetLaneCodesPath)
	body, _ := jsoniter.Marshal(&GetLaneCodesRequest{})
	data, gErr := httputil.PostJson(ctx, url, body, int(conf.Timeout), nil)
	if gErr != nil {
		return nil, srerr.With(srerr.LpsError, nil, gErr)
	}
	var rsp GetLaneCodesResponse
	if err := jsoniter.Unmarshal(data, &rsp); err != nil {
		return nil, srerr.With(srerr.FormatErr, nil, err)
	}
	if rsp.RetCode != 0 {
		return nil, srerr.New(srerr.LpsError, nil, "invalid response:%s", objutil.JsonString(rsp))
	}

	return rsp.Data, nil
}

func (p *LpsApiImpl) buildLpsAdminHeader(ctx context.Context) (map[string]string, *srerr.Error) {
	conf := configutil.GetLpsAdminConf(ctx)
	jwtToken, err := jwtutil.BuildJWT(ctx, envvar.GetCID(), client.UserName, conf.JwtOptr, conf.JwtSecret)
	if err != nil {
		return nil, srerr.With(srerr.JwtTokenErr, nil, err)
	}
	return map[string]string{"jwt-token": jwtToken, "X-Request-Id": requestid.GetFromCtx(ctx), "Accept": "*/*"}, nil
}

func (p *LpsApiImpl) GetProductBaseInfoList(ctx context.Context) ([]*LogisticProductTab, *srerr.Error) {
	header, err := p.buildLpsAdminHeader(ctx)
	if err != nil {
		return nil, err
	}
	conf := configutil.GetLpsAdminConf(ctx)
	url := objutil.Merge(conf.Host, "/api/admin/product/list")
	data, gErr := httputil.Get(ctx, url, nil, int(conf.Timeout), header)
	if gErr != nil {
		return nil, srerr.With(srerr.LpsError, nil, gErr)
	}
	var rsp GetProductBaseInfoResp
	if err := jsoniter.Unmarshal(data, &rsp); err != nil {
		return nil, srerr.With(srerr.FormatErr, nil, err)
	}
	if rsp.Retcode != 0 || rsp.Data == nil {
		return nil, srerr.New(srerr.LpsError, nil, "invalid response:%s", objutil.JsonString(rsp))
	}
	// 增加install product列表
	productVasInfoList, vErr := p.GetProductVasInfoList(ctx, int32(AllVasProduct), int32(AllVasEntity))
	if vErr != nil {
		return nil, srerr.New(srerr.LpsError, nil, "get product vas info error, err=%v", vErr)
	}
	vasLogisticProductList := vasProductConvertToLogisticProduct(ctx, productVasInfoList)
	rsp.Data = append(rsp.Data, vasLogisticProductList...)

	return rsp.Data, nil
}

func (p *LpsApiImpl) GetProductVasInfoList(ctx context.Context, productType int32, vasEntity int32) ([]*ProductVasInfo, *srerr.Error) {
	if !configutil.IsSwitch(ctx, InitVasProductInfoSwitch, "") {
		return nil, nil
	}
	header := LpsHeader(ctx)
	req := &GetProductVasInfoListReq{
		ProductType: productType,
		VasEntity:   vasEntity,
	}
	var rsp GetProductVasInfoListResp
	err := client.HttpInvoke(ctx, constant.SystemLPSAdmin, GetVasProductInfoList, http.MethodPost, req, &rsp, header)
	if err != nil {
		return nil, srerr.With(srerr.LpsError, nil, err)
	}
	logger.CtxLogInfof(ctx, "GetProductVasInfoList|rsp: %v", rsp)

	return rsp.GetProductVasInfoList(), nil
}

func (p *LpsApiImpl) GetProductDetail(ctx context.Context, productID int) (*ProductDetailInfo, *srerr.Error) {
	header := LpsHeader(ctx)
	req := &ProductDetailRequest{
		ProductId:    productID,
		NeedShipment: true,
	}
	var rsp GetProductDetailResp
	err := client.HttpInvoke(ctx, constant.SystemLPSAdmin, GetProductDetail, http.MethodGet, req, &rsp, header)
	if err != nil {
		return nil, srerr.With(srerr.LpsError, nil, err)
	}
	logger.CtxLogInfof(ctx, "GetProductDetail| rsp:%v", rsp)

	return rsp.Data, nil
}

func (p *LpsApiImpl) HardCheck(ctx context.Context, req *HardCheckRequest) ([]*rule.RoutingLaneInfo, int, *srerr.Error) {
	header := LpsHeader(ctx)
	var rsp HardCheckResponse
	err := client.HttpInvoke(ctx, constant.SystemLpsFulfillment, HardCheck, http.MethodPost, req, &rsp, header)
	if err != nil {
		return nil, 0, srerr.With(srerr.LpsError, nil, err)
	}
	return rsp.Data.LaneList, rsp.Data.ValidationWeight, nil
}

func (p *LpsApiImpl) ILHHardCheck(ctx context.Context, req *ILHHardCheckRequest) ([]string, *srerr.Error) {
	header := LpsHeader(ctx)
	var rsp ILHHardCheckResponse
	err := client.HttpInvoke(ctx, constant.SystemLpsFulfillment, IlhHardCheck, http.MethodPost, req, &rsp, header)
	if err != nil {
		return nil, srerr.With(srerr.LpsError, nil, err)
	}
	return rsp.Data.AvailableLaneCodes, nil
}

func (p *LpsApiImpl) GetClientGroupTabsByTag(ctx context.Context, tagId uint64) ([]*ClientGroupTab, *srerr.Error) {
	header, err := p.buildLpsAdminHeader(ctx)
	if err != nil {
		return nil, err
	}
	conf := configutil.GetLpsAdminConf(ctx)
	url := objutil.Merge(conf.Host, GetGroupsByTag)
	req := GetClientGroupsByTagReq{ClientTagId: tagId}
	body, _ := jsoniter.Marshal(req)
	data, gErr := httputil.PostJson(ctx, url, body, int(conf.Timeout), header)
	if gErr != nil {
		return nil, srerr.With(srerr.LpsError, nil, gErr)
	}
	var rsp GetClientGroupsByTagResp
	if err := jsoniter.Unmarshal(data, &rsp); err != nil {
		return nil, srerr.With(srerr.FormatErr, nil, err)
	}
	if rsp.RetCode != 0 {
		return nil, srerr.New(srerr.LpsError, nil, "invalid response:%s", objutil.JsonString(rsp))
	}

	return rsp.Data, nil
}

func (p *LpsApiImpl) GetShopGroupList(ctx context.Context) ([]*ShopGroup, *srerr.Error) {
	header, err := p.buildLpsAdminHeader(ctx)
	if err != nil {
		return nil, err
	}
	conf := configutil.GetLpsAdminConf(ctx)
	url := objutil.Merge(conf.Host, GetShopGroupList)
	data, gErr := httputil.Get(ctx, url, nil, int(conf.Timeout), header)
	if gErr != nil {
		return nil, srerr.With(srerr.LpsError, nil, gErr)
	}
	var rsp GetShopGroupsResp
	if err := jsoniter.Unmarshal(data, &rsp); err != nil {
		return nil, srerr.With(srerr.FormatErr, nil, err)
	}
	if rsp.RetCode != 0 || len(rsp.Data.ShopGroups) == 0 {
		return nil, srerr.New(srerr.LpsError, nil, "invalid response:%s", objutil.JsonString(rsp))
	}

	return rsp.Data.ShopGroups, nil
}

func (p *LpsApiImpl) GetShopGroupListByTag(ctx context.Context, clientTag uint64) ([]ShopGroupUnit, *srerr.Error) {
	header := LpsHeader(ctx)
	req := &GetShopGroupListByTagReq{
		ClientTag: clientTag,
	}
	var rsp GetShopGroupListByTagResp
	err := client.HttpInvoke(ctx, constant.SystemLPSAdmin, GetShopGroupListByTag, http.MethodGet, req, &rsp, header)
	if err != nil {
		return nil, srerr.With(srerr.LpsError, nil, err)
	}
	logger.CtxLogInfof(ctx, "GetProductDetail| rsp:%v", rsp)

	return rsp.Data, nil
}

func (p *LpsApiImpl) MaskingHardCheck(ctx context.Context, req *MaskingHardCheckRequest) (*MaskingHardCheckResponseData, *srerr.Error) {
	header := LpsHeader(ctx)
	var rsp MaskingHardCheckResponse
	err := client.HttpInvoke(ctx, constant.SystemLpsFulfillment, MaskingHardCheck, http.MethodPost, req, &rsp, header)
	if err != nil {
		return nil, srerr.With(srerr.LpsError, nil, err)
	}

	return rsp.Data, nil
}

func (p *LpsApiImpl) GetShopGroup(ctx context.Context, req *GetShopGroupRequest) (*GetShopGroupResponseData, *srerr.Error) {
	header := LpsHeader(ctx)
	var rsp GetShopGroupResponse
	err := client.HttpInvoke(ctx, constant.SystemLPS, GetShopGroup, http.MethodPost, req, &rsp, header)
	if err != nil {
		return nil, srerr.With(srerr.LpsError, nil, err)
	}

	return rsp.Data, nil
}

// GetShopGroupIdsByShopIdsAndTag 基于现有GetShopGroup接口，批量获取店铺分组ID
// 封装现有GetShopGroup接口，按店铺ID批量获取其所属的分组ID列表
func (p *LpsApiImpl) GetShopGroupIdsByShopIdsAndTag(ctx context.Context, shopIds []int64, clientTag ClientTag) ([]string, *srerr.Error) {
	if len(shopIds) == 0 {
		return []string{}, nil
	}

	// CC Routing场景不依赖特定的mask product，使用0作为通用值
	// 这与LPS API的设计一致：mask_product_id主要用于masking业务的产品隔离，
	// 而CC Routing是基于logistics product层面的，可以使用0值
	maskProductId := 0

	// 转换shopIds为[]int类型，因为GetShopGroupRequest需要[]int
	shopIdsInt := make([]int, len(shopIds))
	for i, id := range shopIds {
		shopIdsInt[i] = int(id)
	}

	req := &GetShopGroupRequest{
		MaskProductId: maskProductId,
		ClientTagId:   uint64(clientTag),
		ShopIds:       shopIdsInt,
	}

	rsp, err := p.GetShopGroup(ctx, req)
	if err != nil {
		logger.CtxLogErrorf(ctx, "GetShopGroup failed for shopIds=%v, clientTag=%d, err=%v", shopIds, clientTag, err)
		return nil, err
	}

	if rsp == nil || len(rsp.ShopGroupIds) == 0 {
		// 没有匹配的分组，返回空列表而不是错误
		logger.CtxLogInfof(ctx, "No shop groups found for shopIds=%v, clientTag=%d", shopIds, clientTag)
		return []string{}, nil
	}

	// 转换int64为string
	groupIds := make([]string, len(rsp.ShopGroupIds))
	for i, id := range rsp.ShopGroupIds {
		groupIds[i] = strconv.FormatInt(id, 10)
	}

	logger.CtxLogDebugf(ctx, "Found shop groups=%v for shopIds=%v, clientTag=%d", groupIds, shopIds, clientTag)
	return groupIds, nil
}

func (p *LpsApiImpl) ConvertProductsToMap(ctx context.Context, tabs []*LogisticProductTab) map[int]*LogisticProductTab {
	productInfoMap := make(map[int]*LogisticProductTab, 0)
	if len(tabs) == 0 {
		return productInfoMap
	}
	for _, productInfo := range tabs {
		productInfoMap[productInfo.ProductId] = productInfo
	}
	return productInfoMap
}

func (p *LpsApiImpl) CreateHistory(ctx context.Context, model dbutil.DBModel, operationType string, changeData string, changeId uint64) *srerr.Error {
	header := LpsHeader(ctx)
	var rsp AddOperationLogResp
	req, pErr := prepareHistoryReq(ctx, model, operationType, changeData, changeId)
	if pErr != nil {
		return pErr
	}
	logger.CtxLogInfof(ctx, "CreateHistory|history:%v", req)
	err := client.HttpInvoke(ctx, constant.SystemLPSAdmin, CreateHistory, http.MethodPost, &req, &rsp, header)
	if err != nil {
		return srerr.With(srerr.LpsError, nil, err)
	}
	logger.CtxLogInfof(ctx, "GetProductDetail| rsp:%v", rsp)

	return nil
}

func prepareHistoryReq(ctx context.Context, model dbutil.DBModel, operationType string, changeData string, changeId uint64) (AddOperationLogReq, *srerr.Error) {
	emptyReq := AddOperationLogReq{}
	if model == nil {
		return emptyReq, srerr.New(srerr.ParamErr, nil, "prepareHistoryReq| nil model")
	}
	userEmail, _ := apiutil.GetUserInfo(ctx)
	req := AddOperationLogReq{
		ChangeId:   changeId,
		Operation:  operationType,
		RecordKey:  model.TableName(),
		Operator:   userEmail,
		ChangeData: changeData,
		Message:    fmt.Sprintf("%v %v", operationType, model.TableName()),
	}
	return req, nil
}

func (p *LpsApiImpl) GetClientGroupInfoList(ctx context.Context, clientTagId ClientTag, shopGroupId []int64) ([]*GetClientGroupInfo, *srerr.Error) {
	header := LpsHeader(ctx)
	req := &GetClientGroupReq{
		ClientTagId: int64(clientTagId),
		ShopGroupId: shopGroupId,
	}
	var rsp GetClientGroupResp
	err := client.HttpInvoke(ctx, constant.SystemLPSAdmin, GetClientGroup, http.MethodPost, req, &rsp, header)
	if err != nil {
		return nil, srerr.With(srerr.LpsError, nil, err)
	}
	logger.CtxLogInfof(ctx, "GetClientGroupInfoList| rsp:%v", rsp)

	return rsp.Data, nil
}

func (p *LpsApiImpl) AddHistoryOperation(ctx context.Context, recordKey string, changeData string, operation OperationType, operator string, message string) *srerr.Error {
	header := LpsHeader(ctx)
	req := &AddOperationLogRequest{
		RecordKey:  recordKey,
		ChangeData: changeData,
		Operation:  string(operation),
		Operator:   operator,
		Message:    message,
	}
	var rsp AddHistoryOperationResp
	err := client.HttpInvoke(ctx, constant.SystemLPSAdmin, AddHistoryOperation, http.MethodPost, req, &rsp, header)
	if err != nil {
		return srerr.With(srerr.LpsError, nil, err)
	}
	logger.CtxLogInfof(ctx, "AddHistoryOperation| rsp:%v", rsp)

	return nil
}

func (p *LpsApiImpl) GetProductRelateShopGroup(ctx context.Context, clientTagId ClientTag, productId int64) ([]*ProductRelateShopGroup, *srerr.Error) {
	header := LpsHeader(ctx)
	req := &GetProductRelateShopGroupReq{
		ClientTagId: int64(clientTagId),
		ProductId:   productId,
	}
	var rsp GetProductRelateShopGroupResp
	err := client.HttpInvoke(ctx, constant.SystemLPSAdmin, GetProductRelateShopGroup, http.MethodPost, req, &rsp, header)
	if err != nil {
		return nil, srerr.With(srerr.LpsError, nil, err)
	}
	logger.CtxLogInfof(ctx, "GetProductRelateShopGroup| rsp:%v", rsp)

	return rsp.Data, nil
}

func (p *LpsApiImpl) GetMaskingProductRefList(ctx context.Context) ([]*GetMaskingProductRefData, *srerr.Error) {
	header := LpsHeader(ctx)
	var rsp GetMaskingProductRefResponse
	err := client.HttpInvoke(ctx, constant.SystemLPSAdmin, GetMaskingProductRef, http.MethodGet, nil, &rsp, header)
	if err != nil {
		return nil, srerr.With(srerr.LpsError, nil, err)
	}
	logger.CtxLogInfof(ctx, "GetMaskingProductRefList| rsp:%v", rsp)

	return rsp.Data, nil
}

func (p *LpsApiImpl) GetWholeShopGroupList(ctx context.Context, req *GetClientGroupTabsReq) (*DisplayClientGroupResp, *srerr.Error) {
	header := LpsHeader(ctx)
	var rsp GetShopGroupInfoListResp
	err := client.HttpInvoke(ctx, constant.SystemLPSAdmin, GetWholeShopGroupList, http.MethodPost, req, &rsp, header)
	if err != nil {
		return nil, srerr.With(srerr.LpsError, nil, err)
	}
	logger.CtxLogInfof(ctx, "GetWholeShopGroupList| rsp:%v", rsp)

	return rsp.Data, nil
}

func (p *LpsApiImpl) CopyShopGroupInfo(ctx context.Context, req *CopyShopGroupReq) *srerr.Error {
	header := LpsHeader(ctx)
	var rsp CopyShopGroupResp
	err := client.HttpInvoke(ctx, constant.SystemLPSAdmin, CopyShopGroupInfo, http.MethodPost, req, &rsp, header)
	if err != nil {
		return srerr.With(srerr.LpsError, nil, err)
	}
	logger.CtxLogInfof(ctx, "CopyShopGroupInfo| rsp:%v", rsp)

	return nil
}

func (p *LpsApiImpl) GetClientInfoByPage(ctx context.Context, req *GetClientInfoByPageReq) (*ClientInfo, *srerr.Error) {
	header := LpsHeader(ctx)
	var rsp GetClientInfoByPageResp
	err := client.HttpInvoke(ctx, constant.SystemLpsFulfillment, GetClientInfo, http.MethodPost, req, &rsp, header)
	if err != nil {
		return nil, srerr.With(srerr.LpsError, nil, err)
	}

	return rsp.Data, nil
}

func (p *LpsApiImpl) GetAllClientInfo(ctx context.Context, shopGroupList []int64, version string) (entityList, relationList []Model, err *srerr.Error) {
	entityReq := &GetClientInfoByPageReq{
		ClientInfoType:    ClientInfoEntity,
		ClientTagId:       int64(ClientTagCBLM),
		ClientGroupIdList: shopGroupList,
		Version:           version,
		Page:              1,
		Size:              batchNum,
	}
	entityResp, err := p.GetClientInfoByPage(ctx, entityReq)
	if err != nil {
		errMsg := fmt.Sprintf("req:%+v, get entity info err:%v", *entityReq, err)
		monitoring.ReportError(ctx, monitoring.CatCbShopGroup, monitoring.CBShopGroupLpsApi, errMsg)
		logger.CtxLogErrorf(ctx, errMsg)
		return nil, nil, err
	}
	total := entityResp.Total
	for i := 0; i <= int(total/entityResp.Size); i++ {
		page := i + 1
		entityReq = &GetClientInfoByPageReq{
			ClientInfoType:    ClientInfoEntity,
			ClientTagId:       int64(ClientTagCBLM),
			ClientGroupIdList: shopGroupList,
			Version:           version,
			Page:              int64(page),
			Size:              batchNum,
		}
		entityResp, err = p.GetClientInfoByPage(ctx, entityReq)
		if err != nil {
			errMsg := fmt.Sprintf("req:%+v, get entity info err:%v", *entityReq, err)
			monitoring.ReportError(ctx, monitoring.CatCbShopGroup, monitoring.CBShopGroupLpsApi, errMsg)
			logger.CtxLogErrorf(ctx, errMsg)
			continue
		}
		entityList = append(entityList, entityResp.List...)
	}

	// 3.get product relation
	relationReq := &GetClientInfoByPageReq{
		ClientInfoType:    ClientInfoProductRelation,
		ClientTagId:       int64(ClientTagCBLM),
		ClientGroupIdList: shopGroupList,
		Version:           version,
		Page:              1,
		Size:              batchNum,
	}
	relationResp, err := p.GetClientInfoByPage(ctx, relationReq)
	if err != nil {
		errMsg := fmt.Sprintf("req:%+v, get relation info err:%v", *relationReq, err)
		monitoring.ReportError(ctx, monitoring.CatCbShopGroup, monitoring.CBShopGroupLpsApi, errMsg)
		logger.CtxLogErrorf(ctx, errMsg)
		return nil, nil, err
	}
	total = relationResp.Total
	for i := 0; i <= int(total/relationResp.Size); i++ {
		page := i + 1
		relationReq := &GetClientInfoByPageReq{
			ClientInfoType:    ClientInfoProductRelation,
			ClientTagId:       int64(ClientTagCBLM),
			ClientGroupIdList: shopGroupList,
			Version:           version,
			Page:              int64(page),
			Size:              batchNum,
		}
		relationResp, err := p.GetClientInfoByPage(ctx, relationReq)
		if err != nil {
			errMsg := fmt.Sprintf("req:%+v, get relation info err:%v", *relationReq, err)
			monitoring.ReportError(ctx, monitoring.CatCbShopGroup, monitoring.CBShopGroupLpsApi, errMsg)
			logger.CtxLogErrorf(ctx, errMsg)
			continue
		}
		relationList = append(relationList, relationResp.List...)
	}

	return entityList, relationList, nil
}

func vasProductConvertToLogisticProduct(ctx context.Context, vasProductInfoList []*ProductVasInfo) []*LogisticProductTab {
	var logisticProductTabList []*LogisticProductTab
	for _, vasProductInfo := range vasProductInfoList {
		logisticProductTabList = append(logisticProductTabList, &LogisticProductTab{
			ProductId:         int(vasProductInfo.ProductId),
			ProductFlowType:   int(vasProductInfo.ProductFlowType),
			BuyerDisplayName:  vasProductInfo.BuyerDisplayName,
			SellerDisplayName: vasProductInfo.SellerDisplayName,
			FromRegion:        vasProductInfo.FromRegion,
			ToRegion:          vasProductInfo.ToRegion,
			WhiteBlacklist:    objutil.Int(int(vasProductInfo.WhiteBlackList)),
			IsMaskingProduct:  vasProductInfo.IsMasking,
			MaskingType:       int(vasProductInfo.MaskingType),
		})
	}
	return logisticProductTabList
}

func (p *LpsApiImpl) GetILHByMultiID(ctx context.Context, multiProductID int) (*GetILHByMultiIDRespData, *srerr.Error) {
	if cacheRsp, exist := lruCache.Get(ctx, strconv.Itoa(multiProductID)); exist {
		if rsp, ok := cacheRsp.(*GetILHByMultiIDRespData); ok {
			return rsp, nil
		}
	}

	req := &GetILHByMultiIDReq{
		MultiProductID: multiProductID,
	}

	var rsp GetILHByMultiIDResp
	err := client.HttpInvoke(ctx, constant.SystemLPSAdmin, GetILHByMultiID, http.MethodGet, req, &rsp, LpsHeader(ctx))
	if err != nil {
		lruCache.Add(ctx, strconv.Itoa(multiProductID), &GetILHByMultiIDRespData{})
		return nil, srerr.With(srerr.LpsError, nil, err)
	}

	lruCache.Add(ctx, strconv.Itoa(multiProductID), rsp.Data)

	return rsp.Data, nil
}
