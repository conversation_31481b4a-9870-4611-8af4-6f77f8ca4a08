package lpsclient

import (
	"fmt"
)

const (
	GetAllProductDictPath   = "/api/admin/product/get_all_product_dict"
	GetLineDictListPath     = "/api/admin/product/get_line_dict_list"
	GetLaneCodesPath        = "/api/logistics/channels/get_lane_codes/"
	GetRuleVolumeDetailPath = "/api/admin/rule_volume/detail"
	ILHHardCheckPath        = "/api/product/smart_routing/ilh_hard_check"
	HardCheckPath           = "/api/product/smart_routing/hard_check"
	GetGroupsByTag          = "/api/admin/client_mode/get_groups_by_tag"
	GetShopGroupList        = "/api/admin/shop/get_shop_groups_info/for_smr"
)

type LocVolumeType int

const (
	LocVolumeTypeRoute   LocVolumeType = 1
	LocVolumeTypeZone    LocVolumeType = 2
	LocVolumeTypeCountry LocVolumeType = 3
)

func (v LocVolumeType) String() string {
	switch v {
	case LocVolumeTypeRoute:
		return "Route"
	case LocVolumeTypeZone:
		return "Zone"
	case LocVolumeTypeCountry:
		return "Country"
	}

	return "Unknown"
}

type ClientTag int64

const (
	ClientTagPickupWindow       ClientTag = 1
	ClientTag3PLMasking         ClientTag = 2
	ClientTag3PLMaskingForecast ClientTag = 3
	//ClientTagShippingFee ClientTag =  4
	ClientTagCBLM         ClientTag = 5
	ClientTagCCAllocation ClientTag = 8
)

type OperationType string

const (
	CreateType     OperationType = "create"
	UpdateType     OperationType = "edit"
	DeleteType     OperationType = "delete"
	HardDeleteType OperationType = "hard delete" // 硬性删除，指删除物理数据
	ImportType     OperationType = "import"
	ExportType     OperationType = "export"
)

type ClientInfoType int64

const (
	ClientInfoEntity          ClientInfoType = 1
	ClientInfoGroup           ClientInfoType = 2
	ClientInfoProductRelation ClientInfoType = 3
)

const (
	batchNum              = 1000
	ForecastVersionPrefix = "forecast"
	LiveVersionPrefix     = "live"
)

func FormatForecastVersion(ruleId int64) string {
	return fmt.Sprintf("%s-%d", ForecastVersionPrefix, ruleId)
}

func FormatLiveVersion(ruleId int64) string {
	return fmt.Sprintf("%s-%d", LiveVersionPrefix, ruleId)
}

type VasProductType int32

const (
	AllVasProduct     VasProductType = 0
	InstallVasProduct VasProductType = 1
)

type VasEntity int32

const (
	AllVasEntity   VasEntity = 0
	LocalVasEntity VasEntity = 1
	CBVasEntity    VasEntity = 2
)
