package cc_routing_rule

type (
	CCRoutingType uint8
)

const (
	CCRoutingTypeFixed          CCRoutingType = 1
	CCRoutingTypeWeight         CCRoutingType = 2
	CCRoutingTypeShopGroup      CCRoutingType = 3
	CCRoutingTypeCategory       CCRoutingType = 4
	CCRoutingTypeWeightCategory CCRoutingType = 5
)

// 规则类型字符串常量
const (
	RuleTypeStringFixed          = "fixed"
	RuleTypeStringWeight         = "weight"
	RuleTypeStringShopGroup      = "shop_group"
	RuleTypeStringCategory       = "category"
	RuleTypeStringWeightCategory = "weight_category"
)

// String 返回 CCRoutingType 对应的字符串表示
func (t CCRoutingType) String() string {
	switch t {
	case CCRoutingTypeFixed:
		return RuleTypeStringFixed
	case CCRoutingTypeWeight:
		return RuleTypeStringWeight
	case CCRoutingTypeShopGroup:
		return RuleTypeStringShopGroup
	case CCRoutingTypeCategory:
		return RuleTypeStringCategory
	case CCRoutingTypeWeightCategory:
		return RuleTypeStringWeightCategory
	default:
		return "unknown"
	}
}

type CCRoutingRuleDetail struct {
	FixedRuleDetail          FixedRuleDetail          `json:"fixed_rule_detail"`
	WeightRuleDetail         WeightRuleDetail         `json:"weight_rule_detail"`
	ShopGroupRuleDetail      ShopGroupRuleDetail      `json:"shop_group_rule_detail"`
	CategoryRuleDetail       CategoryRuleDetail       `json:"category_rule_detail"`
	WeightCategoryRuleDetail WeightCategoryRuleDetail `json:"weight_category_rule_detail"`
}

type FixedRuleDetail struct {
	FixedCustomsClearance string `json:"fixed_customs_clearance"`
}

type WeightRuleDetail struct {
	RuleList []RuleDetailWeightItem `json:"rule_list"`
}

type RuleDetailWeightItem struct {
	MinWeight        int    `json:"min_weight"`
	MaxWeight        int    `json:"max_weight"`
	CustomsClearance string `json:"customs_clearance"`
}

// ShopGroupRuleDetail 按店铺分组路由规则
type ShopGroupRuleDetail struct {
	Rules                   []ShopGroupRuleItem `json:"rules"`
	DefaultCustomsClearance string              `json:"default_customs_clearance"`
}

type ShopGroupRuleItem struct {
	ClientTagId      uint8  `json:"client_tag_id"`     // 必须为8(CB CC Allocation)
	ClientGroupId    string `json:"client_group_id"`   // 店铺分组ID
	CustomsClearance string `json:"customs_clearance"` // 分配的CC
}

// CategoryRuleDetail 按类目路由规则
type CategoryRuleDetail struct {
	Rules                   []CategoryRuleItem `json:"rules"`
	DefaultCustomsClearance string             `json:"default_customs_clearance"`
}

type CategoryRuleItem struct {
	CategoryId       int    `json:"category_id"`       // 类目ID
	CustomsClearance string `json:"customs_clearance"` // 分配的CC
}

// WeightCategoryRuleDetail 按重量+类目路由规则
type WeightCategoryRuleDetail struct {
	Rules                   []WeightCategoryRuleItem `json:"rules"`
	DefaultCustomsClearance string                   `json:"default_customs_clearance"`
}

type WeightCategoryRuleItem struct {
	CategoryId       int    `json:"category_id"`       // 类目ID
	MinWeight        int    `json:"min_weight"`        // 最小重量(克)
	MaxWeight        int    `json:"max_weight"`        // 最大重量(克)
	CustomsClearance string `json:"customs_clearance"` // 分配的CC
}

type CCRoutingRule struct {
	Id          int                 `json:"id"`
	ProductId   int                 `json:"product_id"`
	RoutingType CCRoutingType       `json:"routing_type"`
	RuleDetail  CCRoutingRuleDetail `json:"rule_detail,omitempty"`
}
