package lfslib

// ComposeType 链路组成type
const (
	LaneComposeUndefined = 0
	LaneComposeSite      = 1
	LaneComposeLine      = 2
)

// line main type
const (
	CBLine    = 1
	LocalLine = 2
)

// line sub type
const (
	C_FL           = 1      // 1 << 0
	C_LM           = 2      // 1 << 1
	C_FM           = 4      // 1 << 2
	C_LineHaul     = 8      // 1 << 3
	C_FLAndLM      = 16     // 1 << 4
	C_FMAndFLAndLM = 32     // 1 << 5
	L_FM           = 64     // 1 << 6
	L_LM           = 128    // 1 << 7
	L_FMAndLM      = 256    // 1 << 8
	L_LineHaul     = 512    // 1 << 9
	L_SelfBuild    = 1024   // 1 << 10
	L_MiddleMile   = 2048   // 1 << 11 MY SPX多段式中的中间段, FM->MM->LM
	C_ILH          = 4096   // 1 << 12
	C_XAndT_ILH    = 8192   // 1 << 13
	C_M_ILH        = 16384  // 1 << 14 进口清关商
	C_DFM          = 32768  // 1 << 15 CB多段式的本地段中的头程
	C_DLM          = 65536  // 1 << 16 CB多段式的本地段中的尾程
	C_OriginExport = 131072 // 1 << 17 出口清关商(不负责干线运输)
	C_LH           = 262144 // 1 << 18 干线运输商(仅干线运输)
	C_LHAndImport  = 524288 // 1 << 19 进口干线(干线运输+进口清关)
)

var (
	NeedRoutingILH       = []int{C_ILH, C_XAndT_ILH}
	SpxNoNeedRoutingLine = []int{C_FL, C_ILH, C_XAndT_ILH, C_M_ILH}
	CBLMLine             = []int{C_LM, C_DFM}
	ILHLine              = []int{C_ILH, C_XAndT_ILH, C_LH} // 国际干线中负责运输的Line类型列表
	CCLine               = []int{C_M_ILH, C_LHAndImport}   // 国际干线中负责清关的Line类型列表
)

const (
	TrackingNumber13digits          = -112501
	EMSTrackingNumberMustBeginWithE = -112502
	EMSTrackingNumberMustEndWithTH  = -112503
	TrackingNumberMustBeginWithERO  = -112504
	MailTrackingNumberMustEndWithTH = -112505
	TrackingNumberHasBeenUsed       = -112506
	TrackingNumberShouldBeValid     = -112507
	CutOfTime                       = -120014
	ShippingTracenoIsUsed           = -110384
	GetAddressCodeFailed            = -500349 //-500349lfs的错误码，获取address code失败时返回给lps
)

var (
	LineSubTypeMap = map[int32]string{
		C_FL:           "CB_FL",
		C_LM:           "CB_LM",
		C_FM:           "CB_FM",
		C_LineHaul:     "CB_LineHaul",
		C_FLAndLM:      "CB_FL&LM",
		C_FMAndFLAndLM: "CB_FM&FL&LM",
		L_FM:           "LocalFM",
		L_LM:           "LocalLM",
		L_FMAndLM:      "LocalFM&LM",
		L_LineHaul:     "LocalLineHaul",
		L_SelfBuild:    "L_SelfBuild",
		L_MiddleMile:   "L-MiddleMile",
		C_ILH:          "Export&Import_ILH",
		C_XAndT_ILH:    "Export_ILH",
		C_M_ILH:        "Import_ILH",
		C_DFM:          "CB_LM",
		C_DLM:          "C_DLM",
		C_OriginExport: "C-OriginExport",
		C_LH:           "C-LH",
		C_LHAndImport:  "C-LH&Import",
	}

	SiteSubTypeMap = map[int32]string{
		SelfPostSite:        "SelfPostSite",
		NormalShipSite:      "NormalShipSite",
		TWS:                 "TWS",
		ThirdPartyJointSite: "ThirdPartyJointSite",
		NormalJointSite:     "NormalJointSite",
		SocJointSite:        "SocJointSite",
		SelfCollectSite:     "SelfCollectSite",
		NormalDeliverSite:   "NormalDeliverSite",
	}
)

const (
	DeliverAddressDeliverCheckFail = -120007
	PickupAddressPickupCheckFail   = -120008
	PickupAddressNotSupported      = -130001
	DeliverAddressNotSupported     = -130002
	PickupPostalCodeNotSupported   = -130003
	DeliverPostalCodeNotSupported  = -130004
	TPRoutingDgError               = -600302
	PkgDgErr                       = -600204
)

var (
	DeliverCheckFail = []int32{
		DeliverAddressNotSupported,
		DeliverPostalCodeNotSupported,
		DeliverAddressDeliverCheckFail,
	}
	PickupCheckFail = []int32{
		PickupAddressNotSupported,
		PickupPostalCodeNotSupported,
		PickupAddressPickupCheckFail,
	}
	DGCheckFail = []int32{
		TPRoutingDgError,
		PkgDgErr,
	}
)

const (
	ResourceTypeSite = 1
	ResourceTypeLine = 2
)

// site main type
const (
	SiteMainTypeDefault = 0
	ShipSite            = 1
	DeliverySite        = 2
	JointSite           = 3
)

// site sub type
const (
	SiteSubTypeDefault = 0
	// ShipSite sub type
	SelfPostSite   = 1 // 自寄点
	NormalShipSite = 2 // 其他发货地

	// JointSite sub type
	TWS                 = 3 // TWS
	ThirdPartyJointSite = 4 // 3PL交接点
	NormalJointSite     = 5 // 其他中转点
	SocJointSite        = 8 // sorting center

	// DeliverSite sub type
	SelfCollectSite   = 6 // 自提点
	NormalDeliverSite = 7 // 其他收件地
)
const (
	CheckOrderScene  = 1
	CreateOrderScene = 2
	OtherScene       = 3
)

var (
	channelsSpec = []string{
		"70015",
		"70016",
	}

	channelsSpecRetCode = map[int]int{
		TrackingNumber13digits:          -13300,
		EMSTrackingNumberMustBeginWithE: -13301,
		EMSTrackingNumberMustEndWithTH:  -13302,
		TrackingNumberMustBeginWithERO:  -13303,
		MailTrackingNumberMustEndWithTH: -13304,
		TrackingNumberHasBeenUsed:       -13305,
		TrackingNumberShouldBeValid:     -13306,
		CutOfTime:                       -10003,
		ShippingTracenoIsUsed:           -3026,
		GetAddressCodeFailed:            -13320, //与lfs错误码-500349进行映射，返回给上游
	}
)

// Dummy First Mile
const (
	DummyFirstMileId   = "FM01"
	DummyFirstMileName = "Dummy"
)

// lane format sub type
// 链路模版的类型
const (
	LaneFormatSubTypeDefault           = 0
	LaneFormatCBDirect                 = 1
	LaneFormatCBFLisLM                 = 2
	LaneFormatCBStandard               = 3
	LaneFormatLocalDirect              = 4
	LaneFormatLocalStandard            = 5
	LaneFormatLocalXD                  = 6
	LaneFormatCBDirectNonIntegrated    = 7
	LaneFormatLocalDirectNonIntegrated = 8
	LaneFormatLocalSelfBuild           = 9
	LaneFormatMutilLayerCBFLisLM       = 10
	LaneFormatMultiLayerCDFM           = 13
)

// 链路模版的子类型
const (
	LaneSubFormatTypeInternational = 1
	LaneSubFormatTypeLocal         = 2
)

// 实际点类型
const (
	ActualPointTypeIn  = 1
	ActualPointTypeOut = 2
	ActualPointTypeAll = 3
)

type LineCategory uint8

const (
	UnknownType LineCategory = 0
	FM          LineCategory = 1
	MM          LineCategory = 2
	LM          LineCategory = 3
)

func GetLineCategory(subType int) LineCategory {
	switch subType {
	case C_FM, L_FM, C_DFM:
		return FM
	case C_LM, C_FLAndLM, C_FMAndFLAndLM, L_LM, L_FMAndLM, C_DLM:
		return LM
	case L_MiddleMile:
		return MM
	}
	return UnknownType
}

var LineSubTypeNameReveseMap = map[string]int32{
	"CB_FL":             C_FL,
	"CB_LM":             C_LM,
	"CB_FM":             C_FM,
	"CB_LineHaul":       C_LineHaul,
	"CB_FL&LM":          C_FLAndLM,
	"CB_FM&FL&LM":       C_FMAndFLAndLM,
	"LocalFM":           L_FM,
	"LocalLM":           L_LM,
	"LocalFM&LM":        L_FMAndLM,
	"LocalLineHaul":     L_LineHaul,
	"L_SelfBuild":       L_SelfBuild,
	"FM":                L_FM,
	"LM":                L_LM,
	"FL":                C_FL,
	"L-MiddleMile":      L_MiddleMile,
	"Export&Import_ILH": C_ILH,
	"Export_ILH":        C_XAndT_ILH,
	"Import_ILH":        C_M_ILH,
	"C_DLM":             C_DLM,
	"C-OriginExport":    C_OriginExport,
	"C-LH":              C_LH,
	"C-LH&Import":       C_LHAndImport,
}
