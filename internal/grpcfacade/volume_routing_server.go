package grpc_api

import (
	"context"
	"fmt"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	pb "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing-protocol/protocol/go"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lfsclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/lane"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/lane/lane_entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volume_counter"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/lh_capacity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/volume_counter_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/volumerouting"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/prometheusutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/grpc_util"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/monitoring"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
)

type VolumeRoutingServer struct {
	pb.UnimplementedVolumeRoutingServerServer

	ZoneRuleMgr           volumerouting.ZoneRuleMgr
	VolumeCounterSrv      volume_counter.VolumeCounter
	LaneSrv               lane.LaneService
	VolumeCounterService  volume_counter_service.VolumeCounterServiceInterface
	AllocateVolumeCounter *allocation.AllocationServiceImpl
	LfsApi                lfsclient.LfsApi
	ILHWeightCounter      volume_counter.ILHWeightCounter
	LHCapacityService     lh_capacity.LHCapacityService
}

func NewVolumeRoutingServer(
	zoneRuleMgr volumerouting.ZoneRuleMgr,
	volumeCounter volume_counter.VolumeCounter,
	laneSrv lane.LaneService,
	volumeCounterService volume_counter_service.VolumeCounterServiceInterface,
	allocateVolumeCounter *allocation.AllocationServiceImpl,
	lfsApi lfsclient.LfsApi,
	ilhWeightCounter volume_counter.ILHWeightCounter,
	lhCapacityService lh_capacity.LHCapacityService,
) *VolumeRoutingServer {
	return &VolumeRoutingServer{
		ZoneRuleMgr:           zoneRuleMgr,
		VolumeCounterSrv:      volumeCounter,
		LaneSrv:               laneSrv,
		VolumeCounterService:  volumeCounterService,
		AllocateVolumeCounter: allocateVolumeCounter,
		LfsApi:                lfsApi,
		ILHWeightCounter:      ilhWeightCounter,
		LHCapacityService:     lhCapacityService,
	}
}

// TODO 确认接口是否在使用
func (p *VolumeRoutingServer) FilterByVolume(ctx context.Context, req *pb.VolumeRoutingReq) (*pb.VolumeRoutingResp, error) {
	return nil, nil
}

func (p *VolumeRoutingServer) StatisticVolumeByOrder(ctx context.Context, req *pb.VolumeStatisticReq) (*pb.VolumeStatisticResp, error) {
	if err := p.ZoneRuleMgr.StatisticVolumeByOrder(ctx, req.GetForderId()); err != nil {
		logger.CtxLogErrorf(ctx, "statistic volume exists errors,forderId: %s, err: %+v", req.GetForderId(), err)
	}
	return &pb.VolumeStatisticResp{
		Header: grpc_util.GenerateRespHeader(req.GetHeader().GetRequestId(), nil),
	}, nil
}

func (p *VolumeRoutingServer) VolumeCounter(ctx context.Context, req *pb.VolumeCounterReq) (*pb.VolumeCounterRsp, error) {
	//lines-range-overs,
	//1.counter-lines
	rsp := new(pb.VolumeCounterRsp)
	rsp.Header = grpc_util.GenerateRespHeader(req.GetHeader().GetRequestId(), nil)
	if err := p.ZoneRuleMgr.VolumeCounter(ctx, req.GetProductId(), req.GetLaneCode(), req.GetDeliveryPostcode(), req.GetLineidList(), req.GetDeliveryLocationidList()); err != nil {
		logger.CtxLogErrorf(ctx, "VolumeCounter failed|err=%v", err)
	}
	//2.match zone
	//3.counter zones
	return rsp, nil
}

func (p *VolumeRoutingServer) AccumulateCapacity(ctx context.Context, req *pb.AccumulateCapacityReq) (*pb.AccumulateCapacityResp, error) {
	rsp := new(pb.AccumulateCapacityResp)

	if req.GetParcelQuantity() < 0 {
		rsp.Header = grpc_util.GenerateRespHeader(req.GetHeader().GetRequestId(), srerr.New(srerr.ParamErr, req.GetParcelQuantity(), "Parcel quantity can not be negative"))
		return rsp, nil
	}

	laneInfo, err := p.LaneSrv.GetLaneInfoByLaneCode(ctx, req.GetLaneCode())
	if err != nil {
		rsp.Header = grpc_util.GenerateRespHeader(req.GetHeader().GetRequestId(), err)
		return rsp, nil
	}

	var destPortId string
	destPort := laneInfo.GetExportThirdPartyJoint()
	if destPort != nil {
		destPortId = destPort.SiteID
	} else {
		logger.CtxLogErrorf(ctx, "Get destination port from lane info fail")
	}

	for _, scene := range req.StatisticsScene {
		switch scene {
		case pb.StatisticsScene_OrderQuantity:
			for _, lineId := range req.GetLineIdList() {
				// 1. Carton维度数量增加
				// 1.1 Line
				if err := p.VolumeCounterSrv.IncrILHCartonVolume(ctx, int(req.GetProductId()), lineId, int(req.GetDgType()),
					req.GetTwsCode(), destPortId, req.GetCreateOrderTime()); err != nil {
					logger.CtxLogErrorf(ctx, "IncrILHCartonVolume fail|err=%v", err)
				}
				// 1.2 Product
				if err := p.VolumeCounterSrv.IncrILHProductCartonCounter(ctx, int(req.GetProductId()), lineId, int(req.GetDgType()),
					req.GetServiceCode(), req.GetCreateOrderTime()); err != nil {
					logger.CtxLogErrorf(ctx, "IncrILHProductCartonCounter fail|err=%v", err)
				}

				// 2. Parcel维度数量增加
				// 2.1 Line
				if err := p.VolumeCounterSrv.IncrILHParcelVolume(ctx, int(req.GetProductId()), lineId, int(req.GetDgType()),
					req.GetTwsCode(), destPortId, req.GetParcelQuantity(), req.GetCreateOrderTime()); err != nil {
					logger.CtxLogErrorf(ctx, "IncrILHParcelVolume fail|err=%v", err)
				}
			}
			if err := p.VolumeCounterSrv.IncrILHProductLaneCartonCounter(ctx, int(req.GetProductId()), req.GetLaneCode(), int(req.GetDgType()),
				req.GetServiceCode(), req.GetCreateOrderTime()); err != nil {
				logger.CtxLogErrorf(ctx, "IncrILHProductLaneCartonCounter fail|err=%v", err)
			}
			if err := p.VolumeCounterSrv.IncrILHProductLaneParcelCounter(ctx, int(req.GetProductId()), req.GetLaneCode(), int(req.GetDgType()),
				req.GetServiceCode(), req.GetParcelQuantity(), req.GetCreateOrderTime()); err != nil {
				logger.CtxLogErrorf(ctx, "IncrILHProductLaneParcelCounter fail|err=%v", err)
			}
		case pb.StatisticsScene_Weight:
			for _, lineId := range req.GetLineIdList() {
				if err := p.VolumeCounterSrv.IncrILHWeight(ctx, int(req.GetProductId()), lineId, int(req.GetDgType()),
					req.GetTwsCode(), destPortId, req.GetWeight(), req.GetCreateOrderTime()); err != nil {
					logger.CtxLogErrorf(ctx, "IncrLineVolume fail|err=%v", err)
				}
				if err := p.VolumeCounterSrv.IncrILHProductWeight(ctx, int(req.GetProductId()), lineId, int(req.GetDgType()),
					req.GetServiceCode(), req.GetWeight(), req.GetCreateOrderTime()); err != nil {
					logger.CtxLogErrorf(ctx, "IncrILHProductCartonCounter fail|err=%v", err)
				}
			}
			if err := p.VolumeCounterSrv.IncrILHProductLaneWeightCounter(ctx, int(req.GetProductId()), req.GetLaneCode(), int(req.GetDgType()),
				req.GetServiceCode(), req.GetWeight(), req.GetCreateOrderTime()); err != nil {
				logger.CtxLogErrorf(ctx, "IncrILHProductLaneWeightCounter fail|err=%v", err)
			}
		}
	}

	sortingCodeInfo, err := p.LfsApi.GetSortingCodeInfo(ctx, req.GetServiceCode())
	if err != nil {
		rsp.Header = grpc_util.GenerateRespHeader(req.GetHeader().GetRequestId(), err)
		return rsp, nil
	}
	ilh := laneInfo.GetCILHLineInfo().LineID
	importIlh := laneInfo.GetImportILHLineInfo().LineID
	lm := sortingCodeInfo.GetLmId()

	if err := p.VolumeCounterSrv.IncrILHCombinationStat(ctx, int(req.GetProductId()), ilh, importIlh, lm, req.GetTwsCode(),
		destPortId, req.GetParcelQuantity(), req.GetWeight(), req.GetCreateOrderTime()); err != nil {
		logger.CtxLogErrorf(ctx, "IncrILHCombinationStat fail|err=%v", err)
	}

	if err := p.updateLHCapacityVolume(
		ctx, int(req.GetProductId()), int(req.GetDgType()), req.GetTwsCode(), laneInfo, req.GetWeight(),
		req.GetPackageNo(), 1,
	); err != nil {
		logger.CtxLogErrorf(ctx, "update LH capacity volume fail|err=%v", err)
	}

	rsp.Header = grpc_util.GenerateRespHeader(req.GetHeader().GetRequestId(), nil)

	return rsp, nil
}

func (p *VolumeRoutingServer) DecreaseCapacity(ctx context.Context, req *pb.DecreaseCapacityReq) (*pb.DecreaseCapacityResp, error) {
	rsp := new(pb.DecreaseCapacityResp)

	if req.GetParcelQuantity() < 0 {
		rsp.Header = grpc_util.GenerateRespHeader(req.GetHeader().GetRequestId(), srerr.New(srerr.ParamErr, req.GetParcelQuantity(), "Parcel quantity can not be negative"))
		return rsp, nil
	}

	laneInfo, err := p.LaneSrv.GetLaneInfoByLaneCode(ctx, req.GetLaneCode())
	if err != nil {
		rsp.Header = grpc_util.GenerateRespHeader(req.GetHeader().GetRequestId(), err)
		return rsp, nil
	}

	var destPortId string
	destPort := laneInfo.GetExportThirdPartyJoint()
	if destPort != nil {
		destPortId = destPort.SiteID
	} else {
		logger.CtxLogErrorf(ctx, "Get destination port from lane info fail")
	}

	for _, scene := range req.StatisticsScene {
		switch scene {
		case pb.StatisticsScene_OrderQuantity:
			for _, lineId := range req.GetLineIdList() {
				// 1. Carton维度数量增加
				// 1.1 Line
				if err := p.VolumeCounterSrv.DecrILHCartonVolume(ctx, int(req.GetProductId()), lineId, int(req.GetDgType()),
					req.GetTwsCode(), destPortId, req.GetCreateOrderTime()); err != nil {
					logger.CtxLogErrorf(ctx, "IncrLineVolume fail|err=%v", err)
				}
				// 1.2 Product
				if err := p.VolumeCounterSrv.DecrILHProductCartonCounter(ctx, int(req.GetProductId()), lineId, int(req.GetDgType()),
					req.GetServiceCode(), req.GetCreateOrderTime()); err != nil {
					logger.CtxLogErrorf(ctx, "IncrILHProductCartonCounter fail|err=%v", err)
				}
				// 2. Parcel维度数量增加
				// 2.1 Line
				if err := p.VolumeCounterSrv.DecrILHParcelVolume(ctx, int(req.GetProductId()), lineId, int(req.GetDgType()),
					req.GetTwsCode(), destPortId, req.GetParcelQuantity(), req.GetCreateOrderTime()); err != nil {
					logger.CtxLogErrorf(ctx, "IncrILHParcelVolume fail|err=%v", err)
				}
			}
			if err := p.VolumeCounterSrv.DecrILHProductLaneCartonCounter(ctx, int(req.GetProductId()), req.GetLaneCode(), int(req.GetDgType()),
				req.GetServiceCode(), req.GetCreateOrderTime()); err != nil {
				logger.CtxLogErrorf(ctx, "IncrILHProductLaneCartonCounter fail|err=%v", err)
			}
			if err := p.VolumeCounterSrv.DecrILHProductLaneParcelCounter(ctx, int(req.GetProductId()), req.GetLaneCode(), int(req.GetDgType()),
				req.GetServiceCode(), req.GetParcelQuantity(), req.GetCreateOrderTime()); err != nil {
				logger.CtxLogErrorf(ctx, "IncrILHProductLaneParcelCounter fail|err=%v", err)
			}
		case pb.StatisticsScene_Weight:
			for _, lineId := range req.GetLineIdList() {
				if err := p.VolumeCounterSrv.DecrILHWeight(ctx, int(req.GetProductId()), lineId, int(req.GetDgType()),
					req.GetTwsCode(), destPortId, req.GetWeight(), req.GetCreateOrderTime()); err != nil {
					logger.CtxLogErrorf(ctx, "IncrLineVolume fail|err=%v", err)
				}
				if err := p.VolumeCounterSrv.DecrILHProductWeight(ctx, int(req.GetProductId()), lineId, int(req.GetDgType()),
					req.GetServiceCode(), req.GetWeight(), req.GetCreateOrderTime()); err != nil {
					logger.CtxLogErrorf(ctx, "IncrILHProductCartonCounter fail|err=%v", err)
				}
			}
			if err := p.VolumeCounterSrv.DecrILHProductLaneWeightCounter(ctx, int(req.GetProductId()), req.GetLaneCode(), int(req.GetDgType()),
				req.GetServiceCode(), req.GetWeight(), req.GetCreateOrderTime()); err != nil {
				logger.CtxLogErrorf(ctx, "IncrILHProductLaneWeightCounter fail|err=%v", err)
			}
		}
	}

	sortingCodeInfo, err := p.LfsApi.GetSortingCodeInfo(ctx, req.GetServiceCode())
	if err != nil {
		rsp.Header = grpc_util.GenerateRespHeader(req.GetHeader().GetRequestId(), err)
		return rsp, nil
	}
	ilh := laneInfo.GetCILHLineInfo().LineID
	importIlh := laneInfo.GetImportILHLineInfo().LineID
	lm := sortingCodeInfo.GetLmId()

	if err := p.VolumeCounterSrv.DecrILHCombinationStat(ctx, int(req.GetProductId()), ilh, importIlh, lm, req.GetTwsCode(),
		destPortId, req.GetParcelQuantity(), req.GetWeight(), req.GetCreateOrderTime()); err != nil {
		logger.CtxLogErrorf(ctx, "DecrILHCombinationStat fail|err=%v", err)
	}

	if err := p.updateLHCapacityVolume(
		ctx, int(req.GetProductId()), int(req.GetDgType()), req.GetTwsCode(), laneInfo, req.GetWeight(),
		req.GetPackageNo(), -1,
	); err != nil {
		logger.CtxLogErrorf(ctx, "update LH capacity volume fail|err=%v", err)
	}

	rsp.Header = grpc_util.GenerateRespHeader(req.GetHeader().GetRequestId(), nil)

	return rsp, nil
}

// extractLHCapacityInfo 从LaneInfo中提取ILH线路、CC线路和目的港信息
func extractLHCapacityInfo(laneInfo *lane_entity.LaneInfo) (ilhLineID, ccLineID, destPortID string) {
	if lineInfo := laneInfo.GetCILHLineInfo(); lineInfo != nil {
		ilhLineID = lineInfo.LineID
	}
	if lineInfo := laneInfo.GetImportILHLineInfo(); lineInfo != nil {
		ccLineID = lineInfo.LineID
	}
	if siteInfo := laneInfo.GetExportThirdPartyJoint(); siteInfo != nil {
		destPortID = siteInfo.SiteID
	}
	return
}

// capacityAllocation 存储各种容量的分配结果
type capacityAllocation struct {
	ReservedBSA             int64            // 预留BSA使用量
	NonReservedBSA          int64            // 非预留BSA使用量
	Adhoc                   int64            // Adhoc使用量
	InheritedBSASlotUsage   map[string]int64 `json:"inherited_bsa_slot_usage"`   // 继承的BSA使用详情
	InheritedAdhocSlotUsage map[string]int64 `json:"inherited_adhoc_slot_usage"` // 继承的Adhoc使用详情
}

// getOperationPrefix 根据factor返回操作前缀
func getOperationPrefix(factor int64) string {
	if factor < 0 {
		return "Decr"
	}
	return "Incr"
}

// logILHCapacityError 辅助函数，根据factor和操作名记录错误日志
func logILHCapacityError(ctx context.Context, operation string, factor int64, err error) {
	prefix := getOperationPrefix(factor)
	logger.CtxLogErrorf(ctx, "%s%s fail|err=%v", prefix, operation, err)
}

// 更新ILH容量
func (p *VolumeRoutingServer) updateILHCapacity(
	ctx context.Context,
	productID int,
	ilhLineID string,
	dgType int,
	twsCode string,
	destPortID string,
	slotID string,
	allocation capacityAllocation,
	factor int64, // +1 表示增加容量，-1 表示减少容量
) {
	// 计算BSA总用量
	totalBSAIncr := allocation.ReservedBSA + allocation.NonReservedBSA

	// 更新产品级别的BSA用量
	if totalBSAIncr > 0 {
		if err := p.ILHWeightCounter.IncrILHProductBSAWeight(ctx, productID, ilhLineID, dgType, twsCode, destPortID, slotID, totalBSAIncr*factor); err != nil {
			logILHCapacityError(ctx, "ILHProductBSAWeight", factor, err)
		}
	}

	// 更新总BSA用量
	if totalBSAIncr > 0 {
		if err := p.ILHWeightCounter.IncrILHBSAWeight(ctx, ilhLineID, dgType, twsCode, destPortID, slotID, totalBSAIncr*factor); err != nil {
			logILHCapacityError(ctx, "ILHBSAWeight", factor, err)
		}
	}

	// 更新Adhoc用量
	if allocation.Adhoc > 0 {
		if err := p.ILHWeightCounter.IncrILHAdhocWeight(ctx, ilhLineID, dgType, twsCode, destPortID, slotID, allocation.Adhoc*factor); err != nil {
			logILHCapacityError(ctx, "ILHAdhocWeight", factor, err)
		}
	}

	// 更新流转的TimeSlot的BSA用量
	for slotID, bsaUsage := range allocation.InheritedBSASlotUsage {
		if err := p.ILHWeightCounter.IncrILHBSAWeight(ctx, ilhLineID, dgType, twsCode, destPortID, slotID, bsaUsage*factor); err != nil {
			logILHCapacityError(ctx, "ILHBSAWeight", factor, err)
		}
	}

	// 更新流转的TimeSlot的Adhoc用量
	for slotID, adhocUsage := range allocation.InheritedAdhocSlotUsage {
		if err := p.ILHWeightCounter.IncrILHAdhocWeight(ctx, ilhLineID, dgType, twsCode, destPortID, slotID, adhocUsage*factor); err != nil {
			logILHCapacityError(ctx, "ILHAdhocWeight", factor, err)
		}
	}
}

// updateLHCapacityVolume 通用函数，处理容量的增加和减少
func (p *VolumeRoutingServer) updateLHCapacityVolume(
	ctx context.Context,
	productID int,
	dgType int,
	twsCode string,
	laneInfo *lane_entity.LaneInfo,
	orderWeight int64,
	packageNo string,
	factor int64, // +1 表示增加容量，-1 表示减少容量
) *srerr.Error {
	// 1. 提取线路信息
	ilhLineID, ccLineID, destPortID := extractLHCapacityInfo(laneInfo)

	if ilhLineID == "" || ccLineID == "" || destPortID == "" {
		logger.CtxLogInfof(ctx, "ILH line id or CC line id is empty, no need update LH capacity volume")
		return nil
	}

	// 2. 尝试从Redis获取订单使用快照
	if packageNo == "" {
		// 如果没有提供packageNo，直接返回
		return nil
	}

	orderUsage, err := p.ILHWeightCounter.GetOrderUsageInfo(ctx, packageNo)
	if err != nil {
		logger.CtxLogErrorf(ctx, "GetOrderUsageInfo failed|packageNo=%s|err=%v", packageNo, err)
		return nil
	}

	if orderUsage == nil || orderUsage.ILHLineID != ilhLineID {
		// 如果没有找到订单快照或ILH线路不匹配，直接返回
		logger.CtxLogInfof(ctx, "Order usage info not found or ILH line not match for %s|packageNo=%s", getOperationPrefix(factor), packageNo)
		return nil
	}

	// 如果找到了订单快照并且ILH线路匹配，直接用快照中的分配信息增加/减少容量
	logger.CtxLogInfof(ctx, "Found order usage info for %s|packageNo=%s|ilhLineID=%s|mode=%s|slotID=%s",
		getOperationPrefix(factor), packageNo, orderUsage.ILHLineID, orderUsage.CapacityMode, orderUsage.SlotID)

	// 3. 更新CC线路的重量
	if err := p.ILHWeightCounter.IncrCCWeight(ctx, productID, ccLineID, orderWeight*factor); err != nil {
		logILHCapacityError(ctx, "CCWeight", factor, err)
	}

	// 4.使用订单快照中的容量分配记录更新ILH容量
	a := capacityAllocation{
		ReservedBSA:             orderUsage.ReservedBSAUsage,
		NonReservedBSA:          orderUsage.NonReservedBSAUsage,
		Adhoc:                   orderUsage.AdhocUsage,
		InheritedBSASlotUsage:   orderUsage.InheritedBSASlotUsage,
		InheritedAdhocSlotUsage: orderUsage.InheritedAdhocSlotUsage,
	}

	p.updateILHCapacity(ctx, productID, ilhLineID, dgType, twsCode, destPortID, orderUsage.SlotID, a, factor)
	return nil
}

func (p *VolumeRoutingServer) UpdateVolume(ctx context.Context, req *pb.UpdateVolumeReq) (*pb.UpdateVolumeResp, error) {
	resp := new(pb.UpdateVolumeResp)
	//1. 参数校验
	err := validateUpdateVolumeParam(ctx, req)
	if err != nil {
		monitoring.ReportError(ctx, monitoring.CatUpdateVolumeApi, monitoring.ValidateParamError, fmt.Sprintf("validate param error:%v", err))
		resp.Header = grpc_util.GenerateRespHeader(req.GetHeader().GetRequestId(), err)
		return resp, nil
	}
	//2. 运力更新
	for _, volumeInfo := range req.VolumeInfoList {
		//2.1 幂等性判断
		err = p.checkDuplicate(ctx, req.GetUpdateVolumeScene(), volumeInfo.GetUpdateVolumeType(), volumeInfo.GetSlsTn())
		if err != nil {
			monitoring.ReportError(ctx, monitoring.CatUpdateVolumeApi, monitoring.DuplicateCheckError, fmt.Sprintf("duplicate check error:%v", err))
			resp.Header = grpc_util.GenerateRespHeader(req.GetHeader().GetRequestId(), err)
			return resp, nil
		}
		//2.2 smart routing运力更新
		err = p.VolumeCounterService.RoutingVolumeCounter(ctx, volumeInfo)
		if err != nil {
			monitoring.ReportError(ctx, monitoring.CatUpdateVolumeApi, monitoring.SmartRoutingVolumeError, fmt.Sprintf("smart routing volume error:%v", err))
			resp.Header = grpc_util.GenerateRespHeader(req.GetHeader().GetRequestId(), err)
			return resp, nil
		}
		// 上报routing运力更新
		prometheusutil.ReportRoutingVolumeSceneMonitor(prometheusutil.UpdateVolumeInterface, req.GetUpdateVolumeScene().String(), volumeInfo.GetProductId(), volumeInfo.GetUpdateVolumeType().String())

		//2.3 allocate运力更新
		if needUpdateAllocationVolume(ctx, req.GetUpdateVolumeScene(), volumeInfo) {
			param := allocation.AllocateCounterParam{
				OrderID:             uint64(objutil.StringToInt(ctx, volumeInfo.GetOrderId())),
				MaskingProductId:    volumeInfo.GetMaskingProductId(),
				ProductId:           volumeInfo.GetProductId(),
				PickupLocationIds:   common.Uint32ToInt64List(volumeInfo.GetPickupLocationIdList()),
				DeliveryLocationIds: common.Uint32ToInt64List(volumeInfo.GetDeliverLocationIdList()),
				SloCreateTime:       volumeInfo.GetSloCreateTime(),
				UpdateType:          volumeInfo.GetUpdateVolumeType(),
				OrderType:           volumeInfo.GetOrderType(),
				PickupPostcode:      volumeInfo.GetPickupPostcode(),
				DeliveryPostcode:    volumeInfo.GetDeliveryPostcode(),
				VolumeScene:         req.GetUpdateVolumeScene().String(),
				ShopId:              int64(volumeInfo.GetShopId()),
			}
			err = p.AllocateVolumeCounter.AllocateVolumeCounter(ctx, param)
			if err != nil {
				monitoring.ReportError(ctx, monitoring.CatUpdateVolumeApi, monitoring.AllocateVolumeError, fmt.Sprintf("allocate volume error:%v", err))
				resp.Header = grpc_util.GenerateRespHeader(req.GetHeader().GetRequestId(), err)
				return resp, nil
			}
			prometheusutil.ReportMaskingVolumeSceneMonitor(prometheusutil.UpdateVolumeInterface, req.GetUpdateVolumeScene().String(), volumeInfo.GetMaskingProductId(), volumeInfo.GetProductId(), volumeInfo.GetUpdateVolumeType().String())
		}

		//2.4 增加幂等性标记，防止重复消费
		err = p.VolumeCounterService.SetVolumeDuplicateCounter(ctx, req.GetUpdateVolumeScene(), volumeInfo.GetUpdateVolumeType(), volumeInfo.GetSlsTn())
		if err != nil {
			monitoring.ReportError(ctx, monitoring.CatUpdateVolumeApi, monitoring.SetDuplicateTagError, fmt.Sprintf("set duplicate tag error:%v", err))
			resp.Header = grpc_util.GenerateRespHeader(req.GetHeader().GetRequestId(), err)
			return resp, nil
		}
	}
	resp.Header = grpc_util.GenerateRespHeader(req.GetHeader().GetRequestId(), nil)
	return resp, nil
}

func (p *VolumeRoutingServer) checkDuplicate(ctx context.Context, updateVolumeScene pb.UpdateVolumeScene, updateVolumeType pb.UpdateVolumeType, slsTn string) *srerr.Error {
	duplicate, err := p.VolumeCounterService.CheckVolumeDuplicateCounter(ctx, updateVolumeScene, updateVolumeType, slsTn)
	if err != nil {
		return err
	}
	if duplicate {
		return srerr.New(srerr.VolumeCounterDuplicate, fmt.Sprintf("volumeType:%v, slsTn:%v", updateVolumeType, slsTn), "")
	}
	return nil
}

func validateUpdateVolumeParam(ctx context.Context, req *pb.UpdateVolumeReq) *srerr.Error {
	if len(req.VolumeInfoList) < 1 {
		return srerr.New(srerr.VolumeCounterParamsError, nil, "volume_info_list is nil")
	}
	var createNum, cancelNum int
	for _, volumeInfo := range req.VolumeInfoList {
		switch volumeInfo.GetUpdateVolumeType() {
		case pb.UpdateVolumeType_Create:
			createNum++
		case pb.UpdateVolumeType_Cancel:
			cancelNum++
		}
		// 校验order_type值的有效性
		if volumeInfo.GetOrderType() != pb.OrderType_MplOrderType && volumeInfo.GetOrderType() != pb.OrderType_WmsOrderType {
			return srerr.New(srerr.VolumeCounterParamsError, nil, "order type value must be 1 or 2")
		}
	}
	switch req.GetUpdateVolumeScene() {
	case pb.UpdateVolumeScene_CreateOrder:
		if createNum != 1 || cancelNum != 0 {
			return srerr.New(srerr.VolumeCounterParamsError, nil, "create order scene volume_info_list must be create")
		}
	case pb.UpdateVolumeScene_CancelOrder:
		if createNum != 0 || cancelNum != 1 {
			return srerr.New(srerr.VolumeCounterParamsError, nil, "cancel order scene volume_info_list must be cancel")
		}
	case pb.UpdateVolumeScene_RerouteOrder, pb.UpdateVolumeScene_MaskingRerouteOrder, pb.UpdateVolumeScene_MultiLegRerouteOrder, pb.UpdateVolumeScene_CBLMReRouteOrder:
		if createNum != 1 || cancelNum != 1 {
			return srerr.New(srerr.VolumeCounterParamsError, nil, "reroute scene volume_info_list must have one create and cancel")
		}
	case pb.UpdateVolumeScene_ConsolidationOrder:
		if createNum != 1 || cancelNum < 1 {
			return srerr.New(srerr.VolumeCounterParamsError, nil, "consolidation scene volume_info_list must be have one create and multi cancel")
		}
	case pb.UpdateVolumeScene_Rebooking:
		if createNum != 1 && cancelNum != 1 {
			return srerr.New(srerr.VolumeCounterParamsError, nil, "rebooking scene volume_info_list must be have one create or one cancel")
		}
	}

	return nil
}

func needUpdateAllocationVolume(ctx context.Context, updateScene pb.UpdateVolumeScene, volumeInfo *pb.VolumeInfo) bool {
	// 只有Masking才需要更新
	if volumeInfo.GetMaskingProductId() == 0 {
		return false
	}

	// 1.Cancel和Reroute场景需要更新
	//（Masking的Create已经由Order侧触发，这个接口由LFS触发，因此Create也更新单量的话会导致重复计数）
	// 2.SSCSMR-3546:returen masking create 场景也需要更新
	if updateScene != pb.UpdateVolumeScene_CancelOrder && updateScene != pb.UpdateVolumeScene_RerouteOrder &&
		updateScene != pb.UpdateVolumeScene_MaskingRerouteOrder && updateScene != pb.UpdateVolumeScene_ReverseCreateOrder {
		return false
	}

	if volumeInfo.GetUpdateVolumeType() == pb.UpdateVolumeType_Cancel && configutil.GetAllocateVolumeCountConf(ctx).AllowDeductVolume {
		return false
	}
	return true
}
