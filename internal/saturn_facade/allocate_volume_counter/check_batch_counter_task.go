package allocate_volume_counter

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis-saturn-server/saturn"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/volumecounter"
	jsoniter "github.com/json-iterator/go"
)

type AllocateCheckBatchVolumeCounter struct {
	VolumeCounter volumecounter.MaskVolumeCounter
}

func NewAllocateCheckBatchVolumeCounter(VolumeCounter volumecounter.MaskVolumeCounter) *AllocateCheckBatchVolumeCounter {
	return &AllocateCheckBatchVolumeCounter{
		VolumeCounter: VolumeCounter,
	}
}

func (s *AllocateCheckBatchVolumeCounter) Name() string {
	return constant.TaskNameAllocateCheckBatchVolumeCounter
}

func (s *AllocateCheckBatchVolumeCounter) MsgHandle(ctx context.Context, message *saturn.SaturnMessage) *saturn.SaturnReply {
	var reply saturn.SaturnReply
	var msg volumecounter.AllocateCheckBatchVolumeJobMsg
	if err := jsoniter.Unmarshal(message.MsgText, &msg); err != nil {
		logger.CtxLogErrorf(ctx, "Unmarshal msg body fail|err=%v", err)
		reply.Retcode = -1
		reply.Message = err.Error()
		return &reply
	}

	if err := s.VolumeCounter.CheckAllocRuleBatchVolumes(ctx, msg.RuleID, msg.BatchVolume); err != nil {
		logger.CtxLogErrorf(ctx, "CheckAllocRuleBatchVolumes fail|err=%v", err)
		reply.Retcode = -1
		reply.Message = err.Error()
		return &reply
	}

	return &reply
}
