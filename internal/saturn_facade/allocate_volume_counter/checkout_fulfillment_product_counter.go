package allocate_volume_counter

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis-saturn-server/saturn"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/allocation"
	allocation2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/prometheusutil"
	orderPb "git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/spcli/gen/go/marketplace_order_order_fulfilment_core_message_bus.pb"
	orderInfoPb "git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/spcli/gen/go/order_order_info.pb"
	msgPb "git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/spcli/gen/go/shared_service_common_message.pb"
	"github.com/golang/protobuf/proto"
	jsoniter "github.com/json-iterator/go"
	"strconv"
)

const (
	OpenCheckoutFulfillmentCounter = "open_checkout_fulfillment_counter"
)

type CheckoutFulfillmentProductCounter struct {
	AllocationSrv *allocation2.AllocationServiceImpl
}

func NewCheckoutFulfillmentProductCounter(allocationSrv *allocation2.AllocationServiceImpl) *CheckoutFulfillmentProductCounter {
	return &CheckoutFulfillmentProductCounter{
		AllocationSrv: allocationSrv,
	}
}

func (c *CheckoutFulfillmentProductCounter) Name() string {
	return constant.TaskNameCheckoutFulfillmentProductCounter
}

func (c *CheckoutFulfillmentProductCounter) MsgHandle(ctx context.Context, message *saturn.SaturnMessage) *saturn.SaturnReply {
	var (
		msg     msgPb.MsgBusMessage
		newData orderPb.OFOrderStatusEvent
		oldData orderPb.OFOrderStatusEvent
		extinfo orderPb.OFOrderStatusEventExtInfo
	)
	logger.CtxLogInfof(ctx, "CheckoutFulfillmentProductCounter|Topic=%s", message.Topic)
	prometheusutil.ReportCheckoutFulfillmentProductEvent(prometheusutil.MessageProcess, prometheusutil.TotalMsgNumber)
	//1. 解析msgBus
	err := proto.Unmarshal(message.MsgText, &msg)
	if err != nil {
		prometheusutil.ReportCheckoutFulfillmentProductEvent(prometheusutil.MessageProcess, prometheusutil.MsgTextUnmarshalError)
		logger.CtxLogErrorf(ctx, "CheckoutFulfillmentProductCounter|Unmarshal message bus err=%v", err)
		return &saturn.SaturnReply{Retcode: 0, Message: err.Error()}
	}
	if msg.GetPayload() == nil {
		prometheusutil.ReportCheckoutFulfillmentProductEvent(prometheusutil.MessageProcess, prometheusutil.PayloadUnmarshalError)
		logger.CtxLogErrorf(ctx, "CheckoutFulfillmentProductCounter|msg payload is nil")
		return &saturn.SaturnReply{Retcode: 0, Message: "message payload is nil"}
	}
	//2. 解析payload
	if err = proto.Unmarshal(msg.GetPayload().GetNewData(), &newData); err != nil {
		prometheusutil.ReportCheckoutFulfillmentProductEvent(prometheusutil.MessageProcess, prometheusutil.PayloadNewDataUnmarshalError)
		logger.CtxLogErrorf(ctx, "CheckoutFulfillmentProductCounter|Unmarshal order new data err=%v", err)
		return &saturn.SaturnReply{Retcode: 0, Message: err.Error()}
	}
	if err = proto.Unmarshal(msg.GetPayload().GetOldData(), &oldData); err != nil {
		prometheusutil.ReportCheckoutFulfillmentProductEvent(prometheusutil.MessageProcess, prometheusutil.PayloadOldDataUnmarshalError)
		logger.CtxLogErrorf(ctx, "CheckoutFulfillmentProductCounter|Unmarshal order old data err=%v", err)
		return &saturn.SaturnReply{Retcode: 0, Message: err.Error()}
	}
	//3. 检查是否是物流准备状态
	if newData.GetLogisticsStatus() == oldData.GetLogisticsStatus() || newData.GetLogisticsStatus() != int32(orderInfoPb.Constant_LOGISTICS_READY) {
		prometheusutil.ReportCheckoutFulfillmentProductEvent(prometheusutil.MessageProcess, prometheusutil.NoReadyLogisticStatus)
		return &saturn.SaturnReply{Retcode: 0, Message: ""}
	}
	//4. 获取物流信息
	err = proto.Unmarshal(newData.GetExtinfo(), &extinfo)
	if err != nil {
		prometheusutil.ReportCheckoutFulfillmentProductEvent(prometheusutil.MessageProcess, prometheusutil.ExtInfoUnmarshalError)
		logger.CtxLogErrorf(ctx, "CheckoutFulfillmentProductCounter|Unmarshal message extinfo bus err=%v", err)
		return &saturn.SaturnReply{Retcode: 0, Message: err.Error()}
	}
	newDataMsg, _ := jsoniter.MarshalToString(newData)
	extInfoMsg, _ := jsoniter.MarshalToString(extinfo)
	logger.CtxLogInfof(ctx, "msgText=%s, extInfo=%s", newDataMsg, extInfoMsg)
	if extinfo.GetLogisticsInfo() == nil || extinfo.GetLogisticsInfo().GetBuyerAddress() == nil {
		prometheusutil.ReportCheckoutFulfillmentProductEvent(prometheusutil.MessageProcess, prometheusutil.BuyerAddressIsNil)
		logger.CtxLogErrorf(ctx, "logisticsInfo or buyerAddress is nil")
		return &saturn.SaturnReply{Retcode: 0, Message: "logisticsInfo or buyerAddress is nil"}
	}
	//5. 判断开关是否打开
	if !configutil.IsSwitch(ctx, OpenCheckoutFulfillmentCounter, strconv.FormatInt(int64(newData.GetOrderId()), 10)) {
		prometheusutil.ReportCheckoutFulfillmentProductEvent(prometheusutil.MessageProcess, prometheusutil.UnOpenSwitch)
		logger.CtxLogErrorf(ctx, "unopen switch")
		return &saturn.SaturnReply{Retcode: 0, Message: "unopen switch"}
	}
	//6. 统计fulfillment product单量
	cErr := c.AllocationSrv.CheckoutFulfillmentProductCounter(ctx, convertToCheckoutFulfillmentProductRequest(newData, extinfo))
	if cErr != nil {
		prometheusutil.ReportCheckoutFulfillmentProductEvent(prometheusutil.MessageProcess, prometheusutil.ExecuteMsgError)
		logger.CtxLogErrorf(ctx, "incr checkout fulfillment product volume error|err=%v", cErr)
		return &saturn.SaturnReply{Retcode: 0, Message: cErr.Error()}
	}
	prometheusutil.ReportCheckoutFulfillmentProductEvent(prometheusutil.MessageProcess, prometheusutil.ExecuteMsgSuccess)
	return &saturn.SaturnReply{Retcode: 0, Message: ""}
}

func convertToCheckoutFulfillmentProductRequest(orderData orderPb.OFOrderStatusEvent, orderExtInfo orderPb.OFOrderStatusEventExtInfo) *allocation.CheckoutFulfillmentProductCounterRequest {
	request := &allocation.CheckoutFulfillmentProductCounterRequest{
		OrderId:    orderData.GetOrderId(),
		ShopId:     orderData.GetShopId(),
		UpdateTime: orderData.GetUpdateTime(),
	}
	if orderExtInfo.GetLogisticsInfo() != nil {
		request.ProductId = int64(orderExtInfo.GetLogisticsInfo().GetChannelId())
		request.FulfillmentProductId = int64(orderExtInfo.GetLogisticsInfo().GetFulfilmentChannelId())
	}
	if orderExtInfo.GetLogisticsInfo() != nil && orderExtInfo.GetLogisticsInfo().GetBuyerAddress() != nil {
		addressInfo := allocation.AddressInfo{
			Country:  orderExtInfo.GetLogisticsInfo().GetBuyerAddress().GetCountry(),
			State:    orderExtInfo.GetLogisticsInfo().GetBuyerAddress().GetState(),
			City:     orderExtInfo.GetLogisticsInfo().GetBuyerAddress().GetCity(),
			District: orderExtInfo.GetLogisticsInfo().GetBuyerAddress().GetDistrict(),
			Town:     orderExtInfo.GetLogisticsInfo().GetBuyerAddress().GetTown(),
			Address:  orderExtInfo.GetLogisticsInfo().GetBuyerAddress().GetAddress(),
			Zipcode:  orderExtInfo.GetLogisticsInfo().GetBuyerAddress().GetZipcode(),
		}
		request.DeliveryAddress = addressInfo
	}
	return request
}
