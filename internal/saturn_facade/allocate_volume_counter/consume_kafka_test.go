package allocate_volume_counter

import (
	"context"
	"crypto/sha256"
	"crypto/sha512"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/chassis"
	"git.garena.com/shopee/bg-logistics/go/chassis-saturn-server/saturn"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/auditclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/chargeclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lcosclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lfsclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/llsclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/locationclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lpsclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/spex_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/address"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/parcel_type_definition"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/pickup_priority"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rulevolume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/batch_allocate/allocation/order"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/business_audit"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/lane"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/outercheck"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/productpriority"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/schedule_visual"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/schedule_visual/schedule_stat"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volume_counter"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation"
	service2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/batch_allocate/service"
	parcel_type_definition2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/parcel_type_definition"
	rulevolume2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/rulevolume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/volumecounter"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/business_audit/approval_manager"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/cacheutil/layercache"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/cacheutil/localcache"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/cacheutil/localcache/lcregistry"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/redisutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/redisutil/counter"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/envvar"
	orderPb "git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/spcli/gen/go/marketplace_order_order_fulfilment_core_message_bus.pb"
	msgPb "git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/spcli/gen/go/shared_service_common_message.pb"
	"github.com/Shopify/sarama"
	"github.com/golang/protobuf/proto"
	jsoniter "github.com/json-iterator/go"
	"github.com/xdg/scram"
	"log"
	"os"
	"os/signal"
	"strings"
	"sync"
	"testing"
)

func InitConfig() {
	_ = os.Setenv("SSC_ENV", strings.ToLower(envvar.GetEnv()))
	if err := chassis.Init(
		chassis.WithChassisConfigPrefix("task_server"),
		chassis.WithGRPCUnaryServerInterceptor(), // grpc拦截器
		chassis.WithDefaultProviderHandlerChain(),
		chassis.WithDefaultConsumerHandlerChain(),
	); err != nil {
		log.Fatalf("Init failed with err: %s\n", err.Error())
		return
	}
	if err := configutil.Init(); err != nil {
		log.Fatalf("initialize global configuration fail, error:%+v", err)
	}
	if err := localcache.Init(lcregistry.LocalCacheConfig...); err != nil {
		log.Fatalf("initialize local cache fail, error:%+v", err)
	}
	if err := redisutil.InitDefaultClient(); err != nil {
		log.Fatalf("initialize redis fail, error:%+v", err)
	}
	if err := spex_service.InitSpex(); err != nil {
		log.Fatalf("init spex service failed %+v", err)
	}
}

func TestNewReportPlaceOrderVolumeJob(t *testing.T) {
	InitConfig()
	// Kafka 服务器地址
	brokers := []string{"kafka.public_2_uat.ap-sg-1-general-c.uat.mq.shopee.io:9095"}

	// 配置消费者
	config := sarama.NewConfig()
	config.Consumer.Return.Errors = true
	config.Consumer.Offsets.Initial = sarama.OffsetOldest
	config.Net.SASL.Enable = true
	config.Net.SASL.User = "guest-9b3fc2-1"
	config.Net.SASL.Password = "ad5qnq7S"
	config.Net.SASL.Mechanism = sarama.SASLTypeSCRAMSHA512
	config.Net.SASL.Handshake = true
	config.Net.SASL.SCRAMClientGeneratorFunc = func() sarama.SCRAMClient {
		return &XDGSCRAMClient{HashGeneratorFcn: SHA512}
	}

	// 创建消费者
	consumer, err := sarama.NewConsumer(brokers, config)
	if err != nil {
		log.Fatalf("Error creating consumer: %v", err)
	}
	defer func() {
		if err := consumer.Close(); err != nil {
			log.Fatalf("Error closing consumer: %v", err)
		}
	}()

	// 要消费的 topic
	topic := "of_order_db.order_fulfilment_order_status_tab-id-uat"

	// 设置消费者组
	consumerGroup, err := sarama.NewConsumerGroup(brokers, "supply_chain-sls-branch-checkout-limit-123", config)
	if err != nil {
		log.Fatalf("Error creating consumer group: %v", err)
	}
	defer func() {
		if err := consumerGroup.Close(); err != nil {
			log.Fatalf("Error closing consumer group: %v", err)
		}
	}()

	ctx := context.TODO()
	// 消费消息
	var wg sync.WaitGroup
	wg.Add(1)
	go func() {
		defer wg.Done()
		for {
			topics := []string{topic}
			handler := ConsumerGroupHandler{}

			if err := consumerGroup.Consume(ctx, topics, &handler); err != nil {
				log.Fatalf("Error from consumer group: %v", err)
			}
		}
	}()

	// 等待中断信号以优雅地关闭消费者
	sigterm := make(chan os.Signal, 1)
	signal.Notify(sigterm, os.Interrupt)
	<-sigterm

	log.Println("Closing consumer...")
	wg.Wait()
	log.Println("Consumer closed.")
}

// ConsumerGroupHandler 实现了 sarama.ConsumerGroupHandler 接口
type ConsumerGroupHandler struct{}

// Setup 在消费者启动时执行
func (h *ConsumerGroupHandler) Setup(sarama.ConsumerGroupSession) error { return nil }

// Cleanup 在消费者关闭时执行
func (h *ConsumerGroupHandler) Cleanup(sarama.ConsumerGroupSession) error { return nil }

// ConsumeClaim 消费消息
func (h *ConsumerGroupHandler) ConsumeClaim(session sarama.ConsumerGroupSession, claim sarama.ConsumerGroupClaim) error {
	for message := range claim.Messages() {
		record := msgPb.MsgBusMessage{}
		err1 := proto.Unmarshal(message.Value, &record)
		fmt.Println(err1)
		recordStr, _ := jsoniter.MarshalToString(record)
		fmt.Println(recordStr)
		payload := record.GetPayload()
		oFOrderStatusEvent := orderPb.OFOrderStatusEvent{}
		err1 = proto.Unmarshal(payload.GetNewData(), &oFOrderStatusEvent)
		fmt.Println(err1)
		oFOrderStatusEventStr, _ := jsoniter.MarshalToString(oFOrderStatusEvent)
		fmt.Println(oFOrderStatusEventStr)
		extInfo := orderPb.OFOrderStatusEventExtInfo{}
		err1 = proto.Unmarshal(oFOrderStatusEvent.Extinfo, &extInfo)
		fmt.Println(err1)
		extInfoStr, _ := jsoniter.MarshalToString(extInfo)
		fmt.Println(extInfoStr)
		extInfo.LogisticsInfo.ChannelId = proto.Int32(80088)
		newExtInfoByte, err1 := proto.Marshal(&extInfo)
		fmt.Println(err1)
		oFOrderStatusEvent.Extinfo = newExtInfoByte
		oFOrderStatusEvent.LogisticsStatus = proto.Int32(9)
		newDataByte, err2 := proto.Marshal(&oFOrderStatusEvent)
		fmt.Println(err2)
		payload.NewData = newDataByte
		recordByte, err3 := proto.Marshal(&record)
		fmt.Println(err3)
		msg := &saturn.SaturnMessage{
			MsgText: recordByte,
		}

		ctx := context.Background()
		checkoutFulfillmentProductCounter := GetFulfillmentProductCounterService()
		checkoutFulfillmentProductCounter.MsgHandle(ctx, msg)

		//oFOrderStatusEventExtInfo := orderPb.OFOrderStatusEventExtInfo{}
		//err1 = proto.Unmarshal(oFOrderStatusEvent.GetExtinfo(), &oFOrderStatusEventExtInfo)
		//fmt.Println(err1)

		session.MarkMessage(message, "")
	}
	return nil
}

var (
	SHA256 scram.HashGeneratorFcn = sha256.New
	SHA512 scram.HashGeneratorFcn = sha512.New
)

type XDGSCRAMClient struct {
	*scram.Client
	*scram.ClientConversation
	scram.HashGeneratorFcn
}

func (x *XDGSCRAMClient) Begin(userName, password, authzID string) (err error) {
	x.Client, err = x.HashGeneratorFcn.NewClient(userName, password, authzID)
	if err != nil {
		return err
	}
	x.ClientConversation = x.Client.NewConversation()
	return nil
}

func (x *XDGSCRAMClient) Step(challenge string) (response string, err error) {
	response, err = x.ClientConversation.Step(challenge)
	return
}

func (x *XDGSCRAMClient) Done() bool {
	return x.ClientConversation.Done()
}

func GetFulfillmentProductCounterService() *CheckoutFulfillmentProductCounter {
	lpsApiImpl := lpsclient.NewLpsApiImpl()
	levelCache, err := layercache.NewLayerCache()
	if err != nil {
		return nil
	}
	redisCounter := counter.NewRedisCounterImpl()
	allocationRuleImpl := rule.NewAllocationRuleImpl(lpsApiImpl)
	allocationConfigImpl := config.NewAllocationConfigImpl(lpsApiImpl)
	maskConfigRepoImpl := config.NewMaskConfigRepo(allocationRuleImpl, allocationConfigImpl)
	iMaskRuleVolumeRepo := rulevolume.NewMaskRuleVolumeRepoImpl(lpsApiImpl)
	checkVolumeFinderImpl := rulevolume2.NewCheckVolumeFinderImpl(iMaskRuleVolumeRepo)
	locationClientImpl := locationclient.NewLocationClientImpl(levelCache)
	addrRepoImpl := address.NewAddrRepoImpl(locationClientImpl)
	auditApiImpl := auditclient.NewAuditApiImpl()
	businessAuditRepoImpl := business_audit.NewBusinessAuditRepoImpl()
	approvalExecutorImpl := approval_manager.NewApprovalExecutorImpl(auditApiImpl, businessAuditRepoImpl)
	maskRuleVolumeServiceImpl := rulevolume2.NewMaskRuleVolumeServiceImpl(addrRepoImpl, lpsApiImpl, iMaskRuleVolumeRepo, levelCache, checkVolumeFinderImpl, approvalExecutorImpl, businessAuditRepoImpl)
	chargeApiImpl := chargeclient.NewChargeApiImpl()
	maskVolumeCounterImpl := volumecounter.NewMaskVolumeCounterImpl()
	softRuleService := allocation.NewSoftRuleService(maskVolumeCounterImpl, chargeApiImpl)
	maskRuleConfRepo := rule.NewMaskRuleConfRepo()
	pickupPriorityRepoImpl := pickup_priority.NewPickupPriorityRepoImpl()
	priorityRepoImpl := productpriority.NewPriorityRepoImpl(lpsApiImpl)
	priorityBusinessImpl := productpriority.NewPriorityBusinessImpl(priorityRepoImpl, lpsApiImpl)
	allOuterCheckServiceImpl := outercheck.NewAllOuterCheckServiceImpl(priorityBusinessImpl)
	maskRuleRepoImpl := rule.NewRuleRepo(iMaskRuleVolumeRepo, maskRuleVolumeServiceImpl, maskRuleConfRepo, lpsApiImpl, allocationRuleImpl, priorityRepoImpl, approvalExecutorImpl, businessAuditRepoImpl, pickupPriorityRepoImpl)
	lfsApiImpl := lfsclient.NewLfsApiImpl()
	llsApiImpl := llsclient.NewLlsApiImpl()
	laneServiceImpl := lane.NewLaneService(lfsApiImpl, llsApiImpl)
	volumeCounterImpl := volume_counter.NewVolumeCounterImpl(redisCounter, laneServiceImpl)
	client, err := redisutil.Client()
	if err != nil {
		return nil
	}
	maskingScheduleVisualStat := schedule_stat.NewMaskingScheduleVisualStat(client)
	maskingScheduleVisualStatV2 := schedule_stat.NewMaskingScheduleVisualStatV2(client)
	maskingForecastScheduleVisualStat := schedule_stat.NewMaskingForecastScheduleVisualStat(client)
	scheduleVisualSet := schedule_stat.NewScheduleVisualSet(maskingScheduleVisualStat, maskingScheduleVisualStatV2, maskingForecastScheduleVisualStat)
	scheduleCountStat := schedule_visual.NewScheduleCountStat(scheduleVisualSet)
	batchAllocateOrderRepoImpl := order.NewBatchAllocateOrderRepo()
	greyServiceImpl := service2.NewGreyServiceImpl()
	parcelTypeDefinitionRepoImpl := parcel_type_definition.NewParcelTypeDefinitionRepoImpl()
	parcelTypeDefinitionServiceImpl := parcel_type_definition2.NewParcelTypeDefinitionServiceImpl(parcelTypeDefinitionRepoImpl, lpsApiImpl)
	spexServiceImpl := spex_service.NewSpexServiceImpl()
	lcosApiImpl := lcosclient.NewLcosApiImpl()
	allocationServiceImpl := allocation.NewAllocationService(softRuleService, maskRuleVolumeServiceImpl, maskConfigRepoImpl, maskRuleRepoImpl, allOuterCheckServiceImpl, maskVolumeCounterImpl, volumeCounterImpl, scheduleCountStat, iMaskRuleVolumeRepo, batchAllocateOrderRepoImpl, greyServiceImpl, parcelTypeDefinitionServiceImpl, spexServiceImpl, lcosApiImpl, addrRepoImpl)
	checkoutFulfillmentProductCounter := NewCheckoutFulfillmentProductCounter(allocationServiceImpl)
	return checkoutFulfillmentProductCounter
}
