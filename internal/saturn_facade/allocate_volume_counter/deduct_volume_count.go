package allocate_volume_counter

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/chassis-saturn-server/saturn"
	"git.garena.com/shopee/bg-logistics/go/go-redis"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	pb "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing-protocol/protocol/go"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	allocation3 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/allocation"
	allocation2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/rulevolume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/prometheusutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/redisutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	orderPb "git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/spcli/gen/go/marketplace_order_order_fulfilment_core_message_bus.pb"
	orderInfoPb "git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/spcli/gen/go/order_order_info.pb"
	msgPb "git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/spcli/gen/go/shared_service_common_message.pb"
	"github.com/golang/protobuf/proto"
	jsoniter "github.com/json-iterator/go"
)

type DeductVolumeParam struct {
	OrderID             uint64              `json:"order_id"`
	MaskingProductId    int64               `json:"masking_product_id"`
	ProductId           int64               `json:"product_id"`
	PickupLocationIds   []int64             `json:"pickup_location_ids"`
	DeliveryLocationIds []int64             `json:"delivery_location_ids"`
	SloCreateTime       int64               `json:"slo_create_time"`
	OrderType           pb.OrderType        `json:"order_type"`
	PickupPostcode      string              `json:"pickup_postcode"`
	DeliveryPostcode    string              `json:"delivery_postcode"`
	ShopId              int64               `json:"shop_id"`
	Cogs                float64             `json:"cogs"`
	PaymentMethod       string              `json:"payment_method"`
	ParcelDimension     *pb.ParcelDimension `json:"parcel_dimension"`
	DgType              pb.DgType           `json:"dg_type"`
}

type DeductVolumeCounter struct {
	AllocationSrv *allocation2.AllocationServiceImpl
}

func NewDeductVolumeCounter(allocationSrv *allocation2.AllocationServiceImpl) *DeductVolumeCounter {
	return &DeductVolumeCounter{
		AllocationSrv: allocationSrv,
	}
}

func (d *DeductVolumeCounter) Name() string {
	return constant.TaskNameDeductVolumeCount
}

func (d *DeductVolumeCounter) MsgHandle(ctx context.Context, message *saturn.SaturnMessage) *saturn.SaturnReply {
	if !configutil.GetAllocateVolumeCountConf(ctx).AllowDeductVolume {
		return &saturn.SaturnReply{Retcode: 0, Message: "deduct volume count toggle is off"}
	}
	var (
		msg     msgPb.MsgBusMessage
		newData orderPb.OFOrderStatusEvent
		oldData orderPb.OFOrderStatusEvent
		extinfo orderPb.OFOrderStatusEventExtInfo
	)
	logger.CtxLogInfof(ctx, "DeductVolumeCount|Topic=%s", message.Topic)
	//1. 解析msgBus
	err := proto.Unmarshal(message.MsgText, &msg)
	if err != nil {
		prometheusutil.ReportDeductVolumeCountEvent(prometheusutil.MessageProcess, prometheusutil.MsgTextUnmarshalError)
		logger.CtxLogErrorf(ctx, "DeductVolumeCount|Unmarshal message bus err=%v", err)
		return &saturn.SaturnReply{Retcode: 0, Message: err.Error()}
	}
	if msg.GetPayload() == nil {
		prometheusutil.ReportDeductVolumeCountEvent(prometheusutil.MessageProcess, prometheusutil.PayloadUnmarshalError)
		logger.CtxLogErrorf(ctx, "DeductVolumeCount|msg payload is nil")
		return &saturn.SaturnReply{Retcode: 0, Message: "message payload is nil"}
	}
	//2. 解析payload
	if err = proto.Unmarshal(msg.GetPayload().GetNewData(), &newData); err != nil {
		prometheusutil.ReportDeductVolumeCountEvent(prometheusutil.MessageProcess, prometheusutil.PayloadNewDataUnmarshalError)
		logger.CtxLogErrorf(ctx, "DeductVolumeCount|Unmarshal order new data err=%v", err)
		return &saturn.SaturnReply{Retcode: 0, Message: err.Error()}
	}
	if err = proto.Unmarshal(msg.GetPayload().GetOldData(), &oldData); err != nil {
		prometheusutil.ReportDeductVolumeCountEvent(prometheusutil.MessageProcess, prometheusutil.PayloadOldDataUnmarshalError)
		logger.CtxLogErrorf(ctx, "DeductVolumeCount|Unmarshal order old data err=%v", err)
		return &saturn.SaturnReply{Retcode: 0, Message: err.Error()}
	}
	//3. 检查物流状态
	if pass := d.CheckOrderStatus(newData, oldData); !pass {
		prometheusutil.ReportDeductVolumeCountEvent(prometheusutil.MessageProcess, prometheusutil.NoReadyLogisticStatus)
		return &saturn.SaturnReply{Retcode: 0, Message: "order status is not expected"}
	}
	//4. 获取物流信息
	if err = proto.Unmarshal(newData.GetExtinfo(), &extinfo); err != nil {
		prometheusutil.ReportDeductVolumeCountEvent(prometheusutil.MessageProcess, prometheusutil.ExtInfoUnmarshalError)
		logger.CtxLogErrorf(ctx, "DeductVolumeCount|Unmarshal message extinfo bus err=%v", err)
		return &saturn.SaturnReply{Retcode: 0, Message: err.Error()}
	}
	newDataMsg, _ := jsoniter.MarshalToString(newData)
	extInfoMsg, _ := jsoniter.MarshalToString(extinfo)
	logger.CtxLogInfof(ctx, "msg=%s, newData=%s, extInfo=%s", message, newDataMsg, extInfoMsg)
	if extinfo.GetLogisticsInfo() == nil || extinfo.GetLogisticsInfo().GetBuyerAddress() == nil {
		prometheusutil.ReportDeductVolumeCountEvent(prometheusutil.MessageProcess, prometheusutil.BuyerAddressIsNil)
		logger.CtxLogErrorf(ctx, "logisticsInfo or buyerAddress is nil")
		return &saturn.SaturnReply{Retcode: 0, Message: "logisticsInfo or buyerAddress is nil"}
	}
	//5. 统计fulfillment product单量
	if cErr := d.DeductVolumeCount(ctx, convertToCheckoutFulfillmentProductRequest(newData, extinfo)); cErr != nil {
		prometheusutil.ReportDeductVolumeCountEvent(prometheusutil.MessageProcess, prometheusutil.ExecuteMsgError)
		logger.CtxLogErrorf(ctx, "deduct volume error|err=%v", cErr)
		return &saturn.SaturnReply{Retcode: 0, Message: cErr.Error()}
	}
	prometheusutil.ReportDeductVolumeCountEvent(prometheusutil.MessageProcess, prometheusutil.ExecuteMsgSuccess)
	return &saturn.SaturnReply{Retcode: 0, Message: ""}
}

func (d *DeductVolumeCounter) CheckOrderStatus(newData orderPb.OFOrderStatusEvent, oldData orderPb.OFOrderStatusEvent) bool {
	if newData.GetLogisticsStatus() == oldData.GetLogisticsStatus() {
		return false
	}
	if oldData.GetLogisticsStatus() == int32(orderInfoPb.Constant_LOGISTICS_NOT_STARTED) || oldData.GetLogisticsStatus() == int32(orderInfoPb.Constant_LOGISTICS_COD_REJECTED) {
		return false
	}
	if newData.GetLogisticsStatus() != int32(orderInfoPb.Constant_LOGISTICS_INVALID) && newData.GetLogisticsStatus() != int32(orderInfoPb.Constant_LOGISTICS_REQUEST_CANCELED) {
		return false
	}
	return true
}

func (d *DeductVolumeCounter) DeductVolumeCount(ctx context.Context, request *allocation.CheckoutFulfillmentProductCounterRequest) *srerr.Error {
	logger.CtxLogInfof(ctx, "DeductVolumeCount|request=%s", objutil.JsonString(request))
	prometheusutil.ReportDeductVolumeCountEvent(prometheusutil.BusinessProcess, prometheusutil.TotalBusinessProcessNumber)
	//1. 检查是否是当天的订单
	currentDayStartTime := timeutil.GetStartTime(timeutil.GetLocalTime(ctx))
	if currentDayStartTime > int64(request.UpdateTime) {
		prometheusutil.ReportDeductVolumeCountEvent(prometheusutil.BusinessProcess, prometheusutil.NonTodayOrder)
		logger.CtxLogInfof(ctx, "DeductVolumeCount|non today order, no need to update volume|orderId=%d, productId=%d", request.OrderId, request.ProductId)
		return nil
	}
	//2. 检查 product
	// 多个masking product id
	maskingProductIdList, checkResult := d.AllocationSrv.CheckCheckoutDirectFulfillmentProduct(ctx, request)
	request.ProductId = request.FulfillmentProductId
	// 找不到product则直接返回
	if !checkResult && maskingProductIdList == nil {
		prometheusutil.ReportDeductVolumeCountEvent(prometheusutil.BusinessProcess, prometheusutil.FulfillmentProductNotFound)
		logger.CtxLogInfof(ctx, "DeductVolumeCount|no direct fulfillment product|orderId=%d, productId=%d", request.OrderId, request.ProductId)
		return nil
	}
	//3. 检查是否是重复orderId
	value, err1 := redisutil.GetInt(ctx, fmt.Sprintf(allocation2.DeductVolumeCountKey, request.OrderId))
	// 去重校验，获取redis报错不拦截主流程
	if err1 != nil && err1 != redis.Nil {
		prometheusutil.ReportDeductVolumeCountEvent(prometheusutil.BusinessProcess, prometheusutil.GetOrderIdFromRedisError)
		logger.CtxLogErrorf(ctx, "DeductVolumeCount|get redis error, err=%v", err1)
	}
	if err1 == nil && value == allocation2.DeductVolumeCountValue {
		prometheusutil.ReportDeductVolumeCountEvent(prometheusutil.BusinessProcess, prometheusutil.DuplicateOrder)
		logger.CtxLogInfof(ctx, "DeductVolumeCount|duplicate order, orderId=%d", request.OrderId)
		return nil
	}
	// 上报选择的fulfillment product业务指标
	prometheusutil.ReportDeductVolumeCountBusiness(request.ProductId)
	logger.CtxLogInfof(ctx, "DeductVolumeCount|direct fulfillment product|orderId=%d, productId=%d", request.OrderId, request.ProductId)
	//4. 补全更新运力的参数
	deductReqList, err := d.constructDeductVolumeReq(ctx, request, maskingProductIdList)
	if err != nil {
		prometheusutil.ReportDeductVolumeCountEvent(prometheusutil.BusinessProcess, prometheusutil.GenerateDeductVolumeRequestError)
		logger.CtxLogErrorf(ctx, "DeductVolumeCount|completion deduct counter request error, err=%v", err)
		return err
	}
	logger.CtxLogInfof(ctx, "DeductVolumeCount|update volume request=%v", objutil.JsonString(deductReqList))
	//5. 更新运力
	if deductErr := d.DeductVolume(ctx, deductReqList); deductErr != nil {
		prometheusutil.ReportDeductVolumeCountEvent(prometheusutil.BusinessProcess, prometheusutil.DeductVolumeError)
		logger.CtxLogErrorf(ctx, "DeductVolume error, err=%v", deductErr)
		return srerr.With(srerr.CheckoutFProductUpdateVolumeError, nil, deductErr)
	}
	//6. 记录orderId，防止重复消费
	if err2 := redisutil.Set(ctx, fmt.Sprintf(allocation2.DeductVolumeCountKey, request.OrderId), allocation2.DeductVolumeCountValue, allocation2.DeductVolumeCountExpire); err2 != nil {
		prometheusutil.ReportDeductVolumeCountEvent(prometheusutil.BusinessProcess, prometheusutil.RecordOrderIdToRedisError)
		logger.CtxLogErrorf(ctx, "DeductVolumeCount|set order id error, err=%v", err2)
		return srerr.With(srerr.RedisErr, nil, err2)
	}
	prometheusutil.ReportDeductVolumeCountEvent(prometheusutil.BusinessProcess, prometheusutil.ExecuteBusinessSuccess)
	return nil
}

func (d *DeductVolumeCounter) constructDeductVolumeReq(ctx context.Context, request *allocation.CheckoutFulfillmentProductCounterRequest, maskingProductIdList []int64) ([]*DeductVolumeParam, *srerr.Error) {
	//1. 根据orderId获取order信息，包括cogs、paymentMethod、sellerAddress、buyerAddress、itemId、
	orderInfo, err := d.AllocationSrv.GetOrderInfoByOrderId(ctx, request.OrderId)
	if err != nil {
		logger.CtxLogErrorf(ctx, "get order info by order id error, err=%v", err)
		return nil, err
	}
	//2. 获取item的长宽高信息
	itemInfoList, err := d.AllocationSrv.GetItemInfo(ctx, orderInfo.OrderShopItemInfoList)
	if err != nil {
		logger.CtxLogErrorf(ctx, "get item info error, err=%v", err)
		return nil, err
	}
	//3. 请求lcos获取校验后的parcel长宽高重量信息
	parcelDimension, err := d.AllocationSrv.GetParcelInfo(ctx, request, itemInfoList)
	if err != nil {
		logger.CtxLogErrorf(ctx, "get parcel info error, err=%v", err)
		return nil, err
	}
	//4. 获取location id
	pickupAddress, deliverAddress, err := d.AllocationSrv.GetLocationIds(ctx, orderInfo)
	if err != nil {
		logger.CtxLogErrorf(ctx, "get location ids error, err=%v", err)
		return nil, err
	}

	logger.CtxLogInfof(ctx, "DeductVolumeCount|request=%s, orderInfo=%s", objutil.JsonString(request), objutil.JsonString(orderInfo))
	dgType, err := d.AllocationSrv.ObtainDgType(ctx, request.FulfillmentProductId, itemInfoList, pickupAddress, deliverAddress, orderInfo.PackageAmount)
	if err != nil {
		return nil, err
	}

	//5. 构建更新运力的请求
	var deductVolumeReqList []*DeductVolumeParam
	for _, maskingProductId := range maskingProductIdList {
		deductVolumeReq := &DeductVolumeParam{
			MaskingProductId:    maskingProductId,
			ProductId:           request.FulfillmentProductId,
			PickupLocationIds:   objutil.IntToInt64Slice(pickupAddress.LocationIDs),
			DeliveryLocationIds: objutil.IntToInt64Slice(deliverAddress.LocationIDs),
			OrderType:           orderInfo.OrderType,
			PickupPostcode:      pickupAddress.GetPostalCode(),
			DeliveryPostcode:    deliverAddress.GetPostalCode(),
			Cogs:                orderInfo.Cogs,
			PaymentMethod:       orderInfo.PaymentMethod,
			ParcelDimension:     parcelDimension,
			ShopId:              int64(request.ShopId),
			DgType:              pb.DgType(dgType),
			SloCreateTime:       int64(request.UpdateTime),
		}
		deductVolumeReqList = append(deductVolumeReqList, deductVolumeReq)
	}

	return deductVolumeReqList, nil
}

func ToIntPtr(source int) *int {
	return &source
}

func (d *DeductVolumeCounter) DeductVolume(ctx context.Context, reqList []*DeductVolumeParam) error {
	var err error
	for _, req := range reqList {
		if err1 := d.AllocateVolumeCounter(ctx, req); err1 != nil {
			logger.CtxLogErrorf(ctx, "DeductVolumeCount|deduct volume error|err=%v", err1)
			err = err1
		}
	}
	return err
}

func (d *DeductVolumeCounter) AllocateVolumeCounter(ctx context.Context, req *DeductVolumeParam) *srerr.Error {
	var (
		orderId             = req.OrderID
		maskingProductId    = req.MaskingProductId
		productId           = req.ProductId
		pickupLocationIds   = req.PickupLocationIds
		deliveryLocationIds = req.DeliveryLocationIds
		sloCreateTime       = req.SloCreateTime
		orderType           = req.OrderType
		pickupPostcode      = req.PickupPostcode
		deliveryPostcode    = req.DeliveryPostcode
		shopId              = req.ShopId
	)
	ruleMode := allocation2.OrderTypeMappingRuleMode(ctx, orderType)
	locVols, err := d.AllocationSrv.MatchLocationVolumesForUpdateVolume(
		ctx, maskingProductId, []int64{productId}, pickupLocationIds, deliveryLocationIds,
		ruleMode, pickupPostcode, deliveryPostcode, allocation3.SingleAllocate,
	)
	if err != nil {
		return srerr.With(srerr.AllocationLocVolumeError, maskingProductId, err)
	}
	// 更新运力需要对匹配到底的（zoneCode + zoneDirection） 或 routeCode去重
	removeDupLocVols := rulevolume.RemoveDuplicationLocVols(locVols)

	//only product of allocation enabled will check rule
	ruleInfo, err := d.AllocationSrv.RuleRepo.GetEffectiveRuleByCache(ctx, maskingProductId, ruleMode, allocation3.SingleAllocate)
	if err != nil {
		logger.CtxLogErrorf(ctx, "soft_criteria for allocate, masking product:%v, get effective rule err:%v", maskingProductId, err)
		return srerr.New(srerr.AllocationRuleError, maskingProductId, err.Error())
	}
	groupCode := d.AllocationSrv.GetFulfillmentProductGroupCode(ctx, maskingProductId, productId, ruleMode, allocation3.SingleAllocate)
	updateVolumeErrExist := false
	if errUpdateMaskVolume := d.AllocationSrv.VolumeCounter.UpdateMaskVolume(ctx, maskingProductId, productId, groupCode, sloCreateTime, pb.UpdateVolumeType_Cancel, ruleMode); errUpdateMaskVolume != nil {
		logger.CtxLogErrorf(ctx, "update mask volume error, err=%v", errUpdateMaskVolume)
		updateVolumeErrExist = true
	}

	parcelAttr := d.AllocationSrv.GetAdjustVolumeParcelTypeAttr(ctx, maskingProductId, maskingProductId, req.ParcelDimension, req.Cogs, req.PaymentMethod, req.DgType)
	if orderType == pb.OrderType_MplOrderType {
		if err = d.AllocationSrv.VolumeCounter.DeductProductVolume(ctx, maskingProductId, productId, groupCode, sloCreateTime, parcelAttr); err != nil {
			logger.CtxLogErrorf(ctx, "mpl deduct product volume error, err=%v", err)
			updateVolumeErrExist = true
		}
	}
	for _, locVol := range removeDupLocVols[productId] {
		switch locVol.LocType {
		case rulevolume.MaskLocTypeRoute:
			if err = d.AllocationSrv.VolumeCounter.UpdateRouteVolume(ctx, maskingProductId, productId, groupCode, locVol.LocCode, sloCreateTime, pb.UpdateVolumeType_Cancel, ruleMode); err != nil {
				logger.CtxLogErrorf(ctx, "update route volume error, err=%v", err)
				updateVolumeErrExist = true
			}
			if orderType == pb.OrderType_MplOrderType {
				if err = d.AllocationSrv.VolumeCounter.DeductRouteVolume(ctx, maskingProductId, productId, groupCode, locVol.LocCode, sloCreateTime, parcelAttr, ruleMode); err != nil {
					logger.CtxLogErrorf(ctx, "mpl deduct route volume error, err=%v", err)
					updateVolumeErrExist = true
				}
			}
		case rulevolume.MaskLocTypeZone:
			if err = d.AllocationSrv.VolumeCounter.UpdateZoneVolume(ctx, maskingProductId, productId, groupCode, locVol.LocCode, locVol.ZoneDirection, sloCreateTime, pb.UpdateVolumeType_Cancel, ruleMode); err != nil {
				logger.CtxLogErrorf(ctx, "update zone volume error, err=%v", err)
				updateVolumeErrExist = true
			}
			if orderType == pb.OrderType_MplOrderType {
				if err = d.AllocationSrv.VolumeCounter.DeductZoneVolume(ctx, maskingProductId, productId, groupCode, locVol.LocCode, locVol.ZoneDirection, sloCreateTime, parcelAttr, ruleMode); err != nil {
					logger.CtxLogErrorf(ctx, "mpl deduct zone volume error, err=%v", err)
					updateVolumeErrExist = true
				}
			}
		}
	}

	// 更新规则内的batch volume 计数
	// 注意：在应用层要更新当天的总计数，因为如果硬性校验得到了唯一的fulfillment product，不会走这里的allocate
	if err = d.AllocationSrv.VolumeCounter.UpdateVolumeForRule(ctx, productId, ruleInfo.Id, ruleInfo.GetBatchVolume(), sloCreateTime, pb.UpdateVolumeType_Cancel); err != nil {
		logger.CtxLogErrorf(ctx, "update volume for rule error, err=%v", err)
		updateVolumeErrExist = true
	}
	if err = d.AllocationSrv.UpdateShopFulfillmentProducts(ctx, orderId, shopId, maskingProductId, 0, productId); err != nil {
		logger.CtxLogErrorf(ctx, " update shop fulfillment products failed | err: %v", err)
		updateVolumeErrExist = true
	}
	if updateVolumeErrExist {
		return srerr.New(srerr.AllocationLocVolumeError, productId, "deduct volume error")
	}
	return nil
}
