package allocate_volume_counter

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis-saturn-server/saturn"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	orderPb "git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/spcli/gen/go/marketplace_order_order_fulfilment_core_message_bus.pb"
	orderInfoPb "git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/spcli/gen/go/order_order_info.pb"
	msgPb "git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/spcli/gen/go/shared_service_common_message.pb"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/gogo/protobuf/proto"
	"testing"
)

func TestDeductVolumeCounter_MsgHandle(t *testing.T) {
	ctx := context.Background()
	d := &DeductVolumeCounter{}
	var patch *gomonkey.Patches
	msg, _ := proto.Marshal(&msgPb.MsgBusMessage{})

	msgNewDataErr, _ := proto.Marshal(&msgPb.MsgBusMessage{Payload: &msgPb.Payload{NewData: []byte("deduct_volume_count")}})

	msgOldDataErr, _ := proto.Marshal(&msgPb.MsgBusMessage{Payload: &msgPb.Payload{OldData: []byte("deduct_volume_count")}})

	newExtInfoPb, _ := proto.Marshal(&msgPb.MsgBusMessage{MessageType: proto.Uint32(1), CreateTime: proto.Uint64(1627612800)})
	newOFOrderStatusEventPb, _ := proto.Marshal(&orderPb.OFOrderStatusEvent{LogisticsStatus: proto.Int32(int32(orderInfoPb.Constant_LOGISTICS_REQUEST_CANCELED)),
		Extinfo: newExtInfoPb})
	oldOFOrderStatusEventPb, _ := proto.Marshal(&orderPb.OFOrderStatusEvent{LogisticsStatus: proto.Int32(int32(orderInfoPb.Constant_LOGISTICS_REQUEST_CREATED))})
	msgExtinfoErr, _ := proto.Marshal(&msgPb.MsgBusMessage{Payload: &msgPb.Payload{NewData: newOFOrderStatusEventPb, OldData: oldOFOrderStatusEventPb}})

	type args struct {
		message *saturn.SaturnMessage
	}
	tests := []struct {
		name  string
		args  args
		want  *saturn.SaturnReply
		setup func()
	}{
		// TODO: Add test cases.
		{
			args:  args{},
			setup: func() {},
		},
		{
			args: args{
				message: &saturn.SaturnMessage{MsgText: []byte("deduct_volume_count")},
			},
			setup: func() {
				patch = gomonkey.ApplyFunc(configutil.GetAllocateVolumeCountConf, func(ctx context.Context) configutil.AllocateVolumeCountConf {
					return configutil.AllocateVolumeCountConf{AllowDeductVolume: true}
				})
			},
		},
		{
			args: args{
				message: &saturn.SaturnMessage{MsgText: msg},
			},
			setup: func() {
				patch = gomonkey.ApplyFunc(configutil.GetAllocateVolumeCountConf, func(ctx context.Context) configutil.AllocateVolumeCountConf {
					return configutil.AllocateVolumeCountConf{AllowDeductVolume: true}
				})
			},
		},
		{
			args: args{
				message: &saturn.SaturnMessage{MsgText: msgNewDataErr},
			},
			setup: func() {
				patch = gomonkey.ApplyFunc(configutil.GetAllocateVolumeCountConf, func(ctx context.Context) configutil.AllocateVolumeCountConf {
					return configutil.AllocateVolumeCountConf{AllowDeductVolume: true}
				})
			},
		},
		{
			args: args{
				message: &saturn.SaturnMessage{MsgText: msgOldDataErr},
			},
			setup: func() {
				patch = gomonkey.ApplyFunc(configutil.GetAllocateVolumeCountConf, func(ctx context.Context) configutil.AllocateVolumeCountConf {
					return configutil.AllocateVolumeCountConf{AllowDeductVolume: true}
				})
			},
		},
		{
			args: args{
				message: &saturn.SaturnMessage{MsgText: msgExtinfoErr},
			},
			setup: func() {
				patch = gomonkey.ApplyFunc(configutil.GetAllocateVolumeCountConf, func(ctx context.Context) configutil.AllocateVolumeCountConf {
					return configutil.AllocateVolumeCountConf{AllowDeductVolume: true}
				})
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup()
			d.MsgHandle(ctx, tt.args.message)
		})
		if patch != nil {
			patch.Reset()
		}
	}
}
