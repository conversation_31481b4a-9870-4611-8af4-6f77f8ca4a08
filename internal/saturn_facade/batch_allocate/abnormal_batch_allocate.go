package batch_allocate

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis-saturn-server/saturn"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/batch_allocate/allocation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
)

type AbnormalBatchAllocateTask struct {
	batchAllocationService allocation.BatchAllocateService
}

func NewAbnormalBatchAllocateTask(batchAllocationService allocation.BatchAllocateService) *AbnormalBatchAllocateTask {
	return &AbnormalBatchAllocateTask{
		batchAllocationService: batchAllocationService,
	}
}

func (s AbnormalBatchAllocateTask) Name() string {
	return constant.TaskNameAbnormalBatchAllocate
}

func (s AbnormalBatchAllocateTask) RpcHandle(ctx context.Context, args saturn.JobArgs) error {
	// 根据Apollo配置匹配saturn切片序号
	maskProductID := 0
	for key, value := range configutil.GetBatchAllocateConf().MaskProductTableMapping {
		if value == int(args.ShardingNo) {
			maskProductID = key
			break
		}
	}
	// 匹配失败，返回
	if maskProductID == 0 {
		logger.CtxLogErrorf(ctx, "abnormal batch allocate|fail to map mask sharding no:%v", args.ShardingNo)
		return nil
	}

	if err := s.batchAllocationService.AbnormalBatchAllocate(ctx, uint64(maskProductID)); err != nil {
		logger.CtxLogErrorf(ctx, "execute batch allocate failed: %v", err)
		return err
	}

	return nil
}
