package batch_allocate

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis-saturn-server/saturn"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/batch_allocate/allocation/order"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil/str"
	jsoniter "github.com/json-iterator/go"
)

const (
	// inner error Code for BatchAllocateHoldOrderConsumer
	unmarshalMsgError = -1
	unmarshalTabError = -2
	insertDataError   = -3
)

type BatchAllocateHoldOrderConsumer struct {
	BatchAllocateOrderRepo order.BatchAllocateOrderRepo
}

func NewBatchAllocateHoldOrderConsumer(
	orderRepo order.BatchAllocateOrderRepo,
) *BatchAllocateHoldOrderConsumer {
	return &BatchAllocateHoldOrderConsumer{
		BatchAllocateOrderRepo: orderRepo,
	}
}

func (c *BatchAllocateHoldOrderConsumer) Name() string {
	return constant.TaskNameBatchAllocateHoldOrders
}

func (c *BatchAllocateHoldOrderConsumer) MsgHandle(ctx context.Context, message *saturn.SaturnMessage) *saturn.SaturnReply {
	// 初始化saturn reply
	var reply saturn.SaturnReply
	// 解析hold单数据 kafka=>system
	tabMsg := &order.BatchAllocateHoldOrderTab{}
	if umErr := jsoniter.Unmarshal(message.MsgText, tabMsg); umErr != nil {
		logger.CtxLogErrorf(ctx, "Unmarshal msg body failed|tab=%s|err=%v", str.JsonString(tabMsg), umErr)
		reply.Retcode = unmarshalMsgError
		reply.Message = umErr.Error()
		return &reply
	}
	// 解析hold单数据 system=>db
	if umErr := tabMsg.Marshall(); umErr != nil {
		logger.CtxLogErrorf(ctx, "Unmarshal msg body failed|tab=%s|err=%v", str.JsonString(tabMsg), umErr)
		reply.Retcode = unmarshalTabError
		reply.Message = umErr.Error()
		return &reply
	}
	// 保存hold单信息到db
	if insertErr := c.BatchAllocateOrderRepo.InsertOrderToHoldTab(ctx, tabMsg.MaskProductID, tabMsg.Day, tabMsg); insertErr != nil {
		logger.CtxLogErrorf(ctx, "insert msg to hold tab failed|tab=%s|err=%v", str.JsonString(tabMsg), insertErr)
		reply.Retcode = insertDataError
		reply.Message = insertErr.Error()
		return &reply
	}
	logger.CtxLogInfof(ctx, "insert msg to hold tab success|tab=%s", str.JsonString(tabMsg))
	return &reply
}
