package batch_allocate

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis-saturn-server/saturn"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/batch_allocate/allocation/order"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
)

type ClearOrderAndResultTask struct {
	BatchAllocateOrderRepo order.BatchAllocateOrderRepo
}

func NewClearOrderAndResultTask(repo order.BatchAllocateOrderRepo) *ClearOrderAndResultTask {
	return &ClearOrderAndResultTask{BatchAllocateOrderRepo: repo}
}

func (c ClearOrderAndResultTask) Name() string {
	return constant.TaskNameClearOrderAndResult
}

func (c ClearOrderAndResultTask) RpcHandle(ctx context.Context, args saturn.JobArgs) error {
	var (
		conf  = configutil.GetBatchAllocateConf()
		today = timeutil.GetLocalTime(ctx)
	)

	for i := conf.TableRetentionDays + 1; i < timeutil.MaxDays; i++ {
		clearDay := today.AddDate(0, 0, -i).Day()
		if today.Day() == clearDay {
			break
		}

		for maskProductID := range conf.MaskProductTableMapping {
			if err := c.BatchAllocateOrderRepo.ClearHoldOrderTab(ctx, maskProductID, clearDay); err != nil {
				logger.CtxLogErrorf(ctx, "clear hold order tab failed | maskProductID=%d, clearDay=%d", maskProductID, clearDay)
				return err
			}
		}

		if err := c.BatchAllocateOrderRepo.ClearOrderResultTab(ctx, clearDay); err != nil {
			logger.CtxLogErrorf(ctx, "clear order result tab failed | clearDay=%d", clearDay)
			return err
		}
	}

	return nil
}
