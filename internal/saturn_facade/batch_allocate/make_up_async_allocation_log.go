package batch_allocate

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/chassis-saturn-server/saturn"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing-common/client/algorithm_client"
	pb "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing-protocol/protocol/go"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lpsclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/batch_allocate"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rule_mode"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rulevolume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation"
	constant2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/forecast/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocpath"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/cacheutil/localcache"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/requestid"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/zip"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/kafkahelper"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/monitoring"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	jsoniter "github.com/json-iterator/go"
	"github.com/panjf2000/ants/v2"
	uuid "github.com/satori/go.uuid"
	"runtime"
	"strconv"
)

const (
	unmarshalErrCode            = -10000
	illegalErrCode              = -10001
	updateAllocationPathErrCode = -10002
	decoderErrCode              = -10003
	grtPoolSize                 = 32

	IsGetFirstAllocateLog                     = "is_get_first_allocate_log"
	SoftCriteriaNamePickupEfficiencyWhitelist = "Pickup Efficiency Whitelist"
)

var goroutinePool *ants.Pool

func init() {
	p, err := ants.NewPool(grtPoolSize)
	if err != nil {
		logger.LogErrorf("New goroutine pool fail | err=%v", err)
		panic(err)
	}

	goroutinePool = p
}

type MakeUpAsyncAllocationLog struct {
	allocationPathService allocpath.AllocationPathService
}

func NewMakeUpAsyncAllocationLog(
	allocationPathService allocpath.AllocationPathService) *MakeUpAsyncAllocationLog {
	return &MakeUpAsyncAllocationLog{
		allocationPathService: allocationPathService,
	}
}

func (m *MakeUpAsyncAllocationLog) Name() string {
	return constant.TaskNameMakeUpAsyncAllocationLog
}

func (m *MakeUpAsyncAllocationLog) MsgHandle(ctx context.Context, message *saturn.SaturnMessage) *saturn.SaturnReply {
	reply := &saturn.SaturnReply{}
	//1. unmarshal message.Msg
	body, err := zip.ZSTDDecompress(message.MsgText)
	if err != nil {
		msg := fmt.Sprintf("decoder err:%v", err)
		logger.CtxLogErrorf(ctx, msg)
		reply.Retcode = decoderErrCode
		reply.Message = msg
		return reply
	}
	logger.CtxLogInfof(ctx, "MakeUpAsyncAllocationLog|msg key:%s, msg text:%v", message.MsgKey, string(body))
	tempLog := &allocation.TempAllocationLog{}
	if err := objutil.UnmarshalBytes(&tempLog, body); err != nil {
		msg := fmt.Sprintf("unmarshal from msg text:%v, err:%v", message.MsgText, err)
		logger.CtxLogErrorf(ctx, msg)
		reply.Retcode = unmarshalErrCode
		reply.Message = msg
		return reply
	}
	if tempLog.Log == nil {
		msg := fmt.Sprintf("illegal msg text, log:%+v", tempLog.Log)
		logger.CtxLogErrorf(ctx, msg)
		reply.Retcode = illegalErrCode
		reply.Message = msg
		return reply
	}
	// single of batch
	if len(tempLog.Log.OrderListBytes) != 0 {
		// batch
		reply = m.batchUpdating(ctx, uuid.NewV4().String(), tempLog, reply) // nolint
	} else {
		// single
		reply = m.singleUpdating(ctx, tempLog, reply)
	}

	return reply
}

func (m *MakeUpAsyncAllocationLog) singleUpdating(ctx context.Context, tempLog *allocation.TempAllocationLog, reply *saturn.SaturnReply) *saturn.SaturnReply {
	if tempLog.RequestId == "" {
		msg := fmt.Sprintf("illegal request id, request id:%s", tempLog.RequestId)
		logger.CtxLogErrorf(ctx, msg)
		reply.Retcode = illegalErrCode
		reply.Message = msg
		return reply
	}
	requestID := tempLog.RequestId
	ctx = logger.NewLogContext(ctx, requestID)
	ctx = requestid.SetToCtx(ctx, requestID)
	logger.CtxLogInfof(ctx, "MakeUpAsyncAllocationLog|start to update allocation path")
	//2. call Data api to get hard check detail
	//3. make up full allocation log
	//4. send to allocation_path_empty_job
	if err := m.UpdateAllocationPath(ctx, tempLog.RequestId, tempLog.Log); err != nil {
		msg := fmt.Sprintf("update allocate path err:%v", err)
		logger.CtxLogErrorf(ctx, msg)
		reply.Retcode = updateAllocationPathErrCode
		reply.Message = msg
	}
	return reply
}

func (m *MakeUpAsyncAllocationLog) batchUpdating(ctx context.Context, requestID string, tempLog *allocation.TempAllocationLog, reply *saturn.SaturnReply) *saturn.SaturnReply {
	logger.CtxLogInfof(ctx, "start to batch updating")
	orderList := make([]*algorithm_client.OrderResultBo, 0)
	if err := objutil.UnmarshalBytes(&orderList, tempLog.Log.OrderListBytes); err != nil {
		logger.CtxLogErrorf(ctx, "unmarshal order list err:%v", err)
		reply.Retcode = unmarshalErrCode
		reply.Message = fmt.Sprintf("unmarshal order list err:%v", err)
		return reply
	}

	preHandleResult := m.preHandle(ctx, tempLog)

	//2. 装填参数后将一批压缩成bytes
	// goroutine并发处理发送kafka
	log := allocation.NewLog()
	log.BatchAllocationLog = &allocation.BatchAllocationLog{
		List: make([]allocation.BatchAllocationDetail, len(orderList)),
	}
	value, _ := localcache.Get(ctx, constant.MaskingProductRef, strconv.FormatInt(tempLog.Log.MaskingProductId, 10))
	maskingProductRef, _ := value.(*lpsclient.GetMaskingProductRefData)
	for i := 0; i < len(orderList); i++ {
		order := orderList[i]
		productParcelInfoList := make([]*pb.ProductParcelInfo, 0)
		if i < len(tempLog.Log.BatchAllocationLog.List) {
			productParcelInfoList = tempLog.Log.BatchAllocationLog.List[i].ProductParcelInfoList
		}
		if err := goroutinePool.Submit(func() {
			orderInfo := preHandleResult.OrderInfoMap[order.OrderId]
			newRequestID := fmt.Sprintf("%s-%d", requestID, order.OrderId)

			var existZoneCodeList, existRouteCodeList []string
			for _, zoneCode := range orderInfo.ZoneCodeList {
				if volume, ok := orderInfo.ZoneVolumeMap[int64(order.FulfillmentProductId)][zoneCode]; ok && volume != nil {
					existZoneCodeList = append(existZoneCodeList, zoneCode)
				}
			}
			for _, routeCode := range orderInfo.RouteCodeList {
				if volume, ok := orderInfo.RouteVolumeMap[int64(order.FulfillmentProductId)][routeCode]; ok && volume != nil {
					existRouteCodeList = append(existRouteCodeList, routeCode)
				}
			}
			// 放Order id进去log就好，不需要单独ctx
			detail := &allocation.BatchAllocationDetail{
				RequestId:                   newRequestID,
				OrderId:                     order.OrderId,
				MaskProductId:               int(preHandleResult.MaskProductID),
				FulfillmentProductId:        int(order.FulfillmentProductId),
				IsWms:                       false,
				RequestTime:                 timeutil.GetCurrentUnixMilliTimeStamp(ctx),
				AllocationMethod:            "Batch Allocate",
				BatchId:                     int(preHandleResult.BatchInfo.ID),
				BatchName:                   preHandleResult.AllocateRule.BatchRuleDetail.BatchSize.BatchSizeName,
				BatchTime:                   preHandleResult.BatchTime,
				BatchSize:                   int(preHandleResult.BatchInfo.LastOrderDbID-preHandleResult.BatchInfo.FirstOrderDbID) + 1,
				DestZoneCodeList:            existZoneCodeList,
				RouteCodeList:               existRouteCodeList,
				BatchAllocationDistribution: preHandleResult.Distributions,
				SoftRuleId:                  int(preHandleResult.AllocateRule.Id),
				VolumeRuleId:                int(preHandleResult.VolumeRule.ID),
				RuleType:                    preHandleResult.VolumeRule.RuleType.String(),
				ProductParcelInfoList:       productParcelInfoList,
			}

			// 是否要查询第一次调度结果日志，加个开关防止查询较慢影响整体消费速度
			if configutil.IsSwitch(ctx, IsGetFirstAllocateLog, newRequestID) {
				// 查询第一次调度日志
				firstAllocateLog, gErr := m.allocationPathService.GetDetailByOrderId(ctx, order.OrderId)
				if gErr != nil {
					monitoring.ReportError(ctx, monitoring.CatAllocationPath, monitoring.GetFirstAllocateLogError, fmt.Sprintf("get allocation detail err:%v", gErr))
					logger.CtxLogErrorf(ctx, "get allocation detail err:%v", gErr)
				}
				if firstAllocateLog == nil {
					monitoring.ReportSuccess(ctx, monitoring.CatAllocationPath, monitoring.FirstAllocateLogIsNil, "first allocate log is nil")
					logger.CtxLogErrorf(ctx, "first allocate log is nil")
				}
				// 从第一次调度日志取需要的字段
				if firstAllocateLog != nil {
					// 补充requestData
					detail.RequestData = firstAllocateLog.BasicInfo.RequestData
					// 补充shopGroupId
					detail.ShopGroupId = firstAllocateLog.SoftCriteriaResp.ShopGroupId
					// 补充hard criteria list
					detail.HardCriteriaListStr = allocation.ConvertToHardCriteria(ctx, firstAllocateLog.HardCriteriaResp.ProductToggle)
					// 补充allocate type
					detail.AllocateType = string(constant.AsyncBatchAllocate)
				}
				logger.CtxLogInfof(ctx, "complete first allocation log: firstAllocateLog: %s || detail: %s", objutil.JsonString(firstAllocateLog), objutil.JsonString(detail))
			}

			detail.Input = orderInfo.Inputs
			detail.HardInput = maskingProductRef.ComponentProductId
			detail.HardOutput = orderInfo.Inputs
			detail.Output = []int{int(order.FulfillmentProductId)}

			for j, p := range getProcessLog(order.ProcessLog, orderInfo.PickupEffWhitelistInfo) {
				// greedy -> 只看max volume + 运费
				// rebalance -> 看max volume + min volume
				// local search -> 看运费
				softCriteriaDetailList := make([]allocation.SoftCriteriaDetail, 0)
				for _, product := range orderInfo.Inputs {
					productNameData := preHandleResult.ProductNameMap[strconv.Itoa(product)]
					productName, _ := productNameData.(string)
					softDetail := allocation.SoftCriteriaDetail{
						Step:             "Step " + strconv.Itoa(j+1),
						SoftCriteriaName: p.SoftCriteriaName,
						InputProduct:     strconv.Itoa(product) + "-" + productName,
					}
					if objutil.ContainInt(p.Output, product) {
						softDetail.OutputProduct = strconv.Itoa(product) + "-" + productName
					}
					// title需要取涉及的并集
					titles := make([]string, 0)
					titles = append(titles, "Max Daily Limit of Country", "Min Batch Limit of Country", "System Volume of Product",
						"Max Daily Cod Limit of Country", "System Cod Volume of Product", "Max Daily Bulky Limit of Country", "System Bulky Volume of Product",
						"Max Daily HighValue Limit of Country", "System HighValue Volume of Product", "Max Daily Dg Limit of Country", "System Dg Volume of Product")
					for _, code := range existZoneCodeList {
						titles = append(titles, fmt.Sprintf("Max Limit of Dest Zone:%s", code))
						titles = append(titles, fmt.Sprintf("Min Limit of Dest Zone:%s", code))
						titles = append(titles, fmt.Sprintf("System Volume of Dest Zone:%s", code))
						titles = append(titles, fmt.Sprintf("Max Cod Limit of Dest Zone:%s", code))
						titles = append(titles, fmt.Sprintf("System Cod Volume of Dest Zone:%s", code))
						titles = append(titles, fmt.Sprintf("Max Bulky Limit of Dest Zone:%s", code))
						titles = append(titles, fmt.Sprintf("System Bulky Volume of Dest Zone:%s", code))
						titles = append(titles, fmt.Sprintf("Max HighValue Limit of Dest Zone:%s", code))
						titles = append(titles, fmt.Sprintf("System HighValue Volume of Dest Zone:%s", code))
						titles = append(titles, fmt.Sprintf("Max Dg Limit of Dest Zone:%s", code))
						titles = append(titles, fmt.Sprintf("System Dg Volume of Dest Zone:%s", code))
					}
					for _, code := range existRouteCodeList {
						titles = append(titles, fmt.Sprintf("Max Limit of Route:%s", code))
						titles = append(titles, fmt.Sprintf("Min Limit of Route:%s", code))
						titles = append(titles, fmt.Sprintf("System Volume of Route:%s", code))
						titles = append(titles, fmt.Sprintf("Max Cod Limit of Route:%s", code))
						titles = append(titles, fmt.Sprintf("System Cod Volume of Route:%s", code))
						titles = append(titles, fmt.Sprintf("Max Bulky Limit of Route:%s", code))
						titles = append(titles, fmt.Sprintf("System Bulky Volume of Route:%s", code))
						titles = append(titles, fmt.Sprintf("Max HighValue Limit of Route:%s", code))
						titles = append(titles, fmt.Sprintf("System HighValue Volume of Route:%s", code))
						titles = append(titles, fmt.Sprintf("Max Dg Limit of Route:%s", code))
						titles = append(titles, fmt.Sprintf("System Dg Volume of Route:%s", code))
					}
					titles = append(titles, "3PL Shipping Fee(Local Currency)")
					softDetail.Titles = titles

					// 装填values
					values := make([]string, 0)
					values = append(values, strconv.FormatInt(preHandleResult.CountryVolumeMap[int64(order.FulfillmentProductId)].MaxDailyLimit, 10))
					values = append(values, strconv.FormatInt(preHandleResult.CountryVolumeMap[int64(order.FulfillmentProductId)].MinBatchLimit, 10))
					values = append(values, strconv.FormatInt(preHandleResult.CountryVolumeMap[int64(order.FulfillmentProductId)].SystemVolume, 10))
					values = append(values, strconv.FormatInt(preHandleResult.CountryVolumeMap[int64(order.FulfillmentProductId)].MaxCodDailyLimit, 10))
					values = append(values, strconv.FormatInt(preHandleResult.CountryVolumeMap[int64(order.FulfillmentProductId)].SystemCodVolume, 10))
					values = append(values, strconv.FormatInt(preHandleResult.CountryVolumeMap[int64(order.FulfillmentProductId)].MaxBulkyDailyLimit, 10))
					values = append(values, strconv.FormatInt(preHandleResult.CountryVolumeMap[int64(order.FulfillmentProductId)].SystemBulkyVolume, 10))
					values = append(values, strconv.FormatInt(preHandleResult.CountryVolumeMap[int64(order.FulfillmentProductId)].MaxHighValueDailyLimit, 10))
					values = append(values, strconv.FormatInt(preHandleResult.CountryVolumeMap[int64(order.FulfillmentProductId)].SystemHighValueVolume, 10))
					values = append(values, strconv.FormatInt(preHandleResult.CountryVolumeMap[int64(order.FulfillmentProductId)].MaxDgDailyLimit, 10))
					values = append(values, strconv.FormatInt(preHandleResult.CountryVolumeMap[int64(order.FulfillmentProductId)].SystemDgVolume, 10))
					for _, code := range existZoneCodeList {
						values = append(values, strconv.FormatInt(orderInfo.ZoneVolumeMap[int64(order.FulfillmentProductId)][code].MaxDailyLimit, 10))
						values = append(values, strconv.FormatInt(orderInfo.ZoneVolumeMap[int64(order.FulfillmentProductId)][code].MinBatchLimit, 10))
						values = append(values, strconv.FormatInt(orderInfo.ZoneVolumeMap[int64(order.FulfillmentProductId)][code].SystemVolume, 10))
						values = append(values, strconv.FormatInt(orderInfo.ZoneVolumeMap[int64(order.FulfillmentProductId)][code].MaxCodDailyLimit, 10))
						values = append(values, strconv.FormatInt(orderInfo.ZoneVolumeMap[int64(order.FulfillmentProductId)][code].SystemCodVolume, 10))
						values = append(values, strconv.FormatInt(orderInfo.ZoneVolumeMap[int64(order.FulfillmentProductId)][code].MaxBulkyDailyLimit, 10))
						values = append(values, strconv.FormatInt(orderInfo.ZoneVolumeMap[int64(order.FulfillmentProductId)][code].SystemBulkyVolume, 10))
						values = append(values, strconv.FormatInt(orderInfo.ZoneVolumeMap[int64(order.FulfillmentProductId)][code].MaxHighValueDailyLimit, 10))
						values = append(values, strconv.FormatInt(orderInfo.ZoneVolumeMap[int64(order.FulfillmentProductId)][code].SystemHighValueVolume, 10))
						values = append(values, strconv.FormatInt(orderInfo.ZoneVolumeMap[int64(order.FulfillmentProductId)][code].MaxDgDailyLimit, 10))
						values = append(values, strconv.FormatInt(orderInfo.ZoneVolumeMap[int64(order.FulfillmentProductId)][code].SystemDgVolume, 10))
					}
					for _, code := range existRouteCodeList {
						values = append(values, strconv.FormatInt(orderInfo.RouteVolumeMap[int64(order.FulfillmentProductId)][code].MaxDailyLimit, 10))
						values = append(values, strconv.FormatInt(orderInfo.RouteVolumeMap[int64(order.FulfillmentProductId)][code].MinBatchLimit, 10))
						values = append(values, strconv.FormatInt(orderInfo.RouteVolumeMap[int64(order.FulfillmentProductId)][code].SystemVolume, 10))
						values = append(values, strconv.FormatInt(orderInfo.RouteVolumeMap[int64(order.FulfillmentProductId)][code].MaxCodDailyLimit, 10))
						values = append(values, strconv.FormatInt(orderInfo.RouteVolumeMap[int64(order.FulfillmentProductId)][code].SystemCodVolume, 10))
						values = append(values, strconv.FormatInt(orderInfo.RouteVolumeMap[int64(order.FulfillmentProductId)][code].MaxBulkyDailyLimit, 10))
						values = append(values, strconv.FormatInt(orderInfo.RouteVolumeMap[int64(order.FulfillmentProductId)][code].SystemBulkyVolume, 10))
						values = append(values, strconv.FormatInt(orderInfo.RouteVolumeMap[int64(order.FulfillmentProductId)][code].MaxHighValueDailyLimit, 10))
						values = append(values, strconv.FormatInt(orderInfo.RouteVolumeMap[int64(order.FulfillmentProductId)][code].SystemHighValueVolume, 10))
						values = append(values, strconv.FormatInt(orderInfo.RouteVolumeMap[int64(order.FulfillmentProductId)][code].MaxDgDailyLimit, 10))
						values = append(values, strconv.FormatInt(orderInfo.RouteVolumeMap[int64(order.FulfillmentProductId)][code].SystemDgVolume, 10))
					}
					values = append(values, strconv.FormatFloat(order.ShippingFee, 'f', -1, 64))
					softDetail.Values = values

					softCriteriaDetailList = append(softCriteriaDetailList, softDetail)
				}
				detail.Steps = append(detail.Steps, allocation.Step{SoftCriteriaDetailList: softCriteriaDetailList})
			}
			// steps转成soft_criteria_list字符串
			detail.SoftCriteriaListStr = objutil.JsonString(detail.Steps)
			logger.CtxLogInfof(ctx, "start to send batch allocation log with new request id:%s", newRequestID)
			sErr := m.sendBatchAllocationLog(ctx, newRequestID, detail)
			if sErr != nil {
				logger.CtxLogErrorf(ctx, "send batch allocation log err:%v", sErr)
			}
		}); err != nil {
			logger.CtxLogErrorf(ctx, "go routine pool send batch allocation log err:%v", err)
		}

	}

	return nil
}

func (m *MakeUpAsyncAllocationLog) UpdateAllocationPath(ctx context.Context, requestId string, allocationLog *allocation.Log) *srerr.Error {
	defer func() {
		if err := recover(); err != nil {
			var buf [4096]byte
			n := runtime.Stack(buf[:], false)
			errMsg := fmt.Sprintf("UpdateAllocationPath|[Recovery] panic recovered:\n%s\n%s", err, string(buf[:n]))
			logger.CtxLogErrorf(ctx, errMsg)
		}
	}()

	var (
		detail *allocpath.DetailData
		gErr   *srerr.Error
	)
	detail, gErr = m.allocationPathService.GetDetail(ctx, requestId)
	if gErr != nil {
		logger.CtxLogErrorf(ctx, "get allocation detail err:%v", gErr)
		return gErr
	}

	if detail == nil {
		return nil
	}
	//2. fill in soft criteria part
	allocationPath, _ := allocation.FillLog(ctx, allocationLog, detail)

	//3. send kafka with same rowkey again
	return m.SendAllocationLogToTask(ctx, allocationPath)
}

func (m *MakeUpAsyncAllocationLog) SendAllocationLogToTask(ctx context.Context, allocationLog *allocation.AllocationLog) *srerr.Error {
	if !configutil.GetAllocationLogConf(ctx) || allocationLog == nil {
		return nil
	}
	for i, logDetail := range allocationLog.List {
		if i == 0 && len(allocationLog.List) != 1 {
			continue
		}
		if err := m.sendUpdateAllocationLog(ctx, logDetail); err != nil {
			logger.CtxLogErrorf(ctx, "SendAllocationLogToTask Failed| error = %v", err)
		}
	}

	return nil
}

func (m *MakeUpAsyncAllocationLog) sendUpdateAllocationLog(ctx context.Context, allocationLog *allocation.LogDetail) *srerr.Error {
	// 1. no need transfer data to string

	allocationLogJson, jErr := jsoniter.Marshal(allocationLog)
	if jErr != nil {
		return srerr.With(srerr.TypeConvertErr, allocationLog, jErr)
	}

	// 2.zip data
	msgData := zip.ZSTDCompress(allocationLogJson)
	logger.CtxLogInfof(ctx, "SendAllocationLogToTask. msg:%s", string(allocationLogJson))

	// 3.send msg to kafka
	namespace := configutil.GetSaturnNamespaceConf(ctx).SmrNamespace
	if err := kafkahelper.DeliveryMessage(
		ctx, namespace, constant.TaskNameAllocationPathEmpty, msgData, nil, kafkahelper.UpdateAllocationPathType,
	); err != nil {
		msg := fmt.Sprintf("delivery new allocation path to kafka failed  err=%v", err)
		logger.CtxLogErrorf(ctx, msg)
		return err
	}

	return nil
}

func (m *MakeUpAsyncAllocationLog) sendBatchAllocationLog(ctx context.Context, requestID string, logDetail *allocation.BatchAllocationDetail) *srerr.Error {
	// 1. no need transfer data to string
	allocationLogJson := objutil.JsonBytes(logDetail)

	// 2.zip data
	msgData := zip.ZSTDCompress(allocationLogJson)
	logger.CtxLogInfof(ctx, "SendAllocationLogToTask. new request id:%s, msg:%s", requestID, string(allocationLogJson))

	// 3.send msg to kafka
	namespace := configutil.GetSaturnNamespaceConf(ctx).SmrNamespace
	if err := kafkahelper.DeliveryMessage(
		ctx, namespace, constant.TaskNameAllocationPathEmpty, msgData, nil, kafkahelper.BatchAllocationPathType,
	); err != nil {
		msg := fmt.Sprintf("delivery batch allocation path to kafka failed  err=%v", err)
		logger.CtxLogErrorf(ctx, msg)
		return err
	}

	return nil
}

func (m *MakeUpAsyncAllocationLog) preHandle(ctx context.Context, tempLog *allocation.TempAllocationLog) *preHandleInfo {
	preHandleResult := &preHandleInfo{
		Distributions: tempLog.Log.Distributions,
		MaskProductID: uint64(tempLog.Log.MaskingProductId),
	}

	// 1. basic info
	m.fillInByCache(ctx, preHandleResult, uint64(tempLog.Log.MaskingProductId), tempLog.Log.LastSecond)

	// 2. fill order info map
	orderInfoMap := make(map[uint64]allocation.AllocateOrderInfoForLog)
	_ = objutil.UnmarshalBytes(&orderInfoMap, tempLog.Log.OrderInfoMapBytes)
	preHandleResult.OrderInfoMap = orderInfoMap

	// 3. fill batch info
	batchInfo := &batch_allocate.BAOnlineBatchTab{}
	_ = objutil.UnmarshalBytes(&batchInfo, tempLog.Log.BatchInfoBytes)
	preHandleResult.BatchInfo = batchInfo

	// 4. fill country volume map
	countryVolumeMap := make(map[int64]*VolumeInfo, 0)
	_ = objutil.UnmarshalBytes(&countryVolumeMap, tempLog.Log.CountryVolumeMapBytes)
	preHandleResult.CountryVolumeMap = countryVolumeMap

	return preHandleResult
}

func (m *MakeUpAsyncAllocationLog) fillInByCache(ctx context.Context, preHandleInfo *preHandleInfo, maskProductID uint64, lastSecond int64) {
	// volume rule cache
	data, _ := localcache.Get(ctx, constant.BatchAllocateRuleVolume, strconv.FormatUint(maskProductID, 10))
	volumeRule, _ := data.(*rulevolume.MaskRuleVolumeTab)
	// allocate rule cache
	value, _ := localcache.Get(ctx, constant.MaskEffectiveRule, rule_mode.MplOrderRule.String()+strconv.FormatUint(maskProductID, 10)+":"+strconv.FormatInt(constant2.BatchAllocate, 10))
	allocateRule := value.(*rule.MaskAllocationRuleTab)
	allocateRule.UnmarshalBatchRule()
	// product name map
	productNameMap := localcache.AllItems(ctx, constant.ProductNameDict)
	// batch time
	batchTime := ""
	for _, timeUnit := range allocateRule.BatchRuleDetail.BatchSize.FixedTime.FixedTimeUnitList {
		if lastSecond >= timeUnit.StartTime && lastSecond <= timeUnit.EndTime {
			startHour := timeUnit.StartTime / 60 / 60
			startMinute := (timeUnit.StartTime - startHour*60*60) / 60
			endHour := timeUnit.EndTime / 60 / 60
			endMinute := (timeUnit.EndTime - endHour*60*60) / 60
			batchTime = fmt.Sprintf("%d:%d - %d:%d", startHour, startMinute, endHour, endMinute) //todo: break
		}
	}

	// fill
	preHandleInfo.VolumeRule = volumeRule
	preHandleInfo.AllocateRule = allocateRule
	preHandleInfo.BatchTime = batchTime
	preHandleInfo.ProductNameMap = productNameMap
}

type preHandleInfo struct {
	VolumeRule       *rulevolume.MaskRuleVolumeTab
	AllocateRule     *rule.MaskAllocationRuleTab
	BatchTime        string
	ProductNameMap   map[string]interface{}
	OrderInfoMap     map[uint64]allocation.AllocateOrderInfoForLog
	Distributions    []allocation.Distribution
	MaskProductID    uint64
	BatchInfo        *batch_allocate.BAOnlineBatchTab
	CountryVolumeMap map[int64]*VolumeInfo
}

type VolumeInfo struct {
	MaxDailyLimit          int64
	MaxCodDailyLimit       int64
	MaxBulkyDailyLimit     int64
	MaxHighValueDailyLimit int64
	MaxDgDailyLimit        int64
	MinBatchLimit          int64
	SystemVolume           int64
	SystemCodVolume        int64
	SystemBulkyVolume      int64
	SystemHighValueVolume  int64
	SystemDgVolume         int64
}

type processLog struct {
	SoftCriteriaName string
	Output           []int
}

func getProcessLog(algoProcessLogs []*algorithm_client.ProcessLogBo, pickupWhitelistInfo allocation.PickupEffWhitelistInfo) []*processLog {
	processLogs := make([]*processLog, 0, len(algoProcessLogs))

	if pickupWhitelistInfo.HitWhiteList {
		processLogs = append(processLogs, &processLog{
			SoftCriteriaName: SoftCriteriaNamePickupEfficiencyWhitelist,
			Output:           pickupWhitelistInfo.WhitelistProducts,
		})
	}

	for _, p := range algoProcessLogs {
		processLogs = append(processLogs, &processLog{
			SoftCriteriaName: p.AlgoName,
			Output:           []int{int(p.AlgoResult)},
		})
	}

	return processLogs
}
