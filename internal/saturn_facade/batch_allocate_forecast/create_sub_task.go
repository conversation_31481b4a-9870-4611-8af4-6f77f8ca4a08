package batch_allocate_forecast

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis-saturn-server/saturn"
	internal_constant "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/forecast/service"
)

type CreateBASubTask struct {
	AllocateForecastTaskConfigService service.AllocateForecastTaskConfigService
}

func NewCreateBASubTask(service service.AllocateForecastTaskConfigService) *CreateBASubTask {
	return &CreateBASubTask{AllocateForecastTaskConfigService: service}
}

func (s *CreateBASubTask) Name() string {
	return internal_constant.TaskNameCreateBASubTask
}

func (s *CreateBASubTask) RpcHandle(ctx context.Context, args saturn.JobArgs) error {
	return s.AllocateForecastTaskConfigService.CreateBASubTaskByTaskConfig(ctx)
}
