package batch_allocate_forecast

import (
	"context"
	"errors"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/chassis-saturn-server/saturn"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/forecast/repo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/allocation"
	constant2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/forecast/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/redisutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/requestid"
	jsoniter "github.com/json-iterator/go"
	uuid "github.com/satori/go.uuid"
	"time"
)

const (
	oneDay = time.Hour * 24
)

type BAForecastToolProgressImpl struct {
	MainTaskRepo repo.AllocateForecastTaskConfigRepo
	SubTaskRepo  repo.BatchAllocateForecastRepo
	UnitFeeRepo  repo.BatchUnitFeeResultRepo
}

func NewBAForecastToolProgressImpl(
	mainTaskRepo repo.AllocateForecastTaskConfigRepo,
	subTaskRepo repo.BatchAllocateForecastRepo,
	unitFeeRepo repo.BatchUnitFeeResultRepo) *BAForecastToolProgressImpl {
	return &BAForecastToolProgressImpl{
		MainTaskRepo: mainTaskRepo,
		SubTaskRepo:  subTaskRepo,
		UnitFeeRepo:  unitFeeRepo,
	}
}

func (i *BAForecastToolProgressImpl) Name() string {
	return constant.TaskBAForecastToolProgress
}

// 定时任务: 定时刷新batch allocate forecast tool的进度
// 1. 进度 = 已完成批次数 / 总批次数
// 2. 局限：目前只能兼容fix by time类型的
func (i *BAForecastToolProgressImpl) RpcHandle(ctx context.Context, args saturn.JobArgs) error {
	requestID := uuid.NewV4().String() // nolint
	ctx = logger.NewLogContext(ctx, requestID)
	ctx = requestid.SetToCtx(ctx, requestID)

	// 1. 获取progress状态的batch allocate forecast task
	taskList, gErr := i.MainTaskRepo.GetForecastTaskConfigByCondition(ctx, map[string]interface{}{
		"task_status = ?":       constant2.TaskConfigStatusProcess,
		"allocation_method = ?": constant2.BatchAllocate,
	})

	if gErr != nil {
		logger.CtxLogErrorf(ctx, "get process batch task err:%v", gErr)
		return errors.New("get process batch task err")
	}

	// 2. 遍历task。判断split rule中是否只包含fix by time，不是则打印日志并返回
	for _, task := range taskList {
		// 获取split rule
		var batchSizeList []*allocation.BatchSize
		if err := jsoniter.Unmarshal(task.BatchSizeList, &batchSizeList); err != nil {
			logger.CtxLogErrorf(ctx, "task:%v, unmarshal from task batch size rule err:%v", task.Id, err)
			continue
		}
		// 判断split rule是否only fix by time
		var (
			total, completeBatches int64
			batchSizePass          = true
		)
		for _, batchSize := range batchSizeList {
			if batchSize.BatchSizeType != constant2.FixedByTime {
				logger.CtxLogErrorf(ctx, "task：%v, not only fix by time, skip this task", task.Id)
				batchSizePass = false
				break
			}

			// 3. 根据fix by time算出total batches
			for _, timeUnit := range batchSize.FixedTime.FixedTimeUnitList {
				total += (timeUnit.EndTime - timeUnit.StartTime) / timeUnit.TimeRange
			}
		}
		if !batchSizePass {
			logger.CtxLogInfof(ctx, "not pass batch size check")
			continue
		}
		if total == 0 {
			logger.CtxLogInfof(ctx, "total is 0, skip this task:%v", task.Id)
			continue
		}

		// 4. 获取task当前已完成batch
		// 获取sub task
		subTaskList, gErr := i.SubTaskRepo.GetSubTaskList(ctx, uint64(task.Id))
		if gErr != nil {
			logger.CtxLogErrorf(ctx, "task:%v, get sub task list err:%v", task.Id, gErr)
			return errors.New("get sub task list err")
		}
		// 根据sub task获取last batch
		for _, subTask := range subTaskList {
			unitFee, gErr := i.UnitFeeRepo.GetLastBatchBySubTask(ctx, subTask.Id)
			if gErr != nil {
				logger.CtxLogErrorf(ctx, "task:%v, get last batch unit fee err:%v", task.Id, gErr)
				continue
			}
			completeBatches += int64(unitFee.BatchNum)
		}
		ratio := float64(completeBatches) / float64(total)
		logger.CtxLogInfof(ctx, "complete batches:%d, total:%d, ratio:%v", completeBatches, total, ratio)

		// 5. 计算进度百分比，并更新到task中
		if err := redisutil.Set(ctx, fmt.Sprintf(constant.TaskScheduleRatio, task.Id), ratio, oneDay); err != nil {
			logger.CtxLogErrorf(ctx, "set task progress into redis err:%v", err)
			return err
		}
	}

	return nil
}
