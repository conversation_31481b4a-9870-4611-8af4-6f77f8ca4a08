package batch_allocate_forecast_unit_chain

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/chargeclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lpsclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/forecast/repo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/forecast_unit"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/model"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rulevolume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/masking_forecast/forecast_volume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volume_counter"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/saturn_facade/batch_entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/saturn_facade/warning"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/allocation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/batch_allocate"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/whitelist"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	jsoniter "github.com/json-iterator/go"
	"go.uber.org/atomic"
	"sync"
)

type BAForecastUnitExecutor struct {
	BatchAllocateForecastRepo           repo.BatchAllocateForecastRepo
	SplittingRuleServer                 model.SplittingRuleServer
	BAForecastVolumeRepo                rulevolume.BatchAllocateForecastVolumeRepo
	LpsApi                              lpsclient.LpsApi
	RateApi                             chargeclient.ChargeApi
	AllocateForecastTaskConfigRepo      repo.AllocateForecastTaskConfigRepo
	LocationVolumeService               forecast_volume.ForecastLocationVolumeService
	BatchForecastUnitService            forecast_unit.BatchForecastUnitService
	BatchUnitTargetResultRepo           repo.BatchUnitTargetResultRepo
	BatchUnitFeeResultRepo              repo.BatchUnitFeeResultRepo
	BatchAllocateForecastUnitResultRepo repo.BatchAllocateForecastUnitResultRepo
	BatchMinuteOrderConfService         batch_allocate.BatchMinuteOrderConfService
	ShopWhitelistService                whitelist.ShopWhitelistService
}

func NewBAForecastUnitExecutor(
	BatchAllocateForecastRepo repo.BatchAllocateForecastRepo,
	SplittingRuleServer model.SplittingRuleServer,
	BAForecastVolumeRepo rulevolume.BatchAllocateForecastVolumeRepo,
	LpsApi lpsclient.LpsApi,
	RateApi chargeclient.ChargeApi,
	AllocateForecastTaskConfigRepo repo.AllocateForecastTaskConfigRepo,
	LocationVolumeService forecast_volume.ForecastLocationVolumeService,
	BatchForecastUnitService forecast_unit.BatchForecastUnitService,
	batchUnitTargetResultRepo repo.BatchUnitTargetResultRepo,
	batchUnitFeeResultRepo repo.BatchUnitFeeResultRepo,
	batchAllocateForecastUnitResultRepo repo.BatchAllocateForecastUnitResultRepo,
	BatchMinuteOrderConfService batch_allocate.BatchMinuteOrderConfService,
	ShopWhitelistService whitelist.ShopWhitelistService,
) *BAForecastUnitExecutor {
	return &BAForecastUnitExecutor{
		BatchAllocateForecastRepo:           BatchAllocateForecastRepo,
		SplittingRuleServer:                 SplittingRuleServer,
		BAForecastVolumeRepo:                BAForecastVolumeRepo,
		LpsApi:                              LpsApi,
		RateApi:                             RateApi,
		AllocateForecastTaskConfigRepo:      AllocateForecastTaskConfigRepo,
		LocationVolumeService:               LocationVolumeService,
		BatchForecastUnitService:            BatchForecastUnitService,
		BatchUnitTargetResultRepo:           batchUnitTargetResultRepo,
		BatchUnitFeeResultRepo:              batchUnitFeeResultRepo,
		BatchAllocateForecastUnitResultRepo: batchAllocateForecastUnitResultRepo,
		BatchMinuteOrderConfService:         BatchMinuteOrderConfService,
		ShopWhitelistService:                ShopWhitelistService,
	}
}

// todo:SSCSMR-1698: 顺序append chain
func (b *BAForecastUnitExecutor) Execute(ctx context.Context, tab *model.BatchAllocateForecastUnitTab) *srerr.Error {
	//1.定义result manager
	resultManager := NewResultManagerImpl(b.BatchUnitTargetResultRepo, b.BatchUnitFeeResultRepo, b.BatchAllocateForecastUnitResultRepo)

	updater := NewForecastUnitUpdateVolumeImpl(b.BatchForecastUnitService)
	updater.SetNext(resultManager)

	//2.定义forecast starter && 依赖注入result manager
	forecastStarter := NewForecastUnitStarterImpl(b.BatchAllocateForecastRepo, b.ShopWhitelistService)
	forecastStarter.SetNext(updater)

	//3.定义volume finder && 依赖注入forecast starter
	volumeFinder := NewPreparerImpl(b.BAForecastVolumeRepo, b.LpsApi, b.RateApi, b.BatchMinuteOrderConfService, b.ShopWhitelistService)
	volumeFinder.SetNext(forecastStarter)

	//4.定义order collector && 依赖注入 volume finder
	orderCollector := NewOrderCollectorImpl(b.SplittingRuleServer)
	orderCollector.SetNext(volumeFinder)

	//5.定义splitting rule matcher && 依赖注入order collector
	spRuleMatcher := NewSplittingRuleMatcherImpl(b.BatchAllocateForecastRepo)
	spRuleMatcher.SetNext(orderCollector)

	//6.定义forecast unit finder && 依赖注入 splitting rule matcher
	forecastUnitFinder := NewForecastUnitFinderImpl(b.BatchAllocateForecastRepo)
	forecastUnitFinder.SetNext(spRuleMatcher)

	//7.启动
	mainTask, gErr := b.AllocateForecastTaskConfigRepo.GetForecastTaskConfigById(ctx, int64(tab.BatchAllocateForecastId))
	if gErr != nil {
		logger.CtxLogErrorf(ctx, "BAForecastUnitExecutor|get main task:%v, err:%v", tab.BatchAllocateForecastId, gErr)
		return gErr
	}

	var allocationRule allocation.BatchAllocationRuleConfig
	if err := jsoniter.Unmarshal(mainTask.BatchAllocationRuleConfig, &allocationRule); err != nil {
		logger.CtxLogErrorf(ctx, "BAForecastUnitExecutor|unmarshal allocation rule:%v, err:%v", mainTask.BatchAllocationRuleConfig, err)
		return srerr.With(srerr.AllocationRuleError, mainTask.BatchAllocationRuleConfig, err)
	}

	inv := &Invocation{
		BatchUnitTask:                tab,
		BatchAllocateForecastTask:    mainTask,
		AllocationRule:               allocationRule,
		BatchAllocateForecastCounter: volume_counter.NewBatchAllocateForecastCounter(),
		batchProductShippingFeeMap:   make(map[uint64]*sync.Map, 0),
		ExtraInfo:                    &batch_entity.ExtraInfo{},
		shouldClosed: &warning.ShouldCloseObj{
			Once:        sync.Once{},
			ShouldClose: atomic.NewBool(false),
		},
		HoldingTimeMap: &sync.Map{},
		feeMapLock:     &sync.RWMutex{},
	}
	inv.SetAllowFillIn(true)
	logger.CtxLogInfof(ctx, "BAForecastUnitExecutor|set allow fill in true")
	return forecastUnitFinder.ExecuteJob(ctx, inv)
}
