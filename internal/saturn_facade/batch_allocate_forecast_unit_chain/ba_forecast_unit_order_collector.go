package batch_allocate_forecast_unit_chain

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/model"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
)

const (
	orderCollectorName = "Order collector"
)

type OrderCollectorImpl struct {
	next                Job
	SplittingRuleServer model.SplittingRuleServer
}

func NewOrderCollectorImpl(SplittingRuleServer model.SplittingRuleServer) *OrderCollectorImpl {
	return &OrderCollectorImpl{
		SplittingRuleServer: SplittingRuleServer,
	}
}

func (o *OrderCollectorImpl) ExecuteJob(ctx context.Context, inv *Invocation) *srerr.Error {
	/*	1.根据splitting rule拆分批次
		2.记录每批的hold单时间
		3.获取每批订单数据
		3.将批次任务传入channel中，将channel传递到下一模块
	*/

	collectCondition := model.CollectCondition{
		MaskProductId: uint64(inv.BatchAllocateForecastTask.MaskingProductID),
		StartTimeUnix: inv.BatchUnitTask.SplitDateUnix, //将切片日期填充，fix by quantity和fix by time有不同的逻辑
	}
	inv.ForecastUnitStartTime = timeutil.GetCurrentUnixTimeStamp(ctx)
	splitResultsChan, sErr := o.SplittingRuleServer.SplitBatchesByType(ctx, inv.SplittingRule, inv.HoldingTimeMap, inv.shouldClosed, model.ModuleBAForecast, collectCondition)
	if sErr != nil {
		logger.CtxLogErrorf(ctx, "OrderCollectorImpl|split orders err:%v", sErr)
		return sErr
	}
	inv.SplitResultsChan = splitResultsChan

	return o.next.ExecuteJob(ctx, inv)
}

func (o *OrderCollectorImpl) SetNext(job Job) {
	o.next = job
}

func (o *OrderCollectorImpl) OrderCollectorJobName() string {
	return orderCollectorName
}
