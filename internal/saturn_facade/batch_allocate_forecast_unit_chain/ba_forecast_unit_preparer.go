package batch_allocate_forecast_unit_chain

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing-common/client/algorithm_client"
	pb "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing-protocol/protocol/go"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/chargeclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/chargeclient/chargeentity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lpsclient"
	constant2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/model"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rulevolume"
	batch_allocate2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/batch_allocate"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/batch_allocate/batch_minute_order_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/productpriority/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volume_counter"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/saturn_facade/batch_entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/allocation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/batch_allocate"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/forecast/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/parcel_type_definition"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/whitelist"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/envvar"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/monitoring"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	jsoniter "github.com/json-iterator/go"
	"github.com/panjf2000/ants/v2"
	"golang.org/x/time/rate"
	"math"
	"runtime"
	"strconv"
	"strings"
	"sync"
)

const (
	preparerName       = "Parameters preparer"
	noShippingFee      = -1
	panicMsgSize       = 4096
	defaultShopGroupId = -1
)

type PreparerImpl struct {
	next                        Job
	limiter                     *rate.Limiter
	shippingFeeConfigList       []model.ShippingFeeConfig
	BAForecastVolumeRepo        rulevolume.BatchAllocateForecastVolumeRepo
	LpsApi                      lpsclient.LpsApi
	RateClient                  chargeclient.ChargeApi
	BatchMinuteOrderConfService batch_allocate.BatchMinuteOrderConfService
	ShopWhitelistService        whitelist.ShopWhitelistService
}

func NewPreparerImpl(BAForecastVolumeRepo rulevolume.BatchAllocateForecastVolumeRepo, LpsApi lpsclient.LpsApi, RateClient chargeclient.ChargeApi, BatchMinuteOrderConfService batch_allocate.BatchMinuteOrderConfService, ShopWhitelistService whitelist.ShopWhitelistService) *PreparerImpl {
	conf := configutil.GetBatchAllocateForecastConf()
	limiter := rate.NewLimiter(rate.Limit(conf.RateLimit), conf.RateBurst)
	return &PreparerImpl{
		limiter:                     limiter,
		BAForecastVolumeRepo:        BAForecastVolumeRepo,
		LpsApi:                      LpsApi,
		RateClient:                  RateClient,
		BatchMinuteOrderConfService: BatchMinuteOrderConfService,
		ShopWhitelistService:        ShopWhitelistService,
	}
}

func (p *PreparerImpl) ExecuteJob(ctx context.Context, inv *Invocation) *srerr.Error {
	/*
		1.加载forecast volume
		2.调lps硬性校验，计算出可用渠道
		3.调finance计算运费
		4.填充volume
			4.1装载整体的target volume、capacity volume
			4.2分批获取传进来的订单数据，按批次加载target volume、capacity volume
	*/
	//加载shipping fee config
	mainTask := inv.BatchAllocateForecastTask
	ruleBytes := mainTask.BatchAllocationRuleConfig
	priorityBytes := mainTask.ProductPriorityConfigs
	var (
		ruleConfig model.BatchAllocationRuleConfig
		priorities []*allocation.ProductPriorityConfig
	)
	if err := jsoniter.Unmarshal(ruleBytes, &ruleConfig); err != nil {
		logger.CtxLogErrorf(ctx, "PreparerImpl|unmarshal batch rule config:%v, err:%v", string(ruleBytes), err)
		return srerr.With(srerr.TypeConvertErr, nil, err)
	}
	p.shippingFeeConfigList = ruleConfig.ShippingFeeConfigList

	if len(priorityBytes) != 0 {
		if err := jsoniter.Unmarshal(priorityBytes, &priorities); err != nil {
			logger.CtxLogErrorf(ctx, "PreparerImpl|unmarshal batch priority:%v, err:%v", string(ruleBytes), err)
			return srerr.With(srerr.TypeConvertErr, nil, err)
		}
	}

	//1 加载整体volume
	volumeTabs, gErr := p.BAForecastVolumeRepo.GetForecastVolume(ctx, map[string]interface{}{
		"batch_allocate_forecast_id = ?": inv.BatchUnitTask.BatchAllocateForecastId,
	})
	if gErr != nil {
		logger.CtxLogErrorf(ctx, "PreparerImpl|get forecast volume, err:%v", gErr)
		return gErr
	}

	//2.准备起始的target，capacity
	targetBos := p.GetCapacityAndTarget(ctx, ruleConfig, volumeTabs)

	//3.准备batch min volume配置
	batchMinuteConf, gErr := p.BatchMinuteOrderConfService.GetBatchMinuteOrderConf(ctx, allocation.GetMinuteOrderConfReq{
		MaskProductId: uint64(mainTask.MaskingProductID),
		ConfStatus:    batch_minute_order_conf.ConfActive,
		ConfType:      batch_allocate.ConvertUseForCampaign(ruleConfig.UseForCampaign),
	})
	if gErr != nil {
		logger.CtxLogErrorf(ctx, "PreparerImpl|get batch minute conf, err:%v", gErr)
		return gErr
	}
	batchMinuteVolumeMap := make(map[int64]float64, 0)
	for i := 0; i < len(batchMinuteConf.List); i++ {
		batchMinute := batchMinuteConf.List[i]
		ratio, err := strconv.ParseFloat(batchMinute.Ratio, 64)
		if err != nil {
			logger.CtxLogErrorf(ctx, "PreparerImpl|parse ratio:%v err:%v", batchMinute.Ratio, err)
			continue
		}
		batchMinuteVolumeMap[batchMinute.Minute] = ratio
	}
	if len(batchMinuteVolumeMap) == 0 {
		logger.CtxLogErrorf(ctx, "PreparerImpl| get empty batch minute volume")
		return srerr.New(srerr.DatabaseErr, nil, "get empty batch minute volume")
	}
	batchMinuteVolumeList := make([]float64, len(batchMinuteVolumeMap))
	for i := 0; i < len(batchMinuteVolumeMap); i++ {
		batchMinuteVolumeList[i] = batchMinuteVolumeMap[int64(i+1)]
	}

	size := configutil.GetBatchAllocateForecastConf().ChannelSize
	batchAllocateReqChan := make(chan *batch_entity.BatchAllocateReq, size)
	go func(chan *batch_entity.BatchAllocateReq) {
		defer func() {
			logger.CtxLogInfof(ctx, "PreparerImpl| req chan closed")
			close(batchAllocateReqChan)
		}()
		defer func() {
			if err := recover(); err != nil {
				inv.shouldClosed.Set(true)
				var buf [panicMsgSize]byte
				n := runtime.Stack(buf[:], false)
				errMsg := fmt.Sprintf("[Recovery|PreparerImpl] panic recovered:\n%v \n%v", err, string(buf[:n]))
				logger.CtxLogErrorf(ctx, errMsg)
			}
		}()

		var (
			algoConf       = configutil.GetAlgoConfig()
			baForecastConf = configutil.GetBatchAllocateForecastConf()
		)

		//3. 按批次遍历，批次channel缓存不会很大
		var batchNo uint64
		for tempResult := range inv.SplitResultsChan {
			//需要等待上一批结束才能继续状态 //todo:SSCSMR-1698:防呆？比如最多等1分钟之类的；加个or 条件？
			for !inv.GetAllowFillIn() {
				if inv.shouldClosed.Get() {
					break
				}
				continue
			}
			if inv.shouldClosed.Get() {
				break
			}
			//定义批次信息
			batchNo += 1
			fillStartTime := timeutil.GetCurrentUnixTimeStamp(ctx)
			logger.CtxLogInfof(ctx, "set allow fill in false")
			inv.SetAllowFillIn(false)
			//初始化该批次的历史订单shipping fee
			inv.feeMapLock.Lock()
			inv.batchProductShippingFeeMap[batchNo] = &sync.Map{}
			inv.feeMapLock.Unlock()

			orderEntityList := tempResult
			//获取批次的order bo && order location map
			startTime := timeutil.GetCurrentUnixTimeStamp(ctx)
			orderBos, orderLocMap, lastSecond, gErr := p.GetOrderBosAndLocInfo(ctx, objutil.Int2Bool(mainTask.RunSoftRuleOnlyToggle), objutil.Int2Bool(mainTask.RecalculateShippingFeeToggle), orderEntityList, volumeTabs, inv.batchProductShippingFeeMap[batchNo], inv.BatchUnitTask.SplitDateUnix, priorities, inv.AllocationRule)
			if gErr != nil {
				logger.CtxLogErrorf(ctx, "GetOrderBosAndLocInfo|err: %v", gErr)
				continue
			}
			logger.CtxLogInfof(ctx, "batch no:%v, batch size:%v, prepare time:%v", batchNo, len(orderBos), timeutil.GetCurrentUnixTimeStamp(ctx)-startTime)

			//todo:SSCSMR-2724: 挪到starter，在preparer并发准备参数，在starter再串行填充运力
			//填充target value。每一批的数据都要基于上一批次
			//SSCSMR-2557:填充batch min，从Apollo获取
			counter := inv.BatchAllocateForecastCounter
			fillInTarget(ctx, counter, targetBos, lastSecond, batchMinuteVolumeList)
			pickupEffBudget := batch_allocate2.GetBatchMinTarget(lastSecond, batchMinuteVolumeList, uint32(ruleConfig.PickupEfficiencyDetail.Budget-counter.GetPickupEffUsedBudget()))
			batchAllocateReqBo := &algorithm_client.BatchAllocateReqBo{
				BatchNo:      batchNo,
				SoftRuleId:   inv.SplittingRule.BatchAllocateSubTaskId,
				VolumeRuleId: inv.SplittingRule.BatchAllocateSubTaskId,
				TargetVolume: targetBos,
				AlgoConfig: &algorithm_client.AlgoConfigBo{
					LocalSearchSwitch:     algoConf.LocalSearchSwitch,
					LocalSearchTime:       algoConf.LocalSearchTime,
					EarlyStopIter:         algoConf.EarlyStopIter,
					PickupEffEnable:       ruleConfig.PickupEfficiencyEnabled,
					PickupEffUrl:          baForecastConf.PickupEffUrl,
					PickupEffActualBudget: uint64(pickupEffBudget),
					PickupEffSLA:          uint64(baForecastConf.PickupEffSLA),
				}, //可配置项，动态调整
				Orders:                  orderBos,
				ShopFulfillmentProducts: counter.GetShopFulfillmentProducts(),
			}
			batchAllocateReq := &batch_entity.BatchAllocateReq{
				BatchUnitId: batchNo,
				BAReq:       batchAllocateReqBo,
				OrderInfo:   orderLocMap, //SSCSMR-1698:获取location id
			}
			logger.CtxLogInfof(ctx, "preparation time:%v", timeutil.GetCurrentUnixTimeStamp(ctx)-fillStartTime)
			batchAllocateReqChan <- batchAllocateReq
		}
	}(batchAllocateReqChan)

	inv.BatchAllocateReqChan = batchAllocateReqChan

	return p.next.ExecuteJob(ctx, inv)
}

func (p *PreparerImpl) SetNext(job Job) {
	p.next = job
}

func (p *PreparerImpl) VolumeFinderJobName() string {
	return preparerName
}

func (p *PreparerImpl) GetCapacityAndTarget(ctx context.Context, ruleConfig model.BatchAllocationRuleConfig, volumeTabs []rulevolume.BatchAllocateForecastVolumeTab) []*algorithm_client.ProductTargetVolumeBo {
	//获取target bo
	targetBos := getTargetBos(ctx, ruleConfig, volumeTabs)

	return targetBos
}

// 获取预期运力
// 出参：region target list， zone target list
func getTargetBos(ctx context.Context, ruleConfig model.BatchAllocationRuleConfig, volumeTabs []rulevolume.BatchAllocateForecastVolumeTab) []*algorithm_client.ProductTargetVolumeBo {
	//准备供调度sdk的capacity volume
	var targetBos []*algorithm_client.ProductTargetVolumeBo

	//判断target 类型
	//开启了region维度, 装填region维度target
	if ruleConfig.CountryVolumeEnabled {
		countryTargetBos := make([]*algorithm_client.ProductTargetVolumeBo, len(ruleConfig.CountryVolumeDetailList))
		for index, countryVolume := range ruleConfig.CountryVolumeDetailList {
			//初始化target volume详情
			targetVolumeDetail := &algorithm_client.TargetVolumeDetailBo{
				TargetVolumeCode:     envvar.GetCIDLower(),
				DailyMaxTar:          uint32(batch_allocate2.FillVolume(ctx, countryVolume.MaxDailyVolume, batch_allocate2.VolumeTypeMin)),
				DailyMinTar:          uint32(batch_allocate2.FillVolume(ctx, countryVolume.MinDailyVolume, batch_allocate2.VolumeTypeMax)),
				DailyCodMaxTar:       uint32(batch_allocate2.FillVolume(ctx, countryVolume.MaxDailyCodVolume, batch_allocate2.VolumeTypeMax)),
				DailyBulkyMaxTar:     uint32(batch_allocate2.FillVolume(ctx, countryVolume.MaxDailyBulkyVolume, batch_allocate2.VolumeTypeMax)),
				DailyHighValueMaxTar: uint32(batch_allocate2.FillVolume(ctx, countryVolume.MaxDailyHighValueVolume, batch_allocate2.VolumeTypeMax)),
				DailyDgMaxTar:        uint32(batch_allocate2.FillVolume(ctx, countryVolume.MaxDailyDgVolume, batch_allocate2.VolumeTypeMax)),
			}
			//将target volume详情组装到target volume bo中
			targetVolumeDetailList := make([]*algorithm_client.TargetVolumeDetailBo, 1)
			targetVolumeDetailList[0] = targetVolumeDetail
			targetVolumeBo := &algorithm_client.TargetVolumeBo{
				TargetType:         rulevolume.BaVolumeTypeRegion,
				TargetVolumeDetail: targetVolumeDetailList,
			}
			//将target volume bo组装到country target bo list中
			targetVolumeList := make([]*algorithm_client.TargetVolumeBo, 1)
			targetVolumeList[0] = targetVolumeBo
			countryTargetBo := &algorithm_client.ProductTargetVolumeBo{
				FulfillmentProductId: uint64(countryVolume.ProductId),
				TargetVolume:         targetVolumeList,
			}
			countryTargetBos[index] = countryTargetBo
		}
		targetBos = countryTargetBos
	}
	if ruleConfig.ZoneRouteVolumeEnabled { //开启了zone维度, 装填zone维度target
		var (
			productZoneCodeTargetMap  = make(map[uint64]map[string]batch_allocate2.Target)
			productRouteCodeTargetMap = make(map[uint64]map[string]batch_allocate2.Target)
		)
		for _, tab := range volumeTabs {
			//不装填capacity
			if tab.VolumeScene != constant.VolumeSceneTarget {
				continue
			}
			switch tab.VolumeType {
			case constant.VolumeTypeZone:
				fillTargetMap(ctx, productZoneCodeTargetMap, tab.ComponentProductID, tab.ZoneCode, tab)
			case constant.VolumeTypeRoute:
				fillTargetMap(ctx, productRouteCodeTargetMap, tab.ComponentProductID, tab.RouteCode, tab)
			}
		}
		zoneTargetBos := buildTargetBos(productZoneCodeTargetMap, rulevolume.BaVolumeTypeZone)
		routeTargetBos := buildTargetBos(productRouteCodeTargetMap, rulevolume.BaVolumeTypeRoute)
		targetBos = append(targetBos, zoneTargetBos...)
		targetBos = append(targetBos, routeTargetBos...)
	}

	return targetBos
}

// fillTargetMap 填充targetMap的Min/Max Volume信息
func fillTargetMap(
	ctx context.Context, targetMap map[uint64]map[string]batch_allocate2.Target, productID uint64, zoneRouteCode string,
	forecastVolume rulevolume.BatchAllocateForecastVolumeTab,
) {
	if _, ok := targetMap[productID]; !ok {
		targetMap[productID] = make(map[string]batch_allocate2.Target)
	}

	targetMap[productID][zoneRouteCode] = batch_allocate2.Target{
		MinVolume:          uint32(batch_allocate2.FillVolume(ctx, forecastVolume.MinVolumeValue, batch_allocate2.VolumeTypeMin)),
		MaxVolume:          uint32(batch_allocate2.FillVolume(ctx, forecastVolume.MaxVolumeValue, batch_allocate2.VolumeTypeMax)),
		MaxCodVolume:       uint32(batch_allocate2.FillVolume(ctx, forecastVolume.MaxCodVolumeValue, batch_allocate2.VolumeTypeMax)),
		MaxHighValueVolume: uint32(batch_allocate2.FillVolume(ctx, forecastVolume.MaxHighValueVolumeValue, batch_allocate2.VolumeTypeMax)),
		MaxBulkyVolume:     uint32(batch_allocate2.FillVolume(ctx, forecastVolume.MaxBulkyVolumeValue, batch_allocate2.VolumeTypeMax)),
		MaxDgVolume:        uint32(batch_allocate2.FillVolume(ctx, forecastVolume.MaxDgVolumeValue, batch_allocate2.VolumeTypeMax)),
	}
}

// buildTargetBos 构建Batch Allocate的Target Volume数据
func buildTargetBos(productCodeMap map[uint64]map[string]batch_allocate2.Target, targetType uint8) []*algorithm_client.ProductTargetVolumeBo {
	var targetBos []*algorithm_client.ProductTargetVolumeBo

	for product, codeTargetMap := range productCodeMap {
		targetVolumeDetailList := make([]*algorithm_client.TargetVolumeDetailBo, 0, len(codeTargetMap))

		for code, target := range codeTargetMap {
			targetVolumeDetailList = append(targetVolumeDetailList, &algorithm_client.TargetVolumeDetailBo{
				TargetVolumeCode:     code,
				DailyMaxTar:          target.MaxVolume,
				DailyMinTar:          target.MinVolume,
				DailyCodMaxTar:       target.MaxCodVolume,
				DailyHighValueMaxTar: target.MaxHighValueVolume,
				DailyBulkyMaxTar:     target.MaxBulkyVolume,
				DailyDgMaxTar:        target.MaxDgVolume,
			})
		}

		targetVolumeBo := &algorithm_client.TargetVolumeBo{
			TargetType:         targetType,
			TargetVolumeDetail: targetVolumeDetailList,
		}

		targetBo := &algorithm_client.ProductTargetVolumeBo{
			FulfillmentProductId: product,
			TargetVolume:         []*algorithm_client.TargetVolumeBo{targetVolumeBo},
		}

		targetBos = append(targetBos, targetBo)
	}

	return targetBos
}

// getTargetLocIdCodeMap 填充ForecastVolume信息到Map中，方便后面进行匹配
func getTargetLocIdCodeMap(volumes []rulevolume.BatchAllocateForecastVolumeTab) map[string]string {
	targetLocIdCodeMap := make(map[string]string, len(volumes))
	for _, v := range volumes {
		if v.VolumeScene != constant.VolumeSceneTarget {
			continue
		}

		if v.VolumeType == constant.VolumeTypeZone {
			key := fmt.Sprintf(constant.TargetZoneKey, v.ZoneMinLocationId, v.ZonePostcode)
			targetLocIdCodeMap[key] = v.ZoneCode
		} else if v.VolumeType == constant.VolumeTypeRoute {
			key := fmt.Sprintf(constant.TargetRouteKey, v.RouteMinOriginLocId, v.RouteOriginPostcode, v.RouteMinDestLocId, v.RouteDestPostcode)
			targetLocIdCodeMap[key] = v.RouteCode
		}
	}

	return targetLocIdCodeMap
}

// todo:SSCSMR-1698 补个提前计费开关
func (p *PreparerImpl) GetOrderBosAndLocInfo(ctx context.Context, runSoftOnly bool, reCalFee bool, orderEntityList []model.BatchAllocateOrderDataEntity, volumeTabs []rulevolume.BatchAllocateForecastVolumeTab, historicalShippingFee *sync.Map, splitDateUnix int64, priorities []*allocation.ProductPriorityConfig, allocationRule allocation.BatchAllocationRuleConfig) ([]*algorithm_client.OrderBo, map[uint64]*batch_entity.OrderLotionInfo, int64, *srerr.Error) {

	//订单信息
	orderBos := make([]*algorithm_client.OrderBo, 0)
	orderShippingFeeMap := &sync.Map{}
	// 准备volume，按location id -> ZONE/ROUTE code的映射记录数据
	targetLocIdCodeMap := getTargetLocIdCodeMap(volumeTabs)
	invalidOrder := &sync.Map{}
	orderLocMap := make(map[uint64]*batch_entity.OrderLotionInfo, 0)

	conf := configutil.GetBatchAllocateForecastConf()
	// 异步任务链路
	pool, err := ants.NewPool(conf.AntsPoolSize)
	if err != nil {
		logger.CtxLogErrorf(ctx, "init ants pool err:%v", err)
		return nil, nil, 0, srerr.With(srerr.InitAntsPoolError, nil, err)
	}

	//将整个批次的订单数量填充硬性校验&&计算运费
	for i := 0; i < len(orderEntityList); i++ {
		orderEntity := orderEntityList[i]
		fillFunc := func() {
			defer func() {
				if e := recover(); e != nil {
					logger.CtxLogErrorf(ctx, "panic:%v", e)
				}
			}()

			if !runSoftOnly {
				isPassHardCheck := p.lpsHardCheck(ctx, &orderEntity, invalidOrder, priorities)
				if !isPassHardCheck {
					return
				}
			}

			//2.2 开启重新计费开关才调finance, 否则拿历史结果填充
			_ = p.fillShippingFee(ctx, reCalFee, &orderEntity, invalidOrder, orderShippingFeeMap, historicalShippingFee)

		}

		err := pool.Submit(fillFunc)
		if err != nil {
			logger.CtxLogErrorf(ctx, "ants pool submit err:%v", err)
		}
	}
	pool.Release()

	//装填订单location info
	var lastSecond int64
	for i := 0; i < len(orderEntityList); i++ {
		orderEntity := orderEntityList[i]
		//3. 组装order bo
		if _, ok := invalidOrder.Load(orderEntity.OrderId); ok {
			logger.CtxLogErrorf(ctx, "PreparerImpl|invalid order:%v, just continue", orderEntity.OrderId)
			continue
		}
		orderBo := &algorithm_client.OrderBo{
			OrderId:      orderEntity.OrderId,
			RegionTarget: envvar.GetCIDLower(),
		}
		if len(orderEntity.OmsAllocateRequest.CheckoutItems) > 0 {
			orderBo.ShopID = uint64(orderEntity.OmsAllocateRequest.CheckoutItems[0].ShopId)
		}

		var (
			deliverLocIds   = orderEntity.OmsAllocateRequest.DeliveryInfo.LocationIDs
			deliverPostcode = orderEntity.OmsAllocateRequest.DeliveryInfo.GetPostalCode()
			pickupLocIds    = orderEntity.OmsAllocateRequest.PickupInfo.LocationIDs
			pickupPostcode  = orderEntity.OmsAllocateRequest.PickupInfo.GetPostalCode()
		)
		//匹配订单Zone/Route运力信息
		for _, deliverId := range deliverLocIds {
			// Zone
			// LocId + Postcode匹配Zone
			if zoneCode, ok := targetLocIdCodeMap[fmt.Sprintf(constant.TargetZoneKey, deliverId, deliverPostcode)]; ok {
				orderBo.ZoneCodeTarget = append(orderBo.ZoneCodeTarget, zoneCode)
			}
			// 纯LocId匹配Zone
			if zoneCode, ok := targetLocIdCodeMap[fmt.Sprintf(constant.TargetZoneKey, deliverId, "")]; ok {
				orderBo.ZoneCodeTarget = append(orderBo.ZoneCodeTarget, zoneCode)
			}

			// Route
			for _, pickupId := range pickupLocIds {
				// LocId + Postcode匹配Route
				if routeCode, ok := targetLocIdCodeMap[fmt.Sprintf(constant.TargetRouteKey, pickupId, pickupPostcode, deliverId, deliverPostcode)]; ok {
					orderBo.RouteCodeTarget = append(orderBo.RouteCodeTarget, routeCode)
				}
				// 纯LocId匹配Route
				if routeCode, ok := targetLocIdCodeMap[fmt.Sprintf(constant.TargetRouteKey, pickupId, "", deliverId, "")]; ok {
					orderBo.RouteCodeTarget = append(orderBo.RouteCodeTarget, routeCode)
				}
			}
		}
		//装填shipping fee
		if productInfoInterface, ok := orderShippingFeeMap.Load(orderEntity.OrderId); ok {
			productInfo, ok := productInfoInterface.([]*algorithm_client.ProductInfoBo)
			if ok {
				orderBo.ProductInfo = productInfo
			}
		}

		if len(orderBo.ProductInfo) == 0 {
			logger.CtxLogErrorf(ctx, "batchNo:%v, order id:%v, empty product info, continue", orderBo.OrderId)
			continue
		}
		orderBos = append(orderBos, orderBo)

		locInfo := &batch_entity.OrderLotionInfo{
			OrderId:            orderEntity.OrderId,
			PickUpLocationIds:  orderEntity.OmsAllocateRequest.PickupInfo.LocationIDs, //todo:SSCSMR-1698:需要在转换的时候保证这一串不为nil
			DeliverLocationIds: orderEntity.OmsAllocateRequest.DeliveryInfo.LocationIDs,
		}
		orderLocMap[orderEntity.OrderId] = locInfo

		//获取last second
		lastSecond = orderEntity.RequestTime
	}

	return orderBos, orderLocMap, lastSecond, nil
}

func (p *PreparerImpl) CalculateShippingFee(ctx context.Context, orderInfo *pb.MaskingOrderInfo, productList []int, shippingFeeConfigList []model.ShippingFeeConfig) []*algorithm_client.ProductInfoBo {
	//初始化默认值
	productInfoBoList := make([]*algorithm_client.ProductInfoBo, 0)
	for _, product := range productList {
		productInfoBo := &algorithm_client.ProductInfoBo{
			FulfillmentProductId: uint64(product),
			ShippingFee:          noShippingFee,
		}
		productInfoBoList = append(productInfoBoList, productInfoBo)
	}
	// get forecast shipping fee
	rateReq := chargeentity.BatchForecastAllocationESFReq{
		Token: constant2.FreightApiTokens[strings.ToLower(envvar.GetEnv())],
	}
	rateIdMap := make(map[string]int)
	for _, shippingFeeConfig := range shippingFeeConfigList {
		key := fmt.Sprintf("%v#%v", shippingFeeConfig.ChannelId, shippingFeeConfig.WmsFlag)
		rateIdMap[key] = shippingFeeConfig.RateId
	}
	var wmsFlag int
	if orderInfo.GetIsWms() {
		wmsFlag = constant.Wms
	} else {
		wmsFlag = constant.NoWms
	}
	commonReqDataItem := getForecastCommonReqDataItemForOrder(orderInfo)
	for _, productID := range productList {
		reqDataItem := commonReqDataItem
		bothRateKey := fmt.Sprintf("%v#%v", productID, constant.Both)
		rateKey := fmt.Sprintf("%v#%v", productID, wmsFlag)
		if value, exist := rateIdMap[bothRateKey]; exist {
			// 优先both
			reqDataItem.RateID = value
		} else if value, exist := rateIdMap[rateKey]; exist {
			reqDataItem.RateID = value
		} else {
			// 没有查到相应的rate_id, 则不计算最低运费
			continue
		}
		reqDataItem.ProductID = int64(productID)
		reqDataItem.UniqueId = fmt.Sprintf("uniq_%v", productID)
		reqDataItem.Timestamp = uint32(timeutil.GetCurrentUnixTimeStamp(ctx))
		rateReq.Data = append(rateReq.Data, &reqDataItem)
	}
	rateResp, err := p.RateClient.BatchForecastAllocatingESF(ctx, &rateReq)
	if err != nil || rateResp == nil {
		logger.CtxLogErrorf(ctx, "CalculateShippingFee|order id:%+v, calculate shipping fee err:%v, or rateResp is nil", orderInfo.OrderId, err)
		return productInfoBoList //返回默认值
	}

	if !rateResp.IsSuccess() {
		monitoring.ReportError(ctx, monitoring.CatBatchAllocateForecast, monitoring.AllocateShippingFeeReqError, fmt.Sprintf("Cheapest fee get esf from rateapi not success: %+v", rateResp))
		logger.CtxLogErrorf(ctx, "CalculateShippingFee|order id:%+v, Cheapest fee get esf from rateapi not success: %+v", orderInfo.OrderId, rateResp)
		return productInfoBoList //返回默认值
	}
	//组装参数
	productShippingFeeMap := make(map[uint64]float64)
	for _, result := range rateResp.Data.AllocatingShippingFeeResult {
		//todo:SSCSMR-1698:确认finance返回的结果里shipping fee一定是可用的，会不会存在9个9这种看起来是无穷大的值？
		productShippingFeeMap[uint64(result.ProductId)] = result.AllocatingShippingFee
	}
	for i := 0; i < len(productInfoBoList); i++ {
		if fee, ok := productShippingFeeMap[productInfoBoList[i].FulfillmentProductId]; ok {
			//替换返回值
			productInfoBoList[i].ShippingFee = fee
		}
	}
	return productInfoBoList
}

func getForecastCommonReqDataItemForOrder(info *pb.MaskingOrderInfo) chargeentity.BatchForecastAllocationESFReqDataItem {
	commonReqDataItem := chargeentity.BatchForecastAllocationESFReqDataItem{
		CODAmount:           info.CodAmount,
		COGS:                info.Cogs,
		WMSFlag:             objutil.Bool2Int(info.GetIsWms()),
		PickupLocationIDs:   nil,
		DeliveryLocationIDs: nil,
		SellerTaxNumber:     info.GetSellerTaxNumber(),
		StateRegistration:   info.GetStateRegistration(),
	}
	if info.PickupAddress != nil {
		commonReqDataItem.PickupPostcode = info.PickupAddress.PostalCode
		commonReqDataItem.PickupLongitude = info.PickupAddress.Longitude
		commonReqDataItem.PickupLatitude = info.PickupAddress.Latitude
		commonReqDataItem.PickupLocationIDs = sliceInt64ToInt(info.PickupAddress.GetLocationIds())
	}

	if info.DeliveryAddress != nil {
		commonReqDataItem.DeliveryPostcode = info.DeliveryAddress.PostalCode
		commonReqDataItem.DeliverLongitude = info.DeliveryAddress.Longitude
		commonReqDataItem.DeliverLatitude = info.DeliveryAddress.Latitude
		commonReqDataItem.DeliveryLocationIDs = sliceInt64ToInt(info.DeliveryAddress.GetLocationIds())
	}

	for _, item := range info.CheckoutItems {
		commonReqDataItem.SkuInfo = append(commonReqDataItem.SkuInfo, chargeentity.SkuInfo{
			ItemID:     item.GetItemId(),
			ModelID:    item.GetModelId(),
			CategoryID: item.CategoryId,
			Weight:     item.Weight,
			Quantity:   int(item.GetQuantity()),
			Length:     item.Length,
			Width:      item.Width,
			Height:     item.Height,
		})
	}

	return commonReqDataItem
}

func sliceInt64ToInt(data []int64) []int {
	res := make([]int, 0, len(data))
	for i := range data {
		res = append(res, int(data[i]))
	}
	return res
}

// 填充target
func fillInTarget(ctx context.Context, counter *volume_counter.BatchAllocateForecastCounter, targetBos []*algorithm_client.ProductTargetVolumeBo, lastSecond int64, batchMinuteConfList []float64) {
	if counter == nil {
		logger.CtxLogErrorf(ctx, "fillInCapacity|empty counter, no need to fill")
		return
	}
	//遍历填充
	for i := 0; i < len(targetBos); i++ {
		targetBo := targetBos[i]
		for j := 0; j < len(targetBo.TargetVolume); j++ {
			targetVolume := targetBo.TargetVolume[j]
			for k := 0; k < len(targetVolume.TargetVolumeDetail); k++ {
				targetVolumeDetail := targetVolume.TargetVolumeDetail[k]
				//fill batch min target
				batchMinTarget := batch_allocate2.GetBatchMinTarget(lastSecond, batchMinuteConfList, targetVolumeDetail.DailyMinTar)
				targetVolumeDetail.BatchMinTar = batchMinTarget
				//fill allocated volume
				usedValues := getAllocatedTarget(ctx, counter, targetBo.FulfillmentProductId, targetVolume.TargetType, targetVolumeDetail.TargetVolumeCode)
				targetVolumeDetail.AllocatedVolume = uint32(usedValues[0])
				targetVolumeDetail.CodAllocatedVolume = uint32(usedValues[1])
				targetVolumeDetail.BulkyAllocatedVolume = uint32(usedValues[2])
				targetVolumeDetail.HighValueAllocatedVolume = uint32(usedValues[3])
				targetVolumeDetail.DgAllocatedVolume = uint32(usedValues[4])
				targetVolumeDetail.BatchMinTar = uint32(math.Max(float64(batchMinTarget)-float64(usedValues[0]), 0))
			}
		}
	}
}

func getAllocatedTarget(ctx context.Context, counter *volume_counter.BatchAllocateForecastCounter, fulfillmentProduct uint64, codeType uint8, code string) []int64 {
	allocatedTarget := make([]int64, len(parcel_type_definition.ParcelTypeList))
	for i, parcelType := range parcel_type_definition.ParcelTypeList {
		usedValue, ok := getAllocatedTargetByParcelType(ctx, counter, fulfillmentProduct, codeType, code, parcelType)
		if !ok {
			continue
		}
		allocatedTarget[i] = usedValue
	}
	logger.CtxLogErrorf(ctx, "fillInTarget|fulfillmentProduct:%v, code type:%v, code:%v, allocatedTarget:%v", fulfillmentProduct, codeType, code, allocatedTarget)
	return allocatedTarget
}

func getAllocatedTargetByParcelType(ctx context.Context, counter *volume_counter.BatchAllocateForecastCounter, fulfillmentProduct uint64, codeType uint8, code string, parcelType parcel_type_definition.ParcelType) (int64, bool) {
	var (
		value   int64
		existed bool
	)
	switch codeType {
	case rulevolume.BaVolumeTypeRegion:
		value = counter.GetFulfillmentProductParcelTypeVolume(ctx, fulfillmentProduct, parcelType)
	case rulevolume.BaVolumeTypeZone:
		value = counter.GetTargetZoneVolume(ctx, fulfillmentProduct, code, parcelType)
	case rulevolume.BaVolumeTypeRoute:
		value = counter.GetTargetRouteVolume(ctx, fulfillmentProduct, code, parcelType)
	}

	if value != 0 {
		existed = true
	}

	return value, existed
}

func convertPriority(maskProductID int, priorities []*allocation.ProductPriorityConfig) *entity.ProductPriority {
	if len(priorities) == 0 {
		return nil
	}

	result := &entity.ProductPriority{
		MaskProductID: int64(maskProductID),
	}

	for _, tempPriority := range priorities {
		if tempPriority.ShopGroupId != defaultShopGroupId {
			continue
		}

		result.RuleType = entity.DefaultRuleType(tempPriority.RuleType)
		result.ShopGroupID = tempPriority.ShopGroupId
		for _, detail := range tempPriority.PriorityDetails {
			result.ComponentPriorities = append(result.ComponentPriorities, entity.ComponentPriority{
				ProductID: int64(detail.ProductId),
				Name:      detail.Name,
				Priority:  uint32(detail.Priority),
				Weightage: uint32(detail.Weightage),
				Status:    entity.ComponentPriorityStatus(detail.Status),
			})
		}
	}

	return result
}

func (p *PreparerImpl) lpsHardCheck(ctx context.Context, orderEntity *model.BatchAllocateOrderDataEntity, invalidOrder *sync.Map, priorities []*allocation.ProductPriorityConfig) bool {
	hardReq := &lpsclient.MaskingHardCheckRequest{
		OmsRequest:      orderEntity.OmsAllocateRequest,
		ProductPriority: convertPriority(orderEntity.MaskingProductID, priorities),
	}
	//2.1 硬性校验
	resp, err := p.LpsApi.MaskingHardCheck(ctx, hardReq)
	if err != nil {
		invalidOrder.Store(orderEntity.OrderId, struct{}{})
		logger.CtxLogErrorf(ctx, "PreparerImpl|masking hard check err:%v", err)
		return false
	}
	if resp == nil || resp.HardResult == nil || len(resp.HardResult.ProductId) == 0 {
		invalidOrder.Store(orderEntity.OrderId, struct{}{})
		logger.CtxLogErrorf(ctx, "PreparerImpl|order id:%v, empty masking hard check result", orderEntity.OrderId)
		return false
	}
	orderEntity.HardResult = resp.HardResult

	return true
}

func (p *PreparerImpl) fillShippingFee(ctx context.Context, reCalFee bool, orderEntity *model.BatchAllocateOrderDataEntity, invalidOrder *sync.Map, orderShippingFeeMap *sync.Map, historicalShippingFee *sync.Map) bool {
	//记录历史订单fee
	var historicalFee float64
	var historyProductInfo []*algorithm_client.ProductInfoBo

	if orderEntity.HardResult == nil || len(orderEntity.HardResult.ProductId) < 1 {
		invalidOrder.Store(orderEntity.OrderId, struct{}{})
		logger.CtxLogErrorf(ctx, "PreparerImpl| get empty hard result, order id:%v", orderEntity.OrderId)
		return false
	}

	productParcelMap := make(map[int64]*parcel_type_definition.ParcelTypeAttr)
	for _, productParcel := range orderEntity.ProductParcelInfoList {
		productParcelMap[productParcel.GetProductId()] = &parcel_type_definition.ParcelTypeAttr{
			IsCod:       productParcel.GetParcelTypeAttr().GetIsCod(),
			IsBulky:     productParcel.GetParcelTypeAttr().GetIsBulky(),
			IsHighValue: productParcel.GetParcelTypeAttr().GetIsHighValue(),
			IsDg:        productParcel.GetParcelTypeAttr().GetIsDg(),
		}
	}
	if len(orderEntity.HistoricalShippingFees) < 1 {
		for i := 0; i < len(orderEntity.HardResult.ProductId); i++ {
			product := orderEntity.HardResult.ProductId[i]
			productParcel := &parcel_type_definition.ParcelTypeAttr{}
			if parcelAttr, ok := productParcelMap[int64(product)]; ok {
				productParcel = parcelAttr
			} else {
				monitoring.ReportError(ctx, monitoring.CatBatchAllocateForecast, monitoring.ParcelAttrNotFound, fmt.Sprintf("parcel attr not found: product %+v", product))
			}
			historyProductInfo = append(historyProductInfo, &algorithm_client.ProductInfoBo{
				FulfillmentProductId: uint64(product),
				ShippingFee:          float64(-1),
				OrderAttr: algorithm_client.OrderAttrDetail{
					Cod:       productParcel.IsCod,
					Bulky:     productParcel.IsBulky,
					HighValue: productParcel.IsHighValue,
					Dg:        productParcel.IsDg,
				},
			})
			if product == 0 || product == 1 {
				logger.CtxLogErrorf(ctx, "PreparerImpl| illegal product, order id:%v", orderEntity.OrderId)
			}
		}
	} else {
		shippingFeeMap := make(map[int64]float64, len(orderEntity.HistoricalShippingFees))
		for _, h := range orderEntity.HistoricalShippingFees {
			shippingFeeMap[h.ProductId] = h.AllocateShippingFee
			if h.ProductId == int64(orderEntity.FulfillmentProductID) {
				historicalFee = h.AllocateShippingFee
			}
			if h.ProductId == 0 || h.ProductId == 1 {
				logger.CtxLogErrorf(ctx, "PreparerImpl| illegal product, order id:%v|fee item:%v", orderEntity.OrderId, h)
			}
		}
		for _, productId := range orderEntity.HardResult.ProductId {
			fee, ok := shippingFeeMap[int64(productId)]
			if !ok {
				// 如果运费不存在的话使用-1
				fee = float64(-1)
			}
			productParcel := &parcel_type_definition.ParcelTypeAttr{}
			if parcelAttr, ok := productParcelMap[int64(productId)]; ok {
				productParcel = parcelAttr
			} else {
				monitoring.ReportError(ctx, monitoring.CatBatchAllocateForecast, monitoring.ParcelAttrNotFound, fmt.Sprintf("parcel attr not found: product %+v", productId))
			}
			historyProductInfo = append(historyProductInfo, &algorithm_client.ProductInfoBo{
				FulfillmentProductId: uint64(productId),
				ShippingFee:          fee,
				OrderAttr: algorithm_client.OrderAttrDetail{
					Cod:       productParcel.IsCod,
					Bulky:     productParcel.IsBulky,
					HighValue: productParcel.IsHighValue,
					Dg:        productParcel.IsDg,
				},
			})
		}
	}

	if reCalFee { // recal fee，重新计算各渠道的运费，装填起来传给sdk
		maskOrderData := orderEntity.OmsAllocateRequest.ToPbOrderData()

		//SSCSMR-1698:限流
		_ = p.limiter.Wait(ctx)

		shippingFeeInfos := p.CalculateShippingFee(ctx, maskOrderData, orderEntity.HardResult.ProductId, p.shippingFeeConfigList)
		if len(shippingFeeInfos) == 0 {
			invalidOrder.Store(orderEntity.OrderId, struct{}{})
			logger.CtxLogErrorf(ctx, "PreparerImpl| get empty shipping fee result, order id:%v", orderEntity.OrderId)
			return false
		}

		//重新装载订单运费
		for _, tempInfo := range shippingFeeInfos {
			productId := int64(tempInfo.FulfillmentProductId)
			if productId == int64(orderEntity.FulfillmentProductID) {
				historicalFee = tempInfo.ShippingFee
			}
			productParcel := &parcel_type_definition.ParcelTypeAttr{}
			if parcelAttr, ok := productParcelMap[productId]; ok {
				productParcel = parcelAttr
			} else {
				monitoring.ReportError(ctx, monitoring.CatBatchAllocateForecast, monitoring.ParcelAttrNotFound, fmt.Sprintf("parcel attr not found: product %+v", productId))
			}
			tempInfo.OrderAttr = algorithm_client.OrderAttrDetail{
				Cod:       productParcel.IsCod,
				Bulky:     productParcel.IsBulky,
				HighValue: productParcel.IsHighValue,
				Dg:        productParcel.IsDg,
			}
		}
		historyProductInfo = shippingFeeInfos
	}

	if len(historyProductInfo) == 0 {
		invalidOrder.Store(orderEntity.OrderId, struct{}{})
		logger.CtxLogErrorf(ctx, "PreparerImpl|order id:%v, got empty shipping fee product info, continue", orderEntity.OrderId)
		return false
	}

	// 判断是否跳过该订单的shipping fee, 判断条件: 该订单下，存在任一fulfillment product的shipping fee为-1或或者超过40美金
	conf := configutil.GetBatchAllocateForecastConf()
	for _, productInfo := range historyProductInfo {
		if productInfo.ShippingFee == float64(noShippingFee) {
			historicalFee = 0
			break
		}
		if productInfo.ShippingFee/float64(conf.UsDollarRatio) >= 40 {
			historicalFee = 0
			break
		}
	}

	orderShippingFeeMap.Store(orderEntity.OrderId, historyProductInfo)
	key := uint64(orderEntity.FulfillmentProductID)
	if value, ok := historicalShippingFee.Load(key); ok {
		historicalShippingFee.Store(key, value.(float64)+historicalFee)
	} else {
		historicalShippingFee.Store(key, historicalFee)
	}

	return true
}
