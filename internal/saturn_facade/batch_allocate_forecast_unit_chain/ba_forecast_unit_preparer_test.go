package batch_allocate_forecast_unit_chain

import (
	"context"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing-common/client/algorithm_client"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volume_counter"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/parcel_type_definition"
	"testing"

	batch_allocate2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/batch_allocate"
	"github.com/stretchr/testify/assert"
)

func Test_buildTargetBos(t *testing.T) {
	tests := []struct {
		name           string
		productCodeMap map[uint64]map[string]batch_allocate2.Target
		targetType     uint8
		expected       []*algorithm_client.ProductTargetVolumeBo
	}{
		{
			name:           "空映射场景",
			productCodeMap: map[uint64]map[string]batch_allocate2.Target{},
			targetType:     1,
			expected:       []*algorithm_client.ProductTargetVolumeBo{},
		},
		{
			name: "单产品单目标场景",
			productCodeMap: map[uint64]map[string]batch_allocate2.Target{
				1001: {
					"Z1": {
						MaxVolume: 100,
						MinVolume: 50,
					},
				},
			},
			targetType: 2,
			expected: []*algorithm_client.ProductTargetVolumeBo{
				{
					FulfillmentProductId: 1001,
					TargetVolume: []*algorithm_client.TargetVolumeBo{
						{
							TargetType: 2,
							TargetVolumeDetail: []*algorithm_client.TargetVolumeDetailBo{
								{
									TargetVolumeCode: "Z1",
									DailyMaxTar:      100,
									DailyMinTar:      50,
								},
							},
						},
					},
				},
			},
		},
		{
			name: "单产品多目标场景",
			productCodeMap: map[uint64]map[string]batch_allocate2.Target{
				1002: {
					"Z1": {
						MaxVolume: 100,
						MinVolume: 50,
					},
					"Z2": {
						MaxVolume: 200,
						MinVolume: 100,
					},
					"Z3": {
						MaxVolume: 300,
						MinVolume: 150,
					},
				},
			},
			targetType: 3,
			expected: []*algorithm_client.ProductTargetVolumeBo{
				{
					FulfillmentProductId: 1002,
					TargetVolume: []*algorithm_client.TargetVolumeBo{
						{
							TargetType: 3,
							TargetVolumeDetail: []*algorithm_client.TargetVolumeDetailBo{
								{
									TargetVolumeCode: "Z1",
									DailyMaxTar:      100,
									DailyMinTar:      50,
								},
								{
									TargetVolumeCode: "Z2",
									DailyMaxTar:      200,
									DailyMinTar:      100,
								},
								{
									TargetVolumeCode: "Z3",
									DailyMaxTar:      300,
									DailyMinTar:      150,
								},
							},
						},
					},
				},
			},
		},
		{
			name: "多产品多目标场景",
			productCodeMap: map[uint64]map[string]batch_allocate2.Target{
				1003: {
					"Z1": {
						MaxVolume: 100,
						MinVolume: 50,
					},
					"Z2": {
						MaxVolume: 200,
						MinVolume: 100,
					},
				},
				1004: {
					"Z3": {
						MaxVolume: 300,
						MinVolume: 150,
					},
					"Z4": {
						MaxVolume: 400,
						MinVolume: 200,
					},
				},
			},
			targetType: 4,
			expected: []*algorithm_client.ProductTargetVolumeBo{
				{
					FulfillmentProductId: 1003,
					TargetVolume: []*algorithm_client.TargetVolumeBo{
						{
							TargetType: 4,
							TargetVolumeDetail: []*algorithm_client.TargetVolumeDetailBo{
								{
									TargetVolumeCode: "Z1",
									DailyMaxTar:      100,
									DailyMinTar:      50,
								},
								{
									TargetVolumeCode: "Z2",
									DailyMaxTar:      200,
									DailyMinTar:      100,
								},
							},
						},
					},
				},
				{
					FulfillmentProductId: 1004,
					TargetVolume: []*algorithm_client.TargetVolumeBo{
						{
							TargetType: 4,
							TargetVolumeDetail: []*algorithm_client.TargetVolumeDetailBo{
								{
									TargetVolumeCode: "Z3",
									DailyMaxTar:      300,
									DailyMinTar:      150,
								},
								{
									TargetVolumeCode: "Z4",
									DailyMaxTar:      400,
									DailyMinTar:      200,
								},
							},
						},
					},
				},
			},
		},
		{
			name: "产品无目标场景",
			productCodeMap: map[uint64]map[string]batch_allocate2.Target{
				1005: {},
			},
			targetType: 5,
			expected: []*algorithm_client.ProductTargetVolumeBo{
				{
					FulfillmentProductId: 1005,
					TargetVolume: []*algorithm_client.TargetVolumeBo{
						{
							TargetType:         5,
							TargetVolumeDetail: []*algorithm_client.TargetVolumeDetailBo{},
						},
					},
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 执行测试
			result := buildTargetBos(tt.productCodeMap, tt.targetType)

			// 验证结果
			assert.Equal(t, len(tt.expected), len(result), "结果数组长度不匹配")

			// 由于结果数组顺序可能不确定，需要先按产品ID排序
			productIDToResult := make(map[uint64]*algorithm_client.ProductTargetVolumeBo)
			for _, r := range result {
				productIDToResult[r.FulfillmentProductId] = r
			}

			for _, expected := range tt.expected {
				actual, exists := productIDToResult[expected.FulfillmentProductId]
				assert.True(t, exists, "产品ID %d 应该存在于结果中", expected.FulfillmentProductId)
				if exists {
					// 验证目标类型
					assert.Equal(t, len(expected.TargetVolume), len(actual.TargetVolume), "目标数组长度不匹配")
					if len(expected.TargetVolume) > 0 && len(actual.TargetVolume) > 0 {
						assert.Equal(t, expected.TargetVolume[0].TargetType, actual.TargetVolume[0].TargetType, "目标类型不匹配")

						// 验证目标详情
						expectedDetails := expected.TargetVolume[0].TargetVolumeDetail
						actualDetails := actual.TargetVolume[0].TargetVolumeDetail

						assert.Equal(t, len(expectedDetails), len(actualDetails), "目标详情数组长度不匹配")

						// 由于详情数组顺序可能不确定，需要按目标代码排序
						codeToExpectedDetail := make(map[string]*algorithm_client.TargetVolumeDetailBo)
						for _, d := range expectedDetails {
							codeToExpectedDetail[d.TargetVolumeCode] = d
						}

						for _, actualDetail := range actualDetails {
							expectedDetail, detailExists := codeToExpectedDetail[actualDetail.TargetVolumeCode]
							assert.True(t, detailExists, "目标代码 %s 应该存在于详情中", actualDetail.TargetVolumeCode)
							if detailExists {
								assert.Equal(t, expectedDetail.DailyMaxTar, actualDetail.DailyMaxTar, "最大目标值不匹配")
								assert.Equal(t, expectedDetail.DailyMinTar, actualDetail.DailyMinTar, "最小目标值不匹配")
							}
						}
					}
				}
			}
		})
	}
}

func Test_getAllocatedTarget(t *testing.T) {
	ctx := context.Background()
	type args struct {
		counter            *volume_counter.BatchAllocateForecastCounter
		fulfillmentProduct uint64
		codeType           uint8
		code               string
	}
	tests := []struct {
		name string
		args args
		want []int64
	}{
		// TODO: Add test cases.
		{
			name: "normal case",
			args: args{
				counter:  volume_counter.NewBatchAllocateForecastCounter(),
				codeType: 3,
			},
			want: []int64{1, 0, 0, 0, 0},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.args.counter.IncrFulfillmentProductParcelTypeVolume(ctx, tt.args.fulfillmentProduct, parcel_type_definition.ParcelTypeNone, 1)
			assert.Equalf(t, tt.want, getAllocatedTarget(ctx, tt.args.counter, tt.args.fulfillmentProduct, tt.args.codeType, tt.args.code), "getAllocatedTarget(%v, %v, %v, %v, %v)", ctx, tt.args.counter, tt.args.fulfillmentProduct, tt.args.codeType, tt.args.code)
		})
	}
}
