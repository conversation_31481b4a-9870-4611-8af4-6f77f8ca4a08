package batch_allocate_forecast_unit_chain

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/forecast/repo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/model"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rulevolume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
)

const (
	resultManagerName        = "Result manager"
	routeAndZoneKeySplitSize = 4 // 这里表示key有4部分组成，分别是1.常量前缀、2.fulfillmentProduct、3.route/zone Code、4.parcelType
)

type ResultManagerImpl struct {
	next                                Job
	BatchUnitTargetResultRepo           repo.BatchUnitTargetResultRepo
	BatchUnitFeeResultRepo              repo.BatchUnitFeeResultRepo
	BatchAllocateForecastUnitResultRepo repo.BatchAllocateForecastUnitResultRepo
	BatchAllocateForecastUnitRepo       repo.BatchAllocateForecastUnitRepo
}

func NewResultManagerImpl(
	batchUnitTargetResultRepo repo.BatchUnitTargetResultRepo,
	batchUnitFeeResultRepo repo.BatchUnitFeeResultRepo,
	batchAllocateForecastUnitResultRepo repo.BatchAllocateForecastUnitResultRepo,
) *ResultManagerImpl {
	return &ResultManagerImpl{
		BatchUnitTargetResultRepo:           batchUnitTargetResultRepo,
		BatchUnitFeeResultRepo:              batchUnitFeeResultRepo,
		BatchAllocateForecastUnitResultRepo: batchAllocateForecastUnitResultRepo,
	}
}

func (r *ResultManagerImpl) ExecuteJob(ctx context.Context, inv *Invocation) *srerr.Error {
	var (
		startTime   = timeutil.GetCurrentUnixTimeStamp(ctx)
		batchUnitId = inv.BatchUnitTask.Id
		subTaskId   = inv.BatchUnitTask.SubTaskId
	)
	//1. 根据batch_unit_id查询该切片的所有结果
	// 查询batch_unit_limit的结果

	batchUnitTargetResultTabList, err := r.BatchUnitTargetResultRepo.GetByBatchUnitId(ctx, batchUnitId)
	if err != nil {
		logger.CtxLogErrorf(ctx, "get batch unit target result error: %v", err)
		return err
	}
	lastTargetResultTabList := getLastBatchOfTargetResult(batchUnitTargetResultTabList)

	batchUnitFeeResultTabList, err := r.BatchUnitFeeResultRepo.GetByBatchUnitId(ctx, batchUnitId)
	if err != nil {
		logger.CtxLogErrorf(ctx, "get batch unit fee result error: %v", err)
		return err
	}
	//2. 合并batch unit的数据
	nowTimeStamp := timeutil.GetCurrentUnixTimeStamp(ctx)
	//2.1 overall数据统计
	overallUnitResultList := CombineOverAllResult(batchUnitFeeResultTabList, nowTimeStamp, inv.ForecastUnitStartTime)
	//2.2 zone、route、region数据统计
	zoneUnitResultList, routeUnitResultList := CombineAreaResult(lastTargetResultTabList, nowTimeStamp)
	//3. 保存数据
	var totalUnitResultList []*model.BAForecastUnitResultTab
	totalUnitResultList = append(totalUnitResultList, overallUnitResultList...)
	totalUnitResultList = append(totalUnitResultList, zoneUnitResultList...)
	totalUnitResultList = append(totalUnitResultList, routeUnitResultList...)
	if err := r.BatchAllocateForecastUnitResultRepo.BatchInsertData(ctx, totalUnitResultList); err != nil {
		return err
	}

	pickupEffResultList := combinePickupEffResult(subTaskId, batchUnitId, inv.BatchAllocateForecastCounter.GetShopFulfillmentProducts())
	if err := r.BatchAllocateForecastUnitResultRepo.BatchInsertPickupEffResult(ctx, pickupEffResultList); err != nil {
		return err
	}

	logger.CtxLogInfof(ctx, "result manage time:%v", timeutil.GetCurrentUnixTimeStamp(ctx)-startTime)

	return nil
}

func (r *ResultManagerImpl) SetNext(job Job) {
	r.next = job
}

func (r *ResultManagerImpl) ResultManagerJobName() string {
	return resultManagerName
}

// CombineOverAllResult overall数据统计
func CombineOverAllResult(batchUnitFeeResultTabList []*model.BatchUnitFeeResultTab, nowTime int64, forecastUnitStartTime int64) []*model.BAForecastUnitResultTab {
	var overallUnitResultList []*model.BAForecastUnitResultTab
	// 当前时间减预测单元开始执行时间得到预测单元执行总时间
	totalForecastTime := nowTime - forecastUnitStartTime
	if totalForecastTime < 0 {
		totalForecastTime = 0
	}
	//map聚合,再生成tab
	productFeeMap := make(map[uint64]feeInfo, 0)
	for _, batchUnitFeeResultTab := range batchUnitFeeResultTabList {
		tempFeeInfo := productFeeMap[batchUnitFeeResultTab.FulfillmentProductId]
		tempFeeInfo.SubTaskId = batchUnitFeeResultTab.SubTaskId
		tempFeeInfo.BatchUnitId = batchUnitFeeResultTab.BatchUnitId
		tempFeeInfo.OrderQuantity += batchUnitFeeResultTab.OrderQuantity
		tempFeeInfo.CostFee += batchUnitFeeResultTab.CostFee
		tempFeeInfo.SaveFee += batchUnitFeeResultTab.SaveFee
		tempFeeInfo.PickupEfficiencyCost += batchUnitFeeResultTab.PickupEfficiencyCost
		productFeeMap[batchUnitFeeResultTab.FulfillmentProductId] = tempFeeInfo
	}
	for productId, tempFeeInfo := range productFeeMap {
		bAForecastUnitResultTab := &model.BAForecastUnitResultTab{
			SubTaskId:            tempFeeInfo.SubTaskId,
			BatchUnitId:          tempFeeInfo.BatchUnitId,
			ResultType:           allocation.ResultTypeOverall,
			FulfillmentProductId: productId,
			OrderQuantity:        tempFeeInfo.OrderQuantity,
			TotalCostFee:         tempFeeInfo.CostFee,
			TotalSaveFee:         tempFeeInfo.SaveFee,
			PickupEfficiencyCost: tempFeeInfo.PickupEfficiencyCost,
			TotalForecastTime:    totalForecastTime,
			CTime:                nowTime,
			MTime:                nowTime,
		}
		overallUnitResultList = append(overallUnitResultList, bAForecastUnitResultTab)
	}
	return overallUnitResultList
}

// CombineAreaResult 按区域统计运力
func CombineAreaResult(batchUnitTargetResultTabList []*model.BatchUnitTargetResultTab, nowTime int64) ([]*model.BAForecastUnitResultTab, []*model.BAForecastUnitResultTab) {
	unitZoneResultMap := make(map[uint64]map[string]*model.BAForecastUnitResultTab)
	unitRouteResultMap := make(map[uint64]map[string]*model.BAForecastUnitResultTab)
	for _, batchUnitTargetResultTab := range batchUnitTargetResultTabList {
		fulfillmentProductId := batchUnitTargetResultTab.FulfillmentProductId
		if _, ok := unitZoneResultMap[fulfillmentProductId]; !ok {
			unitZoneResultMap[fulfillmentProductId] = make(map[string]*model.BAForecastUnitResultTab)
		}
		if _, ok := unitRouteResultMap[fulfillmentProductId]; !ok {
			unitRouteResultMap[fulfillmentProductId] = make(map[string]*model.BAForecastUnitResultTab)
		}
		if batchUnitTargetResultTab.TargetCode != "" {
			switch batchUnitTargetResultTab.TargetType {
			case rulevolume.BaVolumeTypeZone:
				if _, ok := unitZoneResultMap[fulfillmentProductId][batchUnitTargetResultTab.TargetCode]; !ok {
					unitZoneResultMap[fulfillmentProductId][batchUnitTargetResultTab.TargetCode] = &model.BAForecastUnitResultTab{
						SubTaskId:            batchUnitTargetResultTab.SubTaskId,
						BatchUnitId:          batchUnitTargetResultTab.BatchUnitId,
						ResultType:           allocation.ResultTypeZone,
						FulfillmentProductId: batchUnitTargetResultTab.FulfillmentProductId,
						OrderQuantity:        batchUnitTargetResultTab.ObtainVolume,
						ZoneCode:             batchUnitTargetResultTab.TargetCode,
					}
				}
			case rulevolume.BaVolumeTypeRoute:
				if _, ok := unitRouteResultMap[fulfillmentProductId][batchUnitTargetResultTab.TargetCode]; !ok {
					unitRouteResultMap[fulfillmentProductId][batchUnitTargetResultTab.TargetCode] = &model.BAForecastUnitResultTab{
						SubTaskId:            batchUnitTargetResultTab.SubTaskId,
						BatchUnitId:          batchUnitTargetResultTab.BatchUnitId,
						ResultType:           allocation.ResultTypeRoute,
						FulfillmentProductId: batchUnitTargetResultTab.FulfillmentProductId,
						OrderQuantity:        batchUnitTargetResultTab.ObtainVolume,
						RouteCode:            batchUnitTargetResultTab.TargetCode,
					}
				}
			}
		}
	}
	var unitZoneResultList, unitRouteResultList []*model.BAForecastUnitResultTab
	for _, zoneMap := range unitZoneResultMap {
		for _, unitZoneResult := range zoneMap {
			unitZoneResultList = append(unitZoneResultList, unitZoneResult)
		}
	}
	for _, routeMap := range unitRouteResultMap {
		for _, unitRouteResult := range routeMap {
			unitRouteResultList = append(unitRouteResultList, unitRouteResult)
		}
	}
	return unitZoneResultList, unitRouteResultList
}

// 获取最后一个批次号，及对应的target result数据
func getLastBatchOfTargetResult(tabs []*model.BatchUnitTargetResultTab) []*model.BatchUnitTargetResultTab {
	if len(tabs) == 0 {
		return nil
	}

	//按batch num 区分limit result
	batchNumResults := make(map[uint64][]*model.BatchUnitTargetResultTab, 0)
	for _, tab := range tabs {
		batchNumResults[tab.BatchNum] = append(batchNumResults[tab.BatchNum], tab)
	}
	var (
		results     []*model.BatchUnitTargetResultTab
		maxBatchNum uint64
	)
	//返回最后一个batch num的数据，因为最后一个batch num才是最终结果，前面的都是过程数据
	for batchNum, tempResults := range batchNumResults {
		if batchNum <= maxBatchNum {
			continue
		}
		maxBatchNum = batchNum
		results = tempResults
	}
	return results
}

func combinePickupEffResult(subTaskId, unitId uint64, shopFulfillmentProducts map[uint64][]uint64) []*model.BAForecastPickupEffResultTab {
	var (
		countMap = make(map[int]int)
		result   = make([]*model.BAForecastPickupEffResultTab, 0)
	)
	for _, productList := range shopFulfillmentProducts {
		countMap[len(productList)]++
	}

	for numberOf3pls, numberOfShops := range countMap {
		result = append(result, &model.BAForecastPickupEffResultTab{
			SubTaskId:     subTaskId,
			BatchUnitId:   unitId,
			NumberOf3pls:  numberOf3pls,
			NumberOfShops: numberOfShops,
		})
	}

	return result
}

type feeInfo struct {
	SubTaskId            uint64
	BatchUnitId          uint64
	OrderQuantity        int64
	CostFee              float64
	SaveFee              float64
	PickupEfficiencyCost float64
}
