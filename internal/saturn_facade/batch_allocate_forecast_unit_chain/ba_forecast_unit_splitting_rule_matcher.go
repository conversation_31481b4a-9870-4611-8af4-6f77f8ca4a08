package batch_allocate_forecast_unit_chain

import (
	"context"
	"encoding/json"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/forecast/repo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/model"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
)

const (
	splittingRuleMatcherName = "Splitting rule matcher"
)

type SplittingRuleMatcherImpl struct {
	next                      Job
	BatchAllocateForecastRepo repo.BatchAllocateForecastRepo
}

func NewSplittingRuleMatcherImpl(BatchAllocateForecastRepo repo.BatchAllocateForecastRepo) *SplittingRuleMatcherImpl {
	return &SplittingRuleMatcherImpl{
		BatchAllocateForecastRepo: BatchAllocateForecastRepo,
	}
}

func (s *SplittingRuleMatcherImpl) ExecuteJob(ctx context.Context, inv *Invocation) *srerr.Error {
	/*
		1.获取sub task
		2.获取sub task的splitting rule并解析到内存模型中
		3.保存splitting rule
		4.调用collector
	*/
	//1.获取sub task
	forecastUnit := inv.BatchUnitTask
	subTask, gErr := s.BatchAllocateForecastRepo.GetSubTask(ctx, map[string]interface{}{
		"id = ?": forecastUnit.SubTaskId,
	})
	if gErr != nil {
		logger.CtxLogErrorf(ctx, "SplittingRuleMatcherImpl|get sub task:%v, err:%v", forecastUnit.SubTaskId, gErr)
		return gErr
	}
	//todo:SSCSMR-1698:去掉这段判断，通过gErr判断
	if subTask.Id == 0 {
		logger.CtxLogErrorf(ctx, "SplittingRuleMatcherImpl|empty sub task, return err")
		return srerr.New(srerr.DatabaseErr, nil, "empty sub task")
	}
	//2.获取sub task的splitting rule并解析到内存模型中
	var splittingRule model.SplittingRule
	if err := json.Unmarshal(subTask.SplittingRule, &splittingRule); err != nil {
		logger.CtxLogErrorf(ctx, "SplittingRuleMatcherImpl| unmarshal splitting rule err:%v", err)
		return srerr.With(srerr.DataErr, nil, err)
	}
	//3.保存splitting rule
	inv.SplittingRule = &splittingRule

	return s.next.ExecuteJob(ctx, inv)
}

func (s *SplittingRuleMatcherImpl) SetNext(job Job) {
	s.next = job
}

func (s *SplittingRuleMatcherImpl) RuleMatcherJobName() string {
	return splittingRuleMatcherName
}
