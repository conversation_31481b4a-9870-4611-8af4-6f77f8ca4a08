package batch_allocate_forecast_unit_chain

import (
	"context"
	"fmt"
	"runtime"
	"time"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing-common/client/algorithm_client"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/aisclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/forecast/repo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/model"
	whitelist2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/whitelist"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/batch_allocate/allocation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/saturn_facade/batch_entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/whitelist"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
)

const (
	forecastUnitStarterName = "Forecast unit starter"
)

type ForecastUnitStarterImpl struct {
	next                      Job
	AlgoClient                algorithm_client.AlgoClientInterface
	AisApi                    aisclient.AisApi
	BatchAllocateForecastRepo repo.BatchAllocateForecastRepo
	ShopWhitelistService      whitelist.ShopWhitelistService
}

func NewForecastUnitStarterImpl(
	BatchAllocateForecastRepo repo.BatchAllocateForecastRepo,
	shopWhitelistService whitelist.ShopWhitelistService,
) *ForecastUnitStarterImpl {
	return &ForecastUnitStarterImpl{
		AlgoClient:                algorithm_client.NewAlgoClient(),
		AisApi:                    aisclient.NewAisApi(),
		BatchAllocateForecastRepo: BatchAllocateForecastRepo,
		ShopWhitelistService:      shopWhitelistService,
	}
}

func (f *ForecastUnitStarterImpl) ExecuteJob(ctx context.Context, inv *Invocation) *srerr.Error {
	respChan := make(chan *batch_entity.BatchAllocateResp, 100) //todo:SSCSMR-1698:size 如何设置?
	go func(chan *batch_entity.BatchAllocateResp) {
		defer func() {
			logger.CtxLogInfof(ctx, "ForecastUnitStarterImpl| resp chan closed")
			close(respChan)
		}()
		defer func() {
			if err := recover(); err != nil {
				inv.shouldClosed.Set(true)
				var buf [panicMsgSize]byte
				n := runtime.Stack(buf[:], false)
				errMsg := fmt.Sprintf("[Recovery|ForecastUnitStarterImpl] panic recovered:\n%v \n%v", err, string(buf[:n]))
				logger.CtxLogErrorf(ctx, errMsg)
			}
		}()
		for batchAllocateReq := range inv.BatchAllocateReqChan {
			if inv.shouldClosed.Get() {
				break
			}

			if inv.AllocationRule.PickupEfficiencyWhitelistEnabled {
				// 走一遍Pickup Efficiency Whitelist的过滤逻辑
				for _, orderInfo := range batchAllocateReq.BAReq.Orders {
					orderInfo.ProductInfo = f.pickupEfficiencyWhitelistFilter(
						ctx, inv.BatchAllocateForecastTask.MaskingProductID, orderInfo.OrderId, int64(orderInfo.ShopID), orderInfo.ProductInfo,
					)
				}
			}

			// TODO go live时改成debug
			logger.CtxLogInfof(ctx, "request sdk args %s", objutil.JsonString(batchAllocateReq))
			startTime := timeutil.GetCurrentUnixTimeStamp(ctx)

			var baResp *algorithm_client.BatchAllocateRespBo
			// 首先调用 AIS API 进行计算
			businessTaskID := fmt.Sprintf("forecast_batch_%d_%d", batchAllocateReq.BatchUnitId, startTime)
			if configutil.IsSwitch(ctx, aisclient.BatchAllocateSDKSwitch, businessTaskID) {
				// 灰度开关命中，调用 AIS API 进行计算
				allocate, aisErr := f.AisApi.ComputeTaskWithEncodedInput(ctx, businessTaskID, batchAllocateReq.BAReq)
				if aisErr != nil {
					logger.CtxLogErrorf(ctx, "AIS API call failed, fallback to original algorithm | err=%v", aisErr.Error())
				} else {
					baResp = allocate
				}
			}
			if baResp == nil {
				// 原有的 AlgoClient.BatchAllocate 调用
				baResp = f.callOriginalAlgorithm(ctx, batchAllocateReq, startTime, inv)
			}

			// 如果两种算法都失败了，跳过这个批次
			if baResp == nil {
				continue
			}

			respChan <- &batch_entity.BatchAllocateResp{
				BatchUnitId: batchAllocateReq.BatchUnitId,
				BAResp:      baResp,
				BAReq:       batchAllocateReq.BAReq,
				OrderInfo:   batchAllocateReq.OrderInfo,
				ExecuteTime: time.Now().Unix() - startTime, // nolint
			}
		}
	}(respChan)

	inv.BatchAllocateRespChan = respChan

	return f.next.ExecuteJob(ctx, inv)
}

func (f *ForecastUnitStarterImpl) SetNext(job Job) {
	f.next = job
}

func (f *ForecastUnitStarterImpl) UnitStarterJobName() string {
	return forecastUnitStarterName
}

func (f *ForecastUnitStarterImpl) UpdateForecastUnit(ctx context.Context, condition map[string]interface{}, tab model.BatchAllocateForecastUnitTab) *srerr.Error {
	return f.BatchAllocateForecastRepo.UpdateForecastUnit(ctx, condition, tab)
}

func (f *ForecastUnitStarterImpl) pickupEfficiencyWhitelistFilter(
	ctx context.Context, maskProductId int, orderId uint64, shopID int64, products []*algorithm_client.ProductInfoBo,
) []*algorithm_client.ProductInfoBo {

	if hit := f.ShopWhitelistService.CheckShopWhitelistAndFulfillmentType(ctx, shopID, whitelist2.MPLFulfillment); !hit {
		logger.CtxLogDebugf(ctx, "shop not in pickup efficiency whitelist | orderId:%d, shopId:%d", orderId, shopID)
		return products
	}

	priorityMap, err := f.ShopWhitelistService.GetPickupPriorityByMaskProductIDWithCache(ctx, int64(maskProductId))
	if err != nil {
		logger.CtxLogDebugf(ctx, "mask product no pickup priority")
	}

	return allocation.GetPickupEfficiencyWhitelistProduct(ctx, orderId, products, priorityMap)
}

func (f *ForecastUnitStarterImpl) callOriginalAlgorithm(
	ctx context.Context, batchAllocateReq *batch_entity.BatchAllocateReq, startTime int64, inv *Invocation,
) *algorithm_client.BatchAllocateRespBo {
	// 原有的 AlgoClient.BatchAllocate 调用
	allocate, err := f.AlgoClient.BatchAllocate(ctx, batchAllocateReq.BAReq)
	logger.CtxLogInfof(ctx, "sdk allocate cost time:%v", timeutil.GetCurrentUnixTimeStamp(ctx)-startTime)
	if err != nil || allocate == nil || allocate.OrderResult == nil {
		inv.SetAllowFillIn(true)
		logger.CtxLogInfof(ctx, "ForecastUnitStarterImpl|set allow fill in true")
		logger.CtxLogErrorf(ctx, "ForecastUnitStarterImpl|batch allocate failed err:%v and allocate:%+v", err, allocate)
		//某个批次失败了，不影响整体流程，打上日志
		return nil
	}
	return allocate
}
