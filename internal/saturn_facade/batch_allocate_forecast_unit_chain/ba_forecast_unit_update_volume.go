package batch_allocate_forecast_unit_chain

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing-common/client/algorithm_client"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/forecast_unit"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/model"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rulevolume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/saturn_facade/batch_entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/parcel_type_definition"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	"strconv"
	"strings"
	"time"
)

const (
	forecastUnitUpdateVolumeName = "Update volume"
)

type ForecastUnitUpdateVolumeImpl struct {
	next                     Job
	BatchAllocateVolumeRepo  rulevolume.BatchAllocateForecastVolumeRepo
	BatchForecastUnitService forecast_unit.BatchForecastUnitService
}

func NewForecastUnitUpdateVolumeImpl(BatchForecastUnitService forecast_unit.BatchForecastUnitService) *ForecastUnitUpdateVolumeImpl {
	return &ForecastUnitUpdateVolumeImpl{
		BatchForecastUnitService: BatchForecastUnitService,
	}
}

func (f *ForecastUnitUpdateVolumeImpl) ExecuteJob(ctx context.Context, inv *Invocation) *srerr.Error {
	for resp := range inv.BatchAllocateRespChan {
		for fProductId, result := range resp.BAResp.StatsResult {
			if result == nil {
				logger.CtxLogErrorf(ctx, "fProductId:%v, result is nil", fProductId)
				continue
			}
			// 增加fulfillment对应的单量
			inv.BatchAllocateForecastCounter.IncrFulfillmentProductParcelTypeVolume(ctx, fProductId, parcel_type_definition.ParcelTypeNone, int64(result.TotalOrderNum))
			inv.BatchAllocateForecastCounter.IncrFulfillmentProductParcelTypeVolume(ctx, fProductId, parcel_type_definition.ParcelTypeCod, int64(result.TotalCodOrderNum))
			inv.BatchAllocateForecastCounter.IncrFulfillmentProductParcelTypeVolume(ctx, fProductId, parcel_type_definition.ParcelTypeBulky, int64(result.TotalBulkyOrderNum))
			inv.BatchAllocateForecastCounter.IncrFulfillmentProductParcelTypeVolume(ctx, fProductId, parcel_type_definition.ParcelTypeHighValue, int64(result.TotalHighValueOrderNum))
			inv.BatchAllocateForecastCounter.IncrFulfillmentProductParcelTypeVolume(ctx, fProductId, parcel_type_definition.ParcelTypeDg, int64(result.TotalDgOrderNum))

			for zoneCode, zoneResult := range result.TargetZoneStatsResult {
				inv.BatchAllocateForecastCounter.IncrTargetZoneVolume(ctx, fProductId, zoneCode, parcel_type_definition.ParcelTypeNone, int64(zoneResult.OrderNum))
				inv.BatchAllocateForecastCounter.IncrTargetZoneVolume(ctx, fProductId, zoneCode, parcel_type_definition.ParcelTypeCod, int64(zoneResult.CodOrderNum))
				inv.BatchAllocateForecastCounter.IncrTargetZoneVolume(ctx, fProductId, zoneCode, parcel_type_definition.ParcelTypeBulky, int64(zoneResult.BulkyOrderNum))
				inv.BatchAllocateForecastCounter.IncrTargetZoneVolume(ctx, fProductId, zoneCode, parcel_type_definition.ParcelTypeHighValue, int64(zoneResult.HighValueOrderNum))
				inv.BatchAllocateForecastCounter.IncrTargetZoneVolume(ctx, fProductId, zoneCode, parcel_type_definition.ParcelTypeDg, int64(zoneResult.DgOrderNum))
			}
			for routeCode, routeResult := range result.TargetRouteStatsResult {
				inv.BatchAllocateForecastCounter.IncrTargetRouteVolume(ctx, fProductId, routeCode, parcel_type_definition.ParcelTypeNone, int64(routeResult.OrderNum))
				inv.BatchAllocateForecastCounter.IncrTargetRouteVolume(ctx, fProductId, routeCode, parcel_type_definition.ParcelTypeCod, int64(routeResult.CodOrderNum))
				inv.BatchAllocateForecastCounter.IncrTargetRouteVolume(ctx, fProductId, routeCode, parcel_type_definition.ParcelTypeBulky, int64(routeResult.BulkyOrderNum))
				inv.BatchAllocateForecastCounter.IncrTargetRouteVolume(ctx, fProductId, routeCode, parcel_type_definition.ParcelTypeHighValue, int64(routeResult.HighValueOrderNum))
				inv.BatchAllocateForecastCounter.IncrTargetRouteVolume(ctx, fProductId, routeCode, parcel_type_definition.ParcelTypeDg, int64(routeResult.DgOrderNum))
			}
		}

		for shopID, shopStatsResult := range resp.BAResp.PickupEffResult.ShopFulfillmentProductsStatsResult {
			for f := range shopStatsResult {
				inv.BatchAllocateForecastCounter.AddShopFulfillmentProducts(ctx, shopID, f)
			}
		}
		inv.BatchAllocateForecastCounter.IncrPickupEffUsedBudget(ctx, float64(resp.BAResp.PickupEffResult.UsedCost))

		//设置标志位，允许下一批继续填充capacity，target volume
		logger.CtxLogInfof(ctx, "ForecastUnitUpdateVolumeImpl|set allow fill in true")
		inv.SetAllowFillIn(true)

		// 保存批次结果到db
		if err := f.SaveLimitAndTargetUnitResult(ctx, inv, resp); err != nil {
			return err
		}
	}

	return f.next.ExecuteJob(ctx, inv)
}

func (f *ForecastUnitUpdateVolumeImpl) SetNext(job Job) {
	f.next = job
}

func (f *ForecastUnitUpdateVolumeImpl) UnitUpdateVolumeJobName() string {
	return forecastUnitUpdateVolumeName
}

func (f *ForecastUnitUpdateVolumeImpl) SaveLimitAndTargetUnitResult(ctx context.Context, inv *Invocation, resp *batch_entity.BatchAllocateResp) *srerr.Error {
	var (
		startTime          = timeutil.GetCurrentUnixTimeStamp(ctx)
		unitTargetResult   = make([]*model.BatchUnitTargetResultTab, 0)
		unitShippingResult = make([]*model.BatchUnitFeeResultTab, 0)
	)

	// 创建zone对应的limit、target结果集
	zoneRouteTargetResult := f.buildZoneRouteUnitResult(ctx, inv, resp.BAReq)
	unitTargetResult = append(unitTargetResult, zoneRouteTargetResult...)

	// 创建region target结果集
	regionTargetResult := f.buildCountryUnitTargetTab(ctx, inv, resp.BAReq)
	unitTargetResult = append(unitTargetResult, regionTargetResult...)

	//运费结果集
	shippingFeeResult := f.buildShippingFeeResult(ctx, inv, resp)
	unitShippingResult = append(unitShippingResult, shippingFeeResult...)

	// 保存结果集到db
	if err := f.BatchForecastUnitService.BatchCreateUnitTargetResult(ctx, unitTargetResult); err != nil {
		return err
	}
	if err := f.BatchForecastUnitService.BatchCreateUnitFeeResult(ctx, unitShippingResult); err != nil {
		return err
	}
	logger.CtxLogInfof(ctx, "update volume time:%v", timeutil.GetCurrentUnixTimeStamp(ctx)-startTime)

	return nil
}

// 创建zone纬度的limit/target result
func (f *ForecastUnitUpdateVolumeImpl) buildZoneRouteUnitResult(
	ctx context.Context, inv *Invocation, req *algorithm_client.BatchAllocateReqBo,
) []*model.BatchUnitTargetResultTab {

	var (
		targetZoneResult  = inv.BatchAllocateForecastCounter.GetTargetZoneResult()
		targetRouteResult = inv.BatchAllocateForecastCounter.GetTargetRouteResult()
		unitTargetResult  = make([]*model.BatchUnitTargetResultTab, 0, len(targetZoneResult)+len(targetRouteResult))
	)

	for zoneKey, zoneCount := range targetZoneResult {
		// 创建这个zone对应的target结果
		zoneTarget := buildUnitTargetTab(ctx, inv, req, zoneKey, zoneCount, rulevolume.BaVolumeTypeZone)
		unitTargetResult = append(unitTargetResult, zoneTarget)
	}

	for routeKey, routeCount := range targetRouteResult {
		// 创建这个route对应的target结果
		routeTarget := buildUnitTargetTab(ctx, inv, req, routeKey, routeCount, rulevolume.BaVolumeTypeRoute)
		unitTargetResult = append(unitTargetResult, routeTarget)
	}

	return unitTargetResult
}

// 创建country纬度的target result
func (f *ForecastUnitUpdateVolumeImpl) buildCountryUnitTargetTab(ctx context.Context, inv *Invocation, req *algorithm_client.BatchAllocateReqBo) []*model.BatchUnitTargetResultTab {
	countryResult := inv.BatchAllocateForecastCounter.GetCountryVolumeResult()
	unitTargetResult := make([]*model.BatchUnitTargetResultTab, 0, len(countryResult))

	for key, countryVolume := range countryResult {
		countryTargetTab := buildCountryTargetResult(ctx, inv, req, key, countryVolume, rulevolume.BaVolumeTypeRegion)
		unitTargetResult = append(unitTargetResult, countryTargetTab)
	}
	return unitTargetResult
}

func (f *ForecastUnitUpdateVolumeImpl) buildShippingFeeResult(ctx context.Context, inv *Invocation, resp *batch_entity.BatchAllocateResp) []*model.BatchUnitFeeResultTab {
	fProductFeeMap := getFulfillmentProductFeeMap(ctx, resp.BAReq, resp.BAResp)
	unitFeeResult := make([]*model.BatchUnitFeeResultTab, 0, len(resp.BAResp.StatsResult))
	for productId, statResult := range resp.BAResp.StatsResult {
		var singleCostFee float64

		inv.feeMapLock.RLock()
		feeMap := inv.batchProductShippingFeeMap[resp.BAResp.BatchNo]
		inv.feeMapLock.RUnlock()

		costFeeInterface, ok := feeMap.Load(productId)
		if !ok {
			logger.CtxLogErrorf(ctx, "buildShippingFeeResult|product id:%v, load cost fee err failed", productId)
		} else {
			singleCostFee, _ = costFeeInterface.(float64)
		}
		sdkFee := fProductFeeMap[productId]
		if sdkFee > singleCostFee {
			logger.CtxLogErrorf(ctx, "sdk fee is bigger than the original|batch no=%d,algo sdk fee=%f,single allocate fee=%f", resp.BAResp.BatchNo, fProductFeeMap[productId], singleCostFee)
		}
		countryTargetTab := buildShippingFeeResultTab(ctx, inv, sdkFee, singleCostFee, productId, int64(statResult.TotalOrderNum), resp)
		unitFeeResult = append(unitFeeResult, countryTargetTab)
	}

	return unitFeeResult
}

func getFulfillmentProductFeeMap(ctx context.Context, req *algorithm_client.BatchAllocateReqBo, resp *algorithm_client.BatchAllocateRespBo) map[uint64]float64 {
	// 遍历订单信息，如果订单下任一渠道运费大于40美金，或运费为-1，跳过该单的运费统计
	invalidOrderMap := make(map[uint64]struct{}, 0)
	conf := configutil.GetBatchAllocateForecastConf()
	for _, order := range req.Orders {
		for _, productInfo := range order.ProductInfo {
			if productInfo.ShippingFee == noShippingFee {
				invalidOrderMap[order.OrderId] = struct{}{}
				break
			}
			if productInfo.ShippingFee/float64(conf.UsDollarRatio) >= 40 {
				invalidOrderMap[order.OrderId] = struct{}{}
				break
			}
		}
	}

	fProductFeeMap := map[uint64]float64{}
	for _, orderResult := range resp.OrderResult {
		if _, ok := invalidOrderMap[orderResult.OrderId]; ok {
			continue
		}
		if orderResult.ShippingFee == float64(noShippingFee) {
			continue
		}
		fProductFeeMap[orderResult.FulfillmentProductId] += orderResult.ShippingFee
	}
	logger.CtxLogInfof(ctx, "getFulfillmentProductFeeMap|current total order fee:%v", fProductFeeMap)

	return fProductFeeMap
}

// 从key中解析route/zone code以及ProductId
func getFulfillmentProductAndCodeByKey(ctx context.Context, key string) (fulfillmentProductId uint64, targetCode string) {
	split := strings.Split(key, ":")
	if len(split) != routeAndZoneKeySplitSize {
		logger.CtxLogErrorf(ctx, "invalid route or zone key format. expected %d parts, got %d | key: %s", routeAndZoneKeySplitSize, len(split), key)
		return 0, ""
	}

	fulfillmentProductId, err := strconv.ParseUint(split[routeAndZoneKeySplitSize-3], 10, 64)
	if err != nil {
		logger.CtxLogErrorf(ctx, "failed to parse fulfillment product ID from key | key: %s | error: %v", key, err)
		return 0, ""
	}

	targetCode = split[routeAndZoneKeySplitSize-2]

	return fulfillmentProductId, targetCode
}

func fillTargetCommonInfo(unitTask *model.BatchAllocateForecastUnitTab, targetLimit *model.BatchUnitTargetResultTab) {
	now := time.Now().Unix() // nolint
	targetLimit.SplitDateUnix = unitTask.SplitDateUnix
	targetLimit.BatchUnitId = unitTask.Id
	targetLimit.SubTaskId = unitTask.SubTaskId
	targetLimit.CTime = now
	targetLimit.MTime = now
}

func getTargetVolume(req *algorithm_client.BatchAllocateReqBo, fProductId uint64, matchCode string, codeType uint8) (uint32, uint32, uint32) {
	for _, volume := range req.TargetVolume {
		if volume.FulfillmentProductId != fProductId {
			continue
		}
		for _, targetVolume := range volume.TargetVolume {
			if targetVolume.TargetType != codeType {
				continue
			}
			for _, detail := range targetVolume.TargetVolumeDetail {
				if detail.TargetVolumeCode == matchCode {
					return detail.DailyMaxTar, detail.DailyMinTar, detail.BatchMinTar
				}
			}
		}
	}

	return 0, 0, 0
}

func buildUnitTargetTab(
	ctx context.Context, inv *Invocation, req *algorithm_client.BatchAllocateReqBo, zoneKey string, obtainVolume int64, targetType int,
) *model.BatchUnitTargetResultTab {

	targetResult := model.BatchUnitTargetResultTab{}
	fillTargetCommonInfo(inv.BatchUnitTask, &targetResult)
	fProductId, targetCode := getFulfillmentProductAndCodeByKey(ctx, zoneKey)
	targetResult.BatchNum = req.BatchNo
	targetResult.FulfillmentProductId = fProductId
	targetResult.TargetType = targetType
	targetResult.TargetCode = targetCode
	targetResult.ObtainVolume = obtainVolume
	dailyMaxVolume, dailyMinVolume, batchMinVolume := getTargetVolume(req, fProductId, targetCode, uint8(targetType))
	targetResult.DailyMaxVolume = int64(dailyMaxVolume)
	targetResult.DailyMinVolume = int64(dailyMinVolume)
	targetResult.BatchMinVolume = int64(batchMinVolume)

	return &targetResult
}

func buildCountryTargetResult(ctx context.Context, inv *Invocation, req *algorithm_client.BatchAllocateReqBo, fProductId uint64, obtainVolume int64, targetType int) *model.BatchUnitTargetResultTab {
	targetResult := model.BatchUnitTargetResultTab{}
	fillTargetCommonInfo(inv.BatchUnitTask, &targetResult)
	targetResult.BatchNum = req.BatchNo
	targetResult.FulfillmentProductId = fProductId
	targetResult.TargetType = targetType
	targetResult.ObtainVolume = obtainVolume
	dailyMaxVolume, dailyMinVolume, batchMinVolume := getTargetVolume(req, fProductId, strconv.FormatUint(fProductId, 10), uint8(targetType))
	targetResult.DailyMaxVolume = int64(dailyMaxVolume)
	targetResult.DailyMinVolume = int64(dailyMinVolume)
	targetResult.BatchMinVolume = int64(batchMinVolume)

	return &targetResult
}

func buildShippingFeeResultTab(ctx context.Context, inv *Invocation, sdkFee, singleCostFee float64, fProductId uint64, orderCount int64, resp *batch_entity.BatchAllocateResp) *model.BatchUnitFeeResultTab {
	unitFee := model.BatchUnitFeeResultTab{}
	fillUnitFeeCommonInfo(inv.BatchUnitTask, &unitFee)
	unitFee.BatchNum = resp.BAReq.BatchNo
	unitFee.FulfillmentProductId = fProductId
	unitFee.CostFee = sdkFee
	unitFee.SaveFee = singleCostFee - sdkFee
	unitFee.OrderQuantity = orderCount
	unitFee.AlgoTime = inv.ExtraInfo.AlgoExecuteTime
	unitFee.ExecuteTime = resp.ExecuteTime
	unitFee.PickupEfficiencyCost = float64(resp.BAResp.PickupEffResult.UsedCost)
	value, exist := inv.HoldingTimeMap.Load(resp.BAResp.BatchNo)
	if exist {
		holdingTime, ok := value.(int64)
		if ok {
			unitFee.HoldingTime = holdingTime
		} else {
			logger.CtxLogErrorf(ctx, "convert holding time failed")
		}
	} else {
		logger.CtxLogErrorf(ctx, "batch no:%v has no holding time", resp.BAResp.BatchNo)
	}
	return &unitFee
}

func fillUnitFeeCommonInfo(unitTask *model.BatchAllocateForecastUnitTab, targetLimit *model.BatchUnitFeeResultTab) {
	now := time.Now().Unix() // nolint
	targetLimit.SplitDateUnix = unitTask.SplitDateUnix
	targetLimit.BatchUnitId = unitTask.Id
	targetLimit.SubTaskId = unitTask.SubTaskId
	targetLimit.CTime = now
	targetLimit.MTime = now
}
