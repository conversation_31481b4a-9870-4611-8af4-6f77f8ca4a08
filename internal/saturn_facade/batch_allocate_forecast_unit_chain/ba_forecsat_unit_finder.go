package batch_allocate_forecast_unit_chain

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/forecast/repo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/model"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/requestid"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"hash/crc32"
	"strconv"
)

const (
	forecastUnitFinderName = "Forecast unit finder"
)

/*
//检索待执行预测单元
type ForecastUnitFinder interface {
	ExecuteJob(ctx context.Context, inv *Invocation) *srerr.Error
	SetNext(job Job)
	FinderJobName() string
}
*/

type ForecastUnitFinderImpl struct {
	next                      Job
	BatchAllocateForecastRepo repo.BatchAllocateForecastRepo
}

func NewForecastUnitFinderImpl(BatchAllocateForecastRepo repo.BatchAllocateForecastRepo) *ForecastUnitFinderImpl {
	return &ForecastUnitFinderImpl{
		BatchAllocateForecastRepo: BatchAllocateForecastRepo,
	}
}

func (f *ForecastUnitFinderImpl) ExecuteJob(ctx context.Context, inv *Invocation) *srerr.Error {
	/*
		1.分片
		2.db抢占batch forecast unit
		3.更新sub task
		4.执行下一环
		5.（如果出错）更新sub task状态
	*/
	forecastUnit := inv.BatchUnitTask
	taskRequestID := fmt.Sprintf("%s|forecast_unit_id=%d", requestid.GetFromCtx(ctx), forecastUnit.Id)
	ctx = logger.NewLogContext(ctx, taskRequestID)
	ctx = requestid.SetToCtx(ctx, taskRequestID)
	//2.匹配sub task
	//一般标识string的时候，可以用hash，然后得到对应的hash value（数字）；如果是数字的话可以直接取模，但是要求数字不能重复
	hashValue := crc32.ChecksumIEEE([]byte(strconv.FormatUint(forecastUnit.Id, 10)))
	if hashValue%uint32(forecastUnit.TotalShardingNum) != uint32(forecastUnit.ShardingNo) {
		//退出
		logger.CtxLogErrorf(ctx, "batch allocate|stop at:%v, not match", f.FinderJobName())
		return srerr.New(srerr.NotMatchError, nil, "hash value of task:%v doesn't match sharding no:%v", forecastUnit.Id, forecastUnit.ShardingNo)
	}
	logger.CtxLogInfof(ctx, "batch allocate|job:%v|sub task id:%v|match success", f.FinderJobName(), forecastUnit.Id)

	//抢占forecast unit，抢占失败则返回
	tab := inv.BatchUnitTask
	tab.BatchStatus = model.BatchForecastUnitProcess
	if err := f.BatchAllocateForecastRepo.UpdateForecastUnit(ctx, map[string]interface{}{
		"batch_status = ?": model.BatchForecastUnitPending,
	}, *tab); err != nil {
		logger.CtxLogErrorf(ctx, "ForecastUnitFinderImpl| update unit to process err:%v", err)
		return err
	}

	//更新sub task到process
	if err := f.BatchAllocateForecastRepo.UpdateSubTaskByCondition(ctx, map[string]interface{}{
		"id = ?":              tab.SubTaskId,
		"sub_task_status = ?": model.BatchAllocateSubTaskPending,
	}, map[string]interface{}{
		"sub_task_status": model.BatchAllocateSubTaskProcess,
	}); err != nil {
		logger.CtxLogErrorf(ctx, "ForecastUnitFinderImpl|update sub task:%v, err:%v", tab.SubTaskId, err)
	}

	err := f.next.ExecuteJob(ctx, inv)
	if err != nil {
		logger.CtxLogErrorf(ctx, "ForecastUnitFinderImpl| execute next job err:%v", err)
		//更新sub task到failed
		condition := map[string]interface{}{
			"id = ?": forecastUnit.SubTaskId,
		}
		value := map[string]interface{}{
			"sub_task_status": model.BatchAllocateSubTaskFailed,
		}
		//todo:SSCSMR-1698: sub task可能要去掉，因为没有地方用，没有意义，只保留sub task + 日期 （即forecast unit）即可
		if uErr := f.BatchAllocateForecastRepo.UpdateSubTaskByCondition(ctx, condition, value); uErr != nil {
			logger.CtxLogErrorf(ctx, "ForecastUnitFinderImpl|update sub task:%v into 'failed' err:%v", forecastUnit.SubTaskId, uErr)
		}
		//更新unit 状态
		forecastUnit.BatchStatus = model.BatchForecastUnitFailed
	} else {
		forecastUnit.BatchStatus = model.BatchForecastUnitDone
	}
	//更新forecast unit 到failed
	condition := map[string]interface{}{
		"id = ?":           forecastUnit.Id,
		"batch_status = ?": model.BatchForecastUnitProcess,
	}
	uErr := f.BatchAllocateForecastRepo.UpdateForecastUnit(ctx, condition, *forecastUnit)
	if uErr != nil {
		logger.CtxLogErrorf(ctx, "ForecastUnitFinderImpl| update forecast unit into 'failed' err:%v", uErr)
	}

	return err
}

func (f *ForecastUnitFinderImpl) SetNext(job Job) {
	f.next = job
}

func (f *ForecastUnitFinderImpl) FinderJobName() string {
	return forecastUnitFinderName
}
