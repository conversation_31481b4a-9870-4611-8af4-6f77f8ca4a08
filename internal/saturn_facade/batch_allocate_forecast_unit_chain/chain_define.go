package batch_allocate_forecast_unit_chain

import (
	"context"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/model"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volume_counter"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/saturn_facade/batch_entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/saturn_facade/warning"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/allocation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"go.uber.org/atomic"
	"sync"
)

// todo:SSCSMR-1698: 修改下变量名，不要用invocation(invation means a request)
// 保存责任链的各种参数，责任链单元之间的数据交互由Invocation完成
type Invocation struct {
	once                         sync.Once
	BatchUnitTask                *model.BatchAllocateForecastUnitTab
	SplittingRule                *model.SplittingRule
	SplitResultsChan             chan []model.BatchAllocateOrderDataEntity
	BatchAllocateForecastTask    *model.AllocateForecastTaskConfigTab
	AllocationRule               allocation.BatchAllocationRuleConfig
	ForecastUnitStartTime        int64
	BatchAllocateReqChan         chan *batch_entity.BatchAllocateReq          //SDK req
	BatchAllocateRespChan        chan *batch_entity.BatchAllocateResp         //SDK response
	BatchAllocateForecastCounter *volume_counter.BatchAllocateForecastCounter //保存过程运力数据
	allowFillIn                  *atomic.Bool
	feeMapLock                   *sync.RWMutex
	batchProductShippingFeeMap   map[uint64]*sync.Map    //保存某个预测单元下，每个批次各product的总shipping fee
	ExtraInfo                    *batch_entity.ExtraInfo // 这里保存的是额外额信息包括hold单时长，预测前的总运费等等
	shouldClosed                 *warning.ShouldCloseObj //通知所有管道退出，用来处理panic场景
	//todo:SSCSMR-1698: sync map => 如果key不交叠且保证size不被动态扩容，可以不用sync map
	HoldingTimeMap *sync.Map //用来记录不同批次的holding time
}

func (i *Invocation) GetAllowFillIn() bool {
	return i.allowFillIn.Load()
}

func (i *Invocation) SetAllowFillIn(flag bool) {
	i.once.Do(func() {
		if i.allowFillIn == nil {
			i.allowFillIn = atomic.NewBool(flag)
		}
	})

	i.allowFillIn.CAS(i.allowFillIn.Load(), flag)
}

// 这里接口方法需要定义成public，因为实现放在了另一个包里，不同包不能调用私有方法
// 定义责任链单元
type Job interface {
	ExecuteJob(ctx context.Context, inv *Invocation) *srerr.Error
	SetNext(job Job)
}

// 检索待执行预测单元
type ForecastUnitFinder interface {
	ExecuteJob(ctx context.Context, inv *Invocation) *srerr.Error
	SetNext(job Job)
	FinderJobName() string
}

// 解析分片规则，不同的rule会有不同的解析规则
type SplittingRuleMatcher interface {
	ExecuteJob(ctx context.Context, inv *Invocation) *srerr.Error
	SetNext(job Job)
	RuleMatcherJobName() string
}

// 检索订单
type OrderCollector interface {
	ExecuteJob(ctx context.Context, inv *Invocation) *srerr.Error
	SetNext(job Job)
	OrderCollectorJobName() string
}

// 获取运力数据
type Preparer interface {
	ExecuteJob(ctx context.Context, inv *Invocation) *srerr.Error
	SetNext(job Job)
	VolumeFinderJobName() string
}

// 调用SDK
type ForecastUnitStarter interface {
	ExecuteJob(ctx context.Context, inv *Invocation) *srerr.Error
	SetNext(job Job)
	UnitStarterJobName() string
}

type UpdateVolumeManager interface {
	ExecuteJob(ctx context.Context, inv *Invocation) *srerr.Error
	SetNext(job Job)
	UnitUpdateVolumeJobName() string
}

// 保存运力结果
type ResultManager interface {
	ExecuteJob(ctx context.Context, inv *Invocation) *srerr.Error
	SetNext(job Job)
	ResultManagerJobName() string
}
