package export_masking_panel_data

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/chassis-saturn-server/saturn"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/masking_panel"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/masking_result_panel"
	jsoniter "github.com/json-iterator/go"
)

type MaskingDataExportTask struct {
	keyDataManager masking_result_panel.KeyDataManager
}

func NewMaskingDataExportTask(keyDataManager masking_result_panel.KeyDataManager) *MaskingDataExportTask {
	return &MaskingDataExportTask{
		keyDataManager: keyDataManager,
	}
}

func (p *MaskingDataExportTask) Name() string {
	return constant.TaskNameExportMaskingData
}

func (p *MaskingDataExportTask) MsgHandle(ctx context.Context, message *saturn.SaturnMessage) *saturn.SaturnReply {
	var taskParam masking_panel.GetKeyDataRequest
	if err := jsoniter.Unmarshal(message.MsgText, &taskParam); err != nil {
		return &saturn.SaturnReply{
			Retcode: -1,
			Message: fmt.Sprintf("message unmarshal failed | msg_text=%s | err=%s", string(message.MsgText), err),
		}
	}
	logger.CtxLogInfof(ctx, "MaskingDataExportTask| task param after unmarshal:%v", taskParam)

	uErr := p.keyDataManager.GetAndUploadMaskingData(ctx, taskParam, taskParam.TaskID)
	if uErr != nil {
		logger.CtxLogErrorf(ctx, "MaskingDataExportTask|get and upload data err:%v", uErr)
		return &saturn.SaturnReply{
			Retcode: -1,
			Message: fmt.Sprintf("MaskingDataExportTask|get and upload data err:%v", uErr),
		}
	}
	return &saturn.SaturnReply{
		Retcode: saturn.SUCCESS,
		Message: "SUCCESS",
	}
}
