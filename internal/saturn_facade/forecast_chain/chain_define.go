package forecast_chain

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/go-redis"
	gohbase "git.garena.com/shopee/bg-logistics/go/ssc-gohbase"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lpsclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/address"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/allocate_order_data_repo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/forecasting_sub_task"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/masking_forecast"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/schedule_visual"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/forecast/service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
)

// 保存责任链的各种参数，责任链单元之间的数据交互由Invocation完成
type Invocation struct {
	SubTask           *forecasting_sub_task.ForecastingSubTaskTab
	ExtendInfo        *ExtendInfo
	HbResults         chan *gohbase.Result
	ForecastCounter   *masking_forecast.AllocateForecastCounter
	HistoricalCounter *masking_forecast.AllocateHistoricalCounter
}

func (i *Invocation) SetHbaseResults(hbResults chan *gohbase.Result) {
	i.HbResults = hbResults
}

func (i *Invocation) SetMaskingResults(forecastCounter *masking_forecast.AllocateForecastCounter, historicalCounter *masking_forecast.AllocateHistoricalCounter) {
	i.ForecastCounter = forecastCounter
	i.HistoricalCounter = historicalCounter
}

func (i *Invocation) SetExtendInfo(info *ExtendInfo) {
	i.ExtendInfo = info
}

// //这里接口方法需要定义成public，因此实现放在了另一个包里，不同包不能调用私有方法
// //定义责任链单元
type Job interface {
	ExecuteJob(ctx context.Context, inv *Invocation) *srerr.Error
	SetNext(job Job)
}

// 定义task matcher，实现责任链单元，用来匹配task，其可以拥有多个实现
type TaskMatcher interface {
	ExecuteJob(ctx context.Context, inv *Invocation) *srerr.Error
	SetNext(job Job)
	TaskMatcherJobName() string
}

// 定义data collector，实现责任链单元，用来检索数据，其可以拥有多个实现
type DataCollector interface {
	ExecuteJob(ctx context.Context, inv *Invocation) *srerr.Error
	SetNext(job Job)
	DataCollectorJobName() string
}

// 定义forecaster，实现责任链单元，用来进行预测，其可以拥有多个实现
type Forecaster interface {
	ExecuteJob(ctx context.Context, inv *Invocation) *srerr.Error
	SetNext(job Job)
	ForecasterJobName() string
}

// 定义dataStorage，实现责任链单元，用来保存结果到db，其可以拥有多个实现
type DataStorage interface {
	ExecuteJob(ctx context.Context, inv *Invocation) *srerr.Error
	SetNext(job Job)
	DataStorageJobName() string
}

type ExtendInfo struct {
	MainTaskId        uint64
	MaskingProductId  uint64
	NeedSleep         bool
	CurrentTotalCount int64
	UseHardRate       bool
}

/*
定义责任链流程
*/
const (
	MaskingExecutor      = "masking_executor"
	SmartRoutingExecutor = "smart_routing_executor"
)

var executorMap map[string]JobChainExecutor

type JobChainExecutor interface {
	ExecuteChain(ctx context.Context, subTask *forecasting_sub_task.ForecastingSubTaskTab) *srerr.Error
}

type JobChainService interface {
	GetJobExecutor(name string) JobChainExecutor
}

type JobChainServiceImpl struct {
}

func NewJobChainServiceImpl(
	subTaskService forecasting_sub_task.ForecastingSubTaskService,
	mainTaskService service.AllocateForecastTaskConfigService,
	AllocateRankService service.AllocateRankService,
	lpsApi lpsclient.LpsApi,
	AllocationConfigRepo config.AllocationConfigRepo,
	RedisClient *redis.Client,
	AddrRepo address.AddrRepo,
	AllocateForecastService masking_forecast.AllocateForecastService,
	allocateOrderDataRepo allocate_order_data_repo.AllocateOrderDataRepo,
	ScheduleVisualStat schedule_visual.ScheduleCountStatInterface) *JobChainServiceImpl {

	if executorMap == nil {
		executorMap = make(map[string]JobChainExecutor, 0)
	}
	executorMap[MaskingExecutor] = NewMaskingJobChainExecutor(subTaskService, mainTaskService, AllocateRankService, lpsApi, AllocationConfigRepo, RedisClient, AddrRepo, AllocateForecastService, allocateOrderDataRepo, ScheduleVisualStat)
	return &JobChainServiceImpl{}
}

func (j *JobChainServiceImpl) GetJobExecutor(name string) JobChainExecutor {
	if executor, ok := executorMap[name]; ok {
		return executor
	}
	return nil
}
