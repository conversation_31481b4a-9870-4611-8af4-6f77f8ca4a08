package forecast_chain

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	gohbase "git.garena.com/shopee/bg-logistics/go/ssc-gohbase"
	smart_routing_protobuf "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing-protocol/protocol/go"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/model"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rulevolume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/masking_forecast"
	ordentity "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/order_info"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/allocation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/zip"
	"github.com/bytedance/sonic"
	"sort"
	"strconv"
)

func (m *MaskingForecaster) convertToRule(ctx context.Context, taskConfig *allocation.AllocateForecastTaskConfigEntity) (*rule.MaskRule, *srerr.Error) {
	// consider of taskConfig un-exception
	if taskConfig.BaseInfo == nil || taskConfig.AllocationRuleConfig == nil ||
		taskConfig.AllocationRuleConfig.RuleDetail == nil || taskConfig.AllocationRuleConfig.MaskProductRuleVolumeConfig == nil {
		logger.CtxLogErrorf(ctx, "Allocate Forecast-- convertToRule err. taskConfig == %v", taskConfig)
		return nil, srerr.New(srerr.TypeConvertErr, taskConfig, "can't convert to rule")
	}
	volume := taskConfig.AllocationRuleConfig.MaskProductRuleVolumeConfig
	detail := taskConfig.AllocationRuleConfig.RuleDetail
	rl := &rule.MaskRule{
		Id:                    int64(taskConfig.BaseInfo.Id),
		MaskProductId:         int64(taskConfig.BaseInfo.MaskProductId),
		Status:                rule.MaskRuleStatusActive,
		EnableProductPriority: taskConfig.BaseInfo.ShopGroupChannelPriorityToggle,
	}

	if volume != nil && volume.RuleType == uint32(rulevolume.LocVolumeTypeRoute) {
		rl.LocVolumeType = rulevolume.LocVolumeTypeRoute
	} else if volume != nil && volume.RuleType == uint32(rulevolume.LocVolumeTypeZone) {
		rl.LocVolumeType = rulevolume.LocVolumeTypeZone
	} else if volume != nil && volume.RuleType == uint32(rulevolume.LocVolumeTypeCountry) {
		rl.LocVolumeType = rulevolume.LocVolumeTypeCountry
	}

	ruleSteps := rule.MaskRuleSteps{}
	if detail.MinVolumeEnable {
		detail.MinVolumeCountryEnable = detail.MinVolumeEnable
		detail.MinVolumeCountrySort = detail.MinVolumeSort
		if rl.LocVolumeType != rulevolume.LocVolumeTypeCountry {
			detail.MinVolumeZoneRouteEnable = detail.MinVolumeEnable
			detail.MinVolumeZoneRouteSort = detail.MinVolumeSort
		}
	}
	if detail.MaxCapacityEnable {
		detail.MaxCapacityCountryEnable = detail.MaxCapacityEnable
		detail.MaxCapacityCountrySort = detail.MaxCapacitySort
		if rl.LocVolumeType != rulevolume.LocVolumeTypeCountry {
			detail.MaxCapacityZoneRouteEnable = detail.MaxCapacityEnable
			detail.MaxCapacityZoneRouteSort = detail.MaxCapacitySort
		}
	}
	if detail.MinVolumeCountryEnable {
		minVolumes := map[rule.VolumeKey]int32{}
		for _, limit := range volume.DefaultVolumeLimit {
			key := rule.VolumeKey{
				MaskProductID:        int64(taskConfig.BaseInfo.MaskProductId),
				FulfillmentProductID: int64(limit.ProductId),
			}
			minVolumes[key] = int32(limit.MinVolume)
		}
		step := rule.MaskRuleStep{
			Priority:      int32(detail.MinVolumeSort),
			MaskStepType:  rule.MaskStepMinVolumeCountry,
			MinVolumeData: &rule.MinVolumeData{MinVolumes: minVolumes},
		}
		ruleSteps = append(ruleSteps, step)
	}
	if detail.MaxCapacityCountryEnable {
		maxCapacities := make(map[rule.VolumeKey]int32)
		isHardCaps := make(map[rule.VolumeKey]bool)
		for _, limit := range volume.DefaultVolumeLimit {
			key := rule.VolumeKey{
				MaskProductID:        int64(taskConfig.BaseInfo.MaskProductId),
				FulfillmentProductID: int64(limit.ProductId),
			}
			maxCapacities[key] = int32(limit.MaxCapacity)
			isHardCaps[key] = limit.IsHardCap
		}
		step := rule.MaskRuleStep{
			Priority:           int32(detail.MaxCapacityCountrySort),
			MaskStepType:       rule.MaskStepMaxCapacityCountry,
			MaxCapacityData:    &rule.MaxCapacityData{MaxCapacities: maxCapacities},
			IsHardCapacityData: &rule.IsHardCapacityData{IsHardCaps: isHardCaps},
		}
		ruleSteps = append(ruleSteps, step)
	}
	if detail.MinVolumeZoneRouteEnable {
		step := rule.MaskRuleStep{
			Priority:     int32(detail.MinVolumeZoneRouteSort),
			MaskStepType: rule.MaskStepMinVolumeZoneRoute,
		}
		ruleSteps = append(ruleSteps, step)
	}
	if detail.MaxCapacityZoneRouteEnable {
		step := rule.MaskRuleStep{
			Priority:     int32(detail.MaxCapacityZoneRouteSort),
			MaskStepType: rule.MaskStepMaxCapacityZoneRoute,
		}
		ruleSteps = append(ruleSteps, step)
	}
	if detail.MaxCodCapacityCountryEnable {
		step := rule.MaskRuleStep{
			Priority:     int32(detail.MaxCodCapacityCountrySort),
			MaskStepType: rule.MaskStepMaxCodCapacityCountry,
		}
		ruleSteps = append(ruleSteps, step)
	}
	if detail.MaxCodCapacityZoneRouteEnable {
		step := rule.MaskRuleStep{
			Priority:     int32(detail.MaxCodCapacityZoneRouteSort),
			MaskStepType: rule.MaskStepMaxCodCapacityZoneRoute,
		}
		ruleSteps = append(ruleSteps, step)
	}
	if detail.MaxBulkyCapacityCountryEnable {
		step := rule.MaskRuleStep{
			Priority:     int32(detail.MaxBulkyCapacityCountrySort),
			MaskStepType: rule.MaskStepMaxBulkyCapacityCountry,
		}
		ruleSteps = append(ruleSteps, step)
	}
	if detail.MaxBulkyCapacityZoneRouteEnable {
		step := rule.MaskRuleStep{
			Priority:     int32(detail.MaxBulkyCapacityZoneRouteSort),
			MaskStepType: rule.MaskStepMaxBulkyCapacityZoneRoute,
		}
		ruleSteps = append(ruleSteps, step)
	}
	if detail.MaxHighValueCapacityCountryEnable {
		step := rule.MaskRuleStep{
			Priority:     int32(detail.MaxHighValueCapacityCountrySort),
			MaskStepType: rule.MaskStepMaxHighValueCapacityCountry,
		}
		ruleSteps = append(ruleSteps, step)
	}
	if detail.MaxHighValueCapacityZoneRouteEnable {
		step := rule.MaskRuleStep{
			Priority:     int32(detail.MaxHighValueCapacityZoneRouteSort),
			MaskStepType: rule.MaskStepMaxHighValueCapacityZoneRoute,
		}
		ruleSteps = append(ruleSteps, step)
	}
	if detail.MaxDgCapacityCountryEnable {
		step := rule.MaskRuleStep{
			Priority:     int32(detail.MaxDgCapacityCountrySort),
			MaskStepType: rule.MaskStepMaxDgCapacityCountry,
		}
		ruleSteps = append(ruleSteps, step)
	}
	if detail.MaxDgCapacityZoneRouteEnable {
		step := rule.MaskRuleStep{
			Priority:     int32(detail.MaxDgCapacityZoneRouteSort),
			MaskStepType: rule.MaskStepMaxDgCapacityZoneRoute,
		}
		ruleSteps = append(ruleSteps, step)
	}
	if detail != nil && detail.MaxBatchVolumeEnable {
		volumesInOneBatch := map[int64]int32{}
		for _, limit := range detail.Limit {
			volumesInOneBatch[int64(limit.ProductId)] = int32(limit.MaxVolumeEachBatch)
		}
		step := rule.MaskRuleStep{
			Priority:     int32(detail.MaxBatchVolumeSort),
			MaskStepType: rule.MaskStepMaxBatchVolume,
			BatchVolumeData: &rule.BatchVolumeData{
				BatchVolume:      int32(detail.MaxBatchVolume),
				VolumeInOneBatch: volumesInOneBatch,
			},
		}
		ruleSteps = append(ruleSteps, step)
	}
	if detail != nil && detail.CheapestFeeEnable {
		step := rule.MaskRuleStep{
			Priority:     int32(detail.CheapestFeeSort),
			MaskStepType: rule.MaskStepCheapestFee,
		}
		ruleSteps = append(ruleSteps, step)
	}
	sort.Sort(ruleSteps)
	rl.MaskRuleSteps = ruleSteps

	return rl, nil
}

func ConvertHbResultToAllocateEntity(a *model.AllocateOrderDataTab) (*masking_forecast.AllocateOrderDataEntity, error) {
	allocateOrderDataEntity := masking_forecast.AllocateOrderDataEntity{
		OrderId:              a.OrderId,
		MaskingProductID:     a.MaskingProductID,
		FulfillmentProductID: a.FulfillmentProductID,
		ShopGroupId:          a.ShopGroupId,
		ZoneCode:             a.ZoneCode,
		OriginZoneCode:       a.OriginZoneCode,
		DestZoneCode:         a.DestZoneCode,
		RouteCode:            a.RouteCode,
		OrderTime:            a.OrderTime,
		OmsAllocateRequest:   a.OmsAllocateRequestHbase,
	}
	hardResult := model.ValidateResult{
		ProductId: a.HardResultHbase,
	}
	allocateOrderDataEntity.HardResult = &hardResult
	return &allocateOrderDataEntity, nil
}

func (m *MaskingForecaster) TransferResultToTab(ctx context.Context, result *gohbase.Result) (*model.AllocateOrderDataTab, *srerr.Error) {
	//转换数据（包括解压+模型转换） 并写入channel
	if result == nil {
		logger.CtxLogErrorf(ctx, "TransferResultToEntity|cannot parse nil hbase result, will skip it")
		return nil, srerr.New(srerr.ParamErr, nil, "result is nil, can't convert it")
	}
	resultEntity := model.AllocationHbaseEntity{}
	//此处实际v只会有一个，for循环的模式能避免result.Cells[0]空指针panic
	for _, v := range result.Cells {
		key := string(v.Qualifier[:])
		decodeBytes, err := zip.ZSTDDecompress(v.Value)
		if err != nil {
			logger.CtxLogErrorf(ctx, "TransferResultToEntity|key:%v, decode value err:%v", key, err)
			continue
		}
		value := string(decodeBytes)
		if err := sonic.Unmarshal(decodeBytes, &resultEntity); err != nil {
			logger.CtxLogDebugf(ctx, "TransferResultToEntity|value:%v, unmarshal value err:%v", value, err)
		}
	}
	if resultEntity.AllocationScenario == int(smart_routing_protobuf.AllocationScenario_ReturnMaskingAllocation) {
		return nil, srerr.New(srerr.ParamErr, nil, "no need to forecast return allocate")
	}
	tab, cErr := m.convertResultToAllocationTab(ctx, resultEntity)
	if cErr != nil {
		logger.CtxLogErrorf(ctx, "TransferResultToEntity|convert result err:%v", cErr)
		return nil, cErr
	}
	return tab, cErr
}

func (m *MaskingForecaster) convertResultToAllocationTab(ctx context.Context, resultEntity model.AllocationHbaseEntity) (*model.AllocateOrderDataTab, *srerr.Error) {
	orderData := &model.AllocateOrderDataTab{
		OrderId:              resultEntity.OrderId,
		MaskingProductID:     resultEntity.MaskProductId,
		FulfillmentProductID: resultEntity.FulfillmentProductId,
		ShopGroupId:          int(resultEntity.ShopGroupId),
		OrderTime:            uint32(resultEntity.RequestTime),
		HardResultHbase:      resultEntity.HardOutput,
	}

	//填充 zone/route code
	//zone和route是互斥的
	if len(resultEntity.RouteCodes) != 0 {
		orderData.RouteCode = resultEntity.RouteCodes[len(resultEntity.RouteCodes)-1]
	} else {
		if resultEntity.ZoneOriginCode != "" {
			orderData.ZoneCode = resultEntity.ZoneOriginCode
		} else {
			orderData.ZoneCode = resultEntity.ZoneDestinationCode
		}
		orderData.OriginZoneCode = resultEntity.ZoneOriginCode
		orderData.DestZoneCode = resultEntity.ZoneDestinationCode
	}

	//转换request
	request := &model.OmsAllocRequest{}
	if err := sonic.UnmarshalString(resultEntity.RequestDataStr, request); err != nil { //报错，返回
		//logger.CtxLogErrorf(ctx, "convertResultToAllocationTab|order id:%v, convert request to schema err:%v", resultEntity.OrderId, err)
		return nil, srerr.With(srerr.DataErr, nil, err)
	} else {
		allocReq, cErr := m.ConvertOmsAllocRequest(ctx, request)
		if cErr != nil { //报错，返回
			//logger.CtxLogErrorf(ctx, "convertResultToAllocationTab|order id:%v, convert request to OmsAllocRequest err:%v", resultEntity.OrderId, cErr)
			return nil, cErr
		} else {
			orderData.OmsAllocateRequestHbase = allocReq
		}
	}

	return orderData, nil
}

func (m *MaskingForecaster) ConvertOmsAllocRequest(ctx context.Context, originReq *model.OmsAllocRequest) (*model.OMSAllocateRequest, *srerr.Error) {
	// https://jira.shopee.io/browse/SPLPS-1972
	// Store Order Snapshot of Fulfilment Channel that Pass Hard Rule
	var snapshot *model.SnapShot
	if originReq.Snapshot != nil {
		snapshot = &model.SnapShot{IgnoreSnapshot: originReq.Snapshot.IgnoreSnapshot, ShippingChannels: originReq.Snapshot.ShippingChannels}
	} else {
		snapshot = nil
	}

	forderID := originReq.FOrderID
	if originReq.FOrderIDStr != nil {
		forderID, _ = strconv.ParseUint(*originReq.FOrderIDStr, 10, 64)
	}
	var allocReq = &model.OMSAllocateRequest{
		MaskingProductID:           originReq.MaskingChannelId,
		PaymentMethod:              originReq.PaymentMethod,
		TotalPrice:                 originReq.TotalPrice,
		CodAmount:                  originReq.CodAmount,
		Cogs:                       originReq.Cogs,
		PartialFulfillment:         0,
		IsWms:                      originReq.IsWms,
		WhsId:                      originReq.WhsId,
		CheckoutItems:              originReq.ConvertCheckoutItem(),
		FOrderID:                   forderID,
		OrderID:                    originReq.OrderID,
		BuyerPaidShippingFee:       originReq.BuyerPaidShippingFee,
		SellerTaxNumber:            originReq.SellerTaxNumber,
		StateRegistration:          originReq.StateRegistration,
		Snapshot:                   snapshot,
		RosOptin:                   originReq.RosOptin,
		RosEligible:                originReq.RosEligible,
		IsCacheAfterBuyerSelection: originReq.IsCacheAfterBuyerSelection,
	}
	var pickupInfo, deliverInfo *ordentity.AddressInfo
	if originReq.PickupInfo != nil && originReq.DeliveryInfo != nil {
		pickupInfo, deliverInfo = originReq.ConvertAddressInfo()
	}
	err := m.FillLocationIds(ctx, pickupInfo)
	if err != nil {
		return nil, err
	}
	err = m.FillLocationIds(ctx, deliverInfo)
	if err != nil {
		return nil, err
	}
	allocReq.PickupInfo = pickupInfo
	allocReq.DeliveryInfo = deliverInfo
	if originReq.PartialFulfillment {
		allocReq.PartialFulfillment = 1
	}
	return allocReq, nil
}

func (m *MaskingForecaster) FillLocationIds(ctx context.Context, info *ordentity.AddressInfo) *srerr.Error {
	// 已经存在location id不用转换
	if info != nil && info.StateLocationId != nil && *info.StateLocationId > 0 {
		return nil
	}
	if info == nil || info.State == nil || info.City == nil {
		return srerr.New(srerr.ParamErr, nil, "missing location core information[state, city]")
	}
	locInfo, err := m.AddrRepo.GetLocationByLocFullPathName(ctx, info.GetCountry(), info.GetState(), info.GetCity(), info.GetDistrict(), info.GetStreet())
	if err != nil {
		return err
	}
	//装填 location id
	var stateLocationId = int(locInfo.GetStateLocId())
	info.StateLocationId = &stateLocationId
	var cityLocationId = int(locInfo.GetCityLocId())
	info.CityLocationId = &cityLocationId
	locIds := []int{stateLocationId, cityLocationId}
	if locInfo.GetDistrictLocId() > 0 {
		var districtLocationId = int(locInfo.GetDistrictLocId())
		info.DistrictLocationId = &districtLocationId
		locIds = append(locIds, districtLocationId)
	}
	if locInfo.GetStreetLocId() > 0 {
		var streetLocationId = int(locInfo.GetStreetLocId())
		info.StreetLocationId = &streetLocationId
		locIds = append(locIds, streetLocationId)
	}
	info.LocationIDs = locIds
	return nil
}
