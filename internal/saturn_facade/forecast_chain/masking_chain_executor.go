package forecast_chain

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/go-redis"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lpsclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/address"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/allocate_order_data_repo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/forecasting_sub_task"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/masking_forecast"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/schedule_visual"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/forecast/service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
)

// 定义责任链处理器，本处为masking实现
type MaskingJobChainExecutor struct {
	subTaskService          forecasting_sub_task.ForecastingSubTaskService
	mainTaskService         service.AllocateForecastTaskConfigService
	AllocateRankService     service.AllocateRankService
	lpsApi                  lpsclient.LpsApi
	AllocationConfigRepo    config.AllocationConfigRepo
	RedisClient             *redis.Client
	AddrRepo                address.AddrRepo
	AllocateForecastService masking_forecast.AllocateForecastService
	allocateOrderDataRepo   allocate_order_data_repo.AllocateOrderDataRepo
	ScheduleVisualStat      schedule_visual.ScheduleCountStatInterface
}

func NewMaskingJobChainExecutor(
	subTaskService forecasting_sub_task.ForecastingSubTaskService,
	mainTaskService service.AllocateForecastTaskConfigService,
	AllocateRankService service.AllocateRankService,
	lpsApi lpsclient.LpsApi,
	AllocationConfigRepo config.AllocationConfigRepo,
	RedisClient *redis.Client,
	AddrRepo address.AddrRepo,
	AllocateForecastService masking_forecast.AllocateForecastService,
	allocateOrderDataRepo allocate_order_data_repo.AllocateOrderDataRepo,
	ScheduleVisualStat schedule_visual.ScheduleCountStatInterface) *MaskingJobChainExecutor {

	return &MaskingJobChainExecutor{
		subTaskService:          subTaskService,
		mainTaskService:         mainTaskService,
		AllocateRankService:     AllocateRankService,
		lpsApi:                  lpsApi,
		AllocationConfigRepo:    AllocationConfigRepo,
		RedisClient:             RedisClient,
		AddrRepo:                AddrRepo,
		AllocateForecastService: AllocateForecastService,
		allocateOrderDataRepo:   allocateOrderDataRepo,
		ScheduleVisualStat:      ScheduleVisualStat,
	}
}

/*
1.定义单元
2.将依赖的单元注入（依赖反转
*/
func (m *MaskingJobChainExecutor) ExecuteChain(ctx context.Context, subTask *forecasting_sub_task.ForecastingSubTaskTab) *srerr.Error {
	//1.定义责任链单元
	dataStorage := NewMaskingDataStorage(m.AllocateRankService, m.subTaskService, m.mainTaskService)

	//2.定义预测单元，并注入数据存储单元
	forecaster := NewMaskingForecaster(m.mainTaskService, m.lpsApi, m.AllocationConfigRepo, m.RedisClient, m.AddrRepo, m.AllocateForecastService, m.ScheduleVisualStat)
	forecaster.SetNext(dataStorage)
	//3.定义数据收集单元，并注入预测单元
	dataCollector := NewMaskingDataCollector(m.allocateOrderDataRepo, m.RedisClient)
	dataCollector.SetNext(forecaster)
	//4.定义匹配单元，并注入数据收集单元
	taskMatcher := NewMaskingTaskMatcher(m.subTaskService, m.mainTaskService)
	taskMatcher.SetNext(dataCollector)

	//5.触发责任链处理器，正常处理完成了就返回，否则更新任务到failed
	inv := &Invocation{
		SubTask: subTask,
	}
	if cErr := taskMatcher.ExecuteJob(ctx, inv); cErr != nil {
		return cErr
	}

	return nil

}
