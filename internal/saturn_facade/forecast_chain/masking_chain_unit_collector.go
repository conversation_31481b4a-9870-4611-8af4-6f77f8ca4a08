package forecast_chain

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/go-redis"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/allocate_order_data_repo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"strconv"
)

const (
	defaultChannelSize = 1000
)

// 定义data collector，实现责任链单元，用来检索数据，本处为masking实现
type MaskingDataCollector struct {
	next                  Job
	allocateOrderDataRepo allocate_order_data_repo.AllocateOrderDataRepo
	RedisClient           *redis.Client
}

func NewMaskingDataCollector(
	allocateOrderDataRepo allocate_order_data_repo.AllocateOrderDataRepo,
	RedisClient *redis.Client) *MaskingDataCollector {
	return &MaskingDataCollector{
		allocateOrderDataRepo: allocateOrderDataRepo,
		RedisClient:           RedisClient,
	}
}

func (m *MaskingDataCollector) ExecuteJob(ctx context.Context, inv *Invocation) *srerr.Error {
	/*
		1.获取hbase数据，将数据放到channel中
		2.将channel传递到下一环节中
		3.获取order总数
	*/
	subTask := inv.SubTask
	startTime, err := strconv.ParseInt(subTask.StartKey, 10, 64)
	if err != nil {
		logger.CtxLogErrorf(ctx, "stop at:v|failed to parse start key:%v, err:%v", m.DataCollectorJobName(), subTask.StartKey, err)
		return srerr.With(srerr.ConvertKeyError, nil, err)
	}
	endTime, err := strconv.ParseInt(subTask.EndKey, 10, 64)
	if err != nil {
		logger.CtxLogErrorf(ctx, "stop at:v|failed to parse end key:%v, err:%v", m.DataCollectorJobName(), subTask.EndKey, err)
		return srerr.With(srerr.ConvertKeyError, nil, err)
	}
	logger.CtxLogInfof(ctx, "job:%v|sub task:%v| start to collect data", m.DataCollectorJobName(), subTask.Id)
	//1.调用hbase，获取channel
	//SSCSMR-1480:复用配置的channel size， 默认1000
	channelSize := defaultChannelSize
	conf := configutil.GetMaskingForecastConf(ctx)
	if conf.ChannelSize != 0 {
		channelSize = conf.ChannelSize
	}
	hbResults, _ := m.allocateOrderDataRepo.GetAllocateOrderDataFromHbaseByKey(ctx, inv.ExtendInfo.MaskingProductId, startTime, endTime, channelSize, inv.ExtendInfo.NeedSleep, inv.ExtendInfo.UseHardRate)
	//2.将channel传递到下一环节中
	inv.SetHbaseResults(hbResults)

	return m.next.ExecuteJob(ctx, inv)
}

func (m *MaskingDataCollector) SetNext(job Job) {
	m.next = job
}

func (m *MaskingDataCollector) DataCollectorJobName() string {
	return "masking data collector"
}
