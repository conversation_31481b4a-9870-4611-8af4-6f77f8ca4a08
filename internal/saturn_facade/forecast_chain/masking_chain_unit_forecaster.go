package forecast_chain

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/go-redis"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/monitor"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/chargeclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lpsclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/address"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/config"
	whitelist2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/whitelist"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/masking_forecast"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/masking_forecast/forecast_volume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/schedule_visual"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/schedule_visual/schedule_stat"
	allocation2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/forecast/service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/whitelist"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/mockutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/requestid"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/monitoring"
	"github.com/gammazero/workerpool"
	"strconv"
)

// 定义forecaster，实现责任链单元，用来进行预测，本处为masking实现
type MaskingForecaster struct {
	next                    Job
	mainTaskService         service.AllocateForecastTaskConfigService
	lpsApi                  lpsclient.LpsApi
	AllocationConfigRepo    config.AllocationConfigRepo
	RedisClient             *redis.Client
	AddrRepo                address.AddrRepo
	AllocateForecastService masking_forecast.AllocateForecastService
	ScheduleVisualStat      schedule_visual.ScheduleCountStatInterface
}

func NewMaskingForecaster(
	mainTaskService service.AllocateForecastTaskConfigService,
	lpsApi lpsclient.LpsApi,
	AllocationConfigRepo config.AllocationConfigRepo,
	RedisClient *redis.Client,
	AddrRepo address.AddrRepo,
	AllocateForecastService masking_forecast.AllocateForecastService,
	ScheduleVisualStat schedule_visual.ScheduleCountStatInterface) *MaskingForecaster {
	return &MaskingForecaster{
		mainTaskService:         mainTaskService,
		lpsApi:                  lpsApi,
		AllocationConfigRepo:    AllocationConfigRepo,
		RedisClient:             RedisClient,
		AddrRepo:                AddrRepo,
		AllocateForecastService: AllocateForecastService,
		ScheduleVisualStat:      ScheduleVisualStat,
	}
}

func (m *MaskingForecaster) ExecuteJob(ctx context.Context, inv *Invocation) *srerr.Error {
	/*
		1.根据sub task获取main task, 并转换成mask task config
		2.开始预测
		3.执行下一环
	*/
	subTask := inv.SubTask
	logger.CtxLogInfof(ctx, "job:%v|sub task:%v|start to forecast", m.ForecasterJobName(), subTask.Id)
	//将预测任务的id传入ctx中
	ctx = context.WithValue(ctx, forecast_volume.CtxMaskForecastKey, subTask.MainTaskId)
	//1.根据sub task获取main task, 并转换成mask task config
	taskConfig, err := m.mainTaskService.GetForecastTaskConfigEntityById(ctx, int64(subTask.MainTaskId))
	if err != nil {
		logger.CtxLogErrorf(ctx, "stop at:%v|GetForecastTaskConfigsByStatus fail|err=%v", m.ForecasterJobName(), err)
		return err
	}

	mockConfig := configutil.GetAllocateForecastUseMockConfig(ctx)
	if mockConfig.Switch {
		ctx = addMockInContext(ctx, mockConfig)
	}

	//初始化sync map，拿来存储预测结果，读写并发安全
	forecastCounter := masking_forecast.NewAllocateForecastCounter()
	historicalCounter := masking_forecast.NewAllocateHistoricalCounter()

	productInfo, err := m.lpsApi.GetProductDetail(ctx, taskConfig.BaseInfo.MaskProductId)
	if err != nil {
		logger.CtxLogErrorf(ctx, "stop at %:v|GetProductDetail fail|err=%v", m.ForecasterJobName(), err)
		return err
	}

	allocateConf, confErr := m.AllocationConfigRepo.GetAllocationConfigByMaskProductID(ctx, int64(taskConfig.BaseInfo.MaskProductId))
	if confErr != nil {
		logger.CtxLogErrorf(ctx, "stop at:%v|get allocation config fail|err=%v", m.ForecasterJobName(), confErr)
		return srerr.With(srerr.AllocationConfigError, taskConfig.BaseInfo.MaskProductId, confErr)
	}

	// get forecast rule
	forecastRule, ruleErr := m.convertToRule(ctx, taskConfig)
	if ruleErr != nil {
		logger.CtxLogErrorf(ctx, "stop at:%v|convertToRule fail|err=%v", m.ForecasterJobName(), ruleErr)
		return ruleErr
	}

	mfConf := configutil.GetMaskingForecastConf(ctx)
	workerPool := workerpool.New(mfConf.RateBurst)
	softSrv := allocation2.NewSoftRuleService(
		forecast_volume.NewAllocateForecastVolumeCounterImpl(),
		chargeclient.NewChargeApiImpl(),
		whitelist.NewShopWhitelistServiceImpl(whitelist2.NewShopWhitelistRepoImpl()),
	)
	var currentOrderCount int64
	//2.遍历订单数，对每一单进行预测
	for hbResult := range inv.HbResults {
		myHbResult := hbResult
		workerPool.Submit(func() {
			orderTab, cErr := m.TransferResultToTab(ctx, myHbResult)
			if cErr != nil {
				logger.CtxLogErrorf(ctx, "convertToAllocateOrderDataTab fail|err=%v", cErr)
				return
			}
			newRequestID := fmt.Sprintf("%s|%v", requestid.GetFromCtx(ctx), orderTab.OrderId)
			orderCtx := logger.NewLogContext(ctx, newRequestID)
			orderCtx = requestid.SetToCtx(orderCtx, newRequestID)
			orderCtx, endFunc := monitor.AwesomeReportTransactionStart2(orderCtx)

			orderEntity, err := ConvertHbResultToAllocateEntity(orderTab)
			if err != nil {
				logger.CtxLogErrorf(orderCtx, "convertToAllocateOrderDataEntity fail|err=%v", err)
				endFunc(monitoring.CatModuleMaskingForecast, "OrderForecast", monitoring.StatusError, err.Error())
				return
			}

			result, fErr := m.AllocateForecastService.ForecastAllocate(orderCtx, orderEntity, taskConfig, forecastRule, allocateConf, productInfo, softSrv)
			if fErr != nil {
				// 内层Forecast初始化了result，返回error时会返回基础result信息
				forecastCounter.StatisticsForecastRank(orderCtx, result)
				historicalCounter.StatisticsHistoricalRank(orderCtx, result)
				logger.CtxLogErrorf(orderCtx, "ForecastAllocate fail|err=%v", fErr)
				endFunc(monitoring.CatModuleMaskingForecast, "OrderForecast", monitoring.StatusError, fErr.Error())
				return
			}
			forecastCounter.StatisticsForecastRank(orderCtx, result)
			historicalCounter.StatisticsHistoricalRank(orderCtx, result)
			endFunc(monitoring.CatModuleMaskingForecast, "OrderForecast", monitoring.StatusSuccess, "")

			//手动释放
			orderTab = nil
			myHbResult = nil
		})
		currentOrderCount += 1
		//每1000条更新一次
		if currentOrderCount%1000 == 0 {
			if configutil.IsOpenScheduleVisualSwitch(ctx, schedule_stat.GetBusinessTypeName(schedule_stat.AllocateForecast)) {
				// 必须用IncrBy方法，该task有分片
				m.RedisClient.IncrBy(ctx, fmt.Sprintf(constant.TaskScheduleCurrentOrderNum, taskConfig.BaseInfo.Id), currentOrderCount)
			}
			currentOrderCount = 0
		}
	}
	workerPool.StopWait()
	//SSCSMR-1480 可视化上线后兼容 '保存当前总单量', 且最后还要判断一次currentOrderCount是否不为0
	if configutil.IsOpenScheduleVisualSwitch(ctx, schedule_stat.GetBusinessTypeName(schedule_stat.AllocateForecast)) && currentOrderCount != 0 {
		m.RedisClient.IncrBy(ctx, fmt.Sprintf(constant.TaskScheduleCurrentOrderNum, taskConfig.BaseInfo.Id), currentOrderCount)
	}
	//SSCSMR-1480: 可视化上线后兼容'保存过程数据'
	if configutil.IsOpenScheduleVisualSwitch(ctx, schedule_stat.GetBusinessTypeName(schedule_stat.AllocateForecast)) {
		syncErr := m.ScheduleVisualStat.SyncStatResult(ctx, schedule_stat.AllocateForecast, strconv.FormatInt(int64(taskConfig.BaseInfo.Id), 10))
		if syncErr != nil {
			logger.CtxLogErrorf(ctx, "masking forecaster|sync schedule visual stat error:%v", syncErr)
		}
	}
	//将结果保存起来
	inv.ExtendInfo.CurrentTotalCount = currentOrderCount

	//3.将统计结果传入下一环节中
	inv.SetMaskingResults(forecastCounter, historicalCounter)
	return m.next.ExecuteJob(ctx, inv)
}

func (m *MaskingForecaster) SetNext(job Job) {
	m.next = job
}

func (m *MaskingForecaster) ForecasterJobName() string {
	return "masking forecaster"
}

func addMockInContext(ctx context.Context, mockConfig configutil.AllocateForecastUseMockConfig) context.Context {
	mockValues := map[string]string{
		mockutil.MockRequestID:  mockConfig.MockRequestID,
		mockutil.MockSystemsKey: mockConfig.MockSystemsKey,
		mockutil.MockTypeKey:    mockConfig.MockTypeKey,
	}
	ctx = context.WithValue(ctx, mockutil.CtxMock, mockValues)

	return ctx
}
