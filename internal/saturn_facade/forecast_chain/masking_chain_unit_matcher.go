package forecast_chain

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/forecasting_sub_task"
	schema "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/allocation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/forecast/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/forecast/service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/requestid"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"hash/crc32"
	"strconv"
)

// 定义TaskMatcher，实现责任链单元，用来保存结果到db，本处为masking实现
type MaskingTaskMatcher struct {
	subTaskService  forecasting_sub_task.ForecastingSubTaskService
	next            Job
	mainTaskService service.AllocateForecastTaskConfigService
}

func NewMaskingTaskMatcher(
	subTaskService forecasting_sub_task.ForecastingSubTaskService,
	mainTaskService service.AllocateForecastTaskConfigService) *MaskingTaskMatcher {
	return &MaskingTaskMatcher{
		subTaskService:  subTaskService,
		mainTaskService: mainTaskService,
	}
}

// 匹配子任务，如果匹配期间出错，返回错误且不更改sub&&main task状态；如果调用下一job模块出错，将sub&&main task改为failed态
func (m *MaskingTaskMatcher) ExecuteJob(ctx context.Context, inv *Invocation) *srerr.Error {
	/*
		1.校验main task是否准备完毕
		2.匹配task
		3.更新sub task
		4.更新main task
		5.执行下一环
		6.（如果出错）更新sub task && main task状态
	*/
	subTask := inv.SubTask
	//1.校验main task volume是否加载完毕
	taskConfig, err := m.mainTaskService.GetForecastTaskConfigEntityById(ctx, int64(subTask.MainTaskId))
	if err != nil {
		logger.CtxLogErrorf(ctx, "GetForecastTaskConfigsByStatus fail|err=%v", err)
		return err
	}

	taskRequestID := fmt.Sprintf("%s|task_id=%d", requestid.GetFromCtx(ctx), taskConfig.BaseInfo.Id)
	ctx = logger.NewLogContext(ctx, taskRequestID)
	ctx = requestid.SetToCtx(ctx, taskRequestID)

	if taskConfig.BaseInfo.ConfigSyncStatus != constant.NoNeedToSyncConfigStatus &&
		taskConfig.BaseInfo.ConfigSyncStatus != constant.CompleteSyncConfigStatus {
		logger.CtxLogInfof(ctx, "Task not ready to run, id=%d", taskConfig.BaseInfo.Id)
		return srerr.New(srerr.TaskNotReadyError, nil, "local volume status is not complete")
	}
	//2.匹配sub task
	hashValue := crc32.ChecksumIEEE([]byte(strconv.FormatUint(subTask.Id, 10)))
	if hashValue%uint32(subTask.TotalShardingNum) != uint32(subTask.ShardingNo) {
		//退出
		logger.CtxLogErrorf(ctx, "stop at:%v, not match", m.TaskMatcherJobName())
		return srerr.New(srerr.NotMatchError, nil, "hash value of task:%v doesn't match sharding no:%v", subTask.Id, subTask.ShardingNo)
	}
	logger.CtxLogInfof(ctx, "job:%v|sub task id:%v|match success", m.TaskMatcherJobName(), subTask.Id)

	//3.匹配成功，更新sub task状态，抢占任务
	preVersion := subTask.Version
	nextVersion := subTask.Version + 1 //version加一,避免aba
	condition := map[string]interface{}{
		"id=?":          subTask.Id,
		"task_status=?": constant.TaskConfigStatusPending,
		"version=?":     preVersion,
	}
	value := map[string]interface{}{
		"task_status": constant.TaskConfigStatusProcess,
		"version":     nextVersion,
	}
	if err := m.subTaskService.UpdateSubTaskByMap(ctx, condition, value); err != nil {
		logger.CtxLogErrorf(ctx, "stop at:%v|failed to update sub task:%v, err:%v", m.TaskMatcherJobName(), subTask.Id, err)
		return err
	}
	//4.事件驱动,更新main task
	if taskConfig.BaseInfo.Status != constant.TaskConfigStatusPending {
		logger.CtxLogInfof(ctx, "ExecuteMatcherJob|main task not pending, no need to update status")
	} else {
		condition := map[string]interface{}{
			"task_status=?": constant.TaskConfigStatusPending,
			"id=?":          taskConfig.BaseInfo.Id,
		}
		value := map[string]interface{}{
			"task_status": constant.TaskConfigStatusProcess,
		}
		//todo:SSCSMR-1480:是否需要根据rows affected == 0来判断update失败?
		if err := m.mainTaskService.UpdateTaskWithCondition(ctx, condition, value, uint64(taskConfig.BaseInfo.MaskProductId)); err != nil {
			logger.CtxLogErrorf(ctx, "ExecuteMatcherJob|failed to update main task:%v, err:%v", subTask.MainTaskId, err)
		}
	}
	needSleep := NeedSleep(ctx)
	useHardRate := UseHardRate(ctx, taskConfig)
	//设置extend info
	inv.SetExtendInfo(&ExtendInfo{
		MainTaskId:       uint64(taskConfig.BaseInfo.Id),
		MaskingProductId: uint64(taskConfig.BaseInfo.MaskProductId),
		NeedSleep:        needSleep,
		UseHardRate:      useHardRate,
	})

	//err不为空，预测失败，将sub task和main task更新为failed"
	eErr := m.next.ExecuteJob(ctx, inv)
	if eErr != nil {
		//预测失败，更新main task到failed
		mainTaskConfig, err := m.mainTaskService.GetForecastTaskConfigEntityById(ctx, int64(subTask.MainTaskId))
		if err != nil {
			logger.CtxLogErrorf(ctx, "ExecuteChain|failed to get main task:%v, by sub task:%v, err:%v", subTask.MainTaskId, subTask.Id, err)
		}
		condition := map[string]interface{}{
			"task_status=?": constant.TaskConfigStatusProcess,
			"id=?":          mainTaskConfig.BaseInfo.Id,
		}
		value := map[string]interface{}{
			"task_status": constant.TaskConfigStatusFailed,
		}
		if err := m.mainTaskService.UpdateTaskWithCondition(ctx, condition, value, uint64(mainTaskConfig.BaseInfo.MaskProductId)); err != nil {
			logger.CtxLogErrorf(ctx, "ExecuteChain|failed to update main task:%v, err:%v", subTask.MainTaskId, err)
		}
		//预测失败，更新sub task到failed
		condition = map[string]interface{}{"id=?": subTask.Id}
		value = map[string]interface{}{"task_status": constant.TaskConfigStatusFailed}
		if err := m.subTaskService.UpdateSubTaskByMap(ctx, condition, value); err != nil {
			logger.CtxLogErrorf(ctx, "ExecuteChain|failed to update sub task:%v, err:%v", subTask.Id, err)
		}
	}

	return eErr
}

func (m *MaskingTaskMatcher) SetNext(job Job) {
	m.next = job
}

func (m *MaskingTaskMatcher) TaskMatcherJobName() string {
	return "masking task matcher"
}

func NeedSleep(ctx context.Context) bool {
	//todo:可以考虑根据"配置是否开启硬性校验"及"是否开启最小运费"来sleep生产者
	////not run soft only toggle, need sleep
	//if !mainTaskConfig.BaseInfo.RunSoftRuleOnlyToggle {
	//	return true
	//}
	////open cheapest shipping fee, need sleep
	//if mainTaskConfig.AllocationRuleConfig.RuleDetail.CheapestFeeEnable {
	//	return true
	//}

	//1.Apollo控制是否需要sleep生产者
	conf := configutil.GetMaskingForecastConf(ctx)
	return conf.NeedSleep
}

func UseHardRate(ctx context.Context, mainTaskConfig *schema.AllocateForecastTaskConfigEntity) bool {
	//not run soft only toggle, need sleep
	if mainTaskConfig.BaseInfo != nil && !mainTaskConfig.BaseInfo.RunSoftRuleOnlyToggle {
		return true
	}
	//open cheapest shipping fee, need sleep
	if mainTaskConfig.AllocationRuleConfig != nil && mainTaskConfig.AllocationRuleConfig.RuleDetail != nil && mainTaskConfig.AllocationRuleConfig.RuleDetail.CheapestFeeEnable {
		return true
	}
	return false
}
