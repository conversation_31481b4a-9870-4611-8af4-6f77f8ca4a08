package forecast_chain

import (
	"context"
	"strconv"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/monitor"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/forecasting_sub_task"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/forecast/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/forecast/service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/monitoring"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	"github.com/bytedance/sonic"
)

// 定义dataStorage，实现责任链单元，用来保存结果到db，本处为masking实现
type MaskingDataStorage struct {
	AllocateRankService service.AllocateRankService
	subTaskService      forecasting_sub_task.ForecastingSubTaskService
	mainTaskService     service.AllocateForecastTaskConfigService
}

func NewMaskingDataStorage(
	AllocateRankService service.AllocateRankService,
	subTaskService forecasting_sub_task.ForecastingSubTaskService,
	mainTaskService service.AllocateForecastTaskConfigService) *MaskingDataStorage {
	return &MaskingDataStorage{
		AllocateRankService: AllocateRankService,
		subTaskService:      subTaskService,
		mainTaskService:     mainTaskService,
	}
}

func (m *MaskingDataStorage) ExecuteJob(ctx context.Context, inv *Invocation) *srerr.Error {
	subTask := inv.SubTask
	logger.CtxLogInfof(ctx, "job:%v|sub task:%v| start to store data into db", m.DataStorageJobName(), subTask.Id)
	//1.更新预测结果到db
	if inv.ForecastCounter != nil {
		if err := m.AllocateRankService.IncrForecastRank(ctx, &inv.ForecastCounter.StatisticMap); err != nil {
			logger.CtxLogErrorf(ctx, "stop at:%v|failed to incr forecast results, err=%v", m.DataStorageJobName(), err)
			return err
		}
	}
	if inv.HistoricalCounter != nil {
		// 统计结果
		if err := m.AllocateRankService.IncrHistoricalRank(ctx, &inv.HistoricalCounter.StatisticMap); err != nil {
			logger.CtxLogErrorf(ctx, "stop at:%v|failed to incr historical results, err=%v", m.DataStorageJobName(), err)
			return err
		}
	}
	//2.更新sub task
	mfConf := configutil.GetMaskingForecastConf(ctx)
	mfConfStr, mErr := sonic.MarshalString(mfConf)
	if mErr != nil {
		logger.CtxLogErrorf(ctx, "failed to marshal masking forecast conf, err=%v", mErr)
	}
	//子任务数据汇总统计到主任务中，并计算每一条数据的平均处理时长，记录Apollo配置,平均时长最短的一条就是最好的Apollo配置
	subTaskExtendInfo := forecasting_sub_task.ExtendInfo{
		ApolloConfig:      mfConfStr,
		CostTime:          timeutil.GetCurrentUnixTimeStamp(ctx) - subTask.JobStartTime,
		CurrentTotalCount: inv.ExtendInfo.CurrentTotalCount,
	}
	subTaskExtendInfoStr, mErr := sonic.MarshalString(subTaskExtendInfo)
	if mErr != nil {
		logger.CtxLogErrorf(ctx, "failed to marshal subtask extend info, err=%v", mErr)
	}
	subTask.TaskStatus = constant.TaskConfigStatusComplete
	condition := map[string]interface{}{
		"id=?": subTask.Id,
	}
	value := map[string]interface{}{
		"task_status":      constant.TaskConfigStatusComplete,
		"mtime":            timeutil.GetCurrentUnixTimeStamp(ctx),
		"last_update_time": timeutil.GetCurrentUnixTimeStamp(ctx),
		"extend_info":      subTaskExtendInfoStr,
	}
	if err := m.subTaskService.UpdateSubTaskByMap(ctx, condition, value); err != nil {
		logger.CtxLogErrorf(ctx, "MaskingDataStorage|failed to update sub task to complete|err=%v", err)
	}
	//3.检查main task，如果所有的sub task 都complete了，则更新main task 到complete
	condition = map[string]interface{}{
		"main_task_id=?":     subTask.MainTaskId,
		"module_name=?":      forecasting_sub_task.ModuleMaskingForecast,
		"task_status in (?)": []int{constant.TaskConfigStatusProcess, constant.TaskConfigStatusPending},
	}
	tabs, err := m.subTaskService.SelectSubTask(ctx, condition)
	if len(tabs) != 0 || err != nil {
		logger.CtxLogErrorf(ctx, "MaskingDataStorage| len(tabs)=%v, err=%v, main task not complete, will not set main task to 'complete'", len(tabs), err)
	} else {
		condition := map[string]interface{}{
			"id=?":          subTask.MainTaskId,
			"task_status=?": constant.TaskConfigStatusProcess,
		}
		value := map[string]interface{}{
			"task_status":   constant.TaskConfigStatusComplete,
			"complete_time": uint32(timeutil.GetCurrentUnixTimeStamp(ctx)),
		}
		if err := m.mainTaskService.UpdateTaskWithCondition(ctx, condition, value, inv.ExtendInfo.MaskingProductId); err != nil {
			logger.CtxLogErrorf(ctx, "MaskingDataStorage| failed to update main task into complete, err:%v", err)
		}
	}
	_ = monitor.AwesomeReportEvent(ctx, monitoring.CatModuleTask, "MaskingForecast", monitoring.StatusSuccess, strconv.FormatUint(subTask.Id, 10))
	return nil
}

func (m *MaskingDataStorage) SetNext(job Job) {
}

func (m *MaskingDataStorage) DataStorageJobName() string {
	return "masking data storage"
}
