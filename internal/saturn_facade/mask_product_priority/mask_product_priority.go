package mask_product_priority

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/chassis-saturn-server/saturn"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	"git.garena.com/shopee/bg-logistics/go/scormv2"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/productpriority"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/productpriority/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
)

type MaskProductPriorityJob struct {
}

func NewMaskCronJob() *MaskProductPriorityJob {
	return new(MaskProductPriorityJob)
}

func (m *MaskProductPriorityJob) Name() string {
	//panic("implement me")
	return "MaskProductPriorityCronJob"
}

func (m MaskProductPriorityJob) RpcHandle(ctx context.Context, args saturn.JobArgs) error {
	dbConn, err := dbutil.MasterDB(ctx, productpriority.LogisticProductPriorityTabHook)
	if err != nil {
		return err
	}
	// 查找将要生效的product priority
	now := recorder.Now(ctx).Unix()
	upcomingToActive := []*productpriority.LogisticProductPriorityTab{}
	upcomingCondition := map[string]interface{}{}
	upcomingCondition["config_status = ?"] = entity.Upcoming
	upcomingCondition["effective_start_time < ?"] = now
	logger.CtxLogInfof(ctx, "UpdatePriorityStatus| upcoming condition:%v", upcomingCondition)
	err = dbutil.Select(ctx, productpriority.LogisticProductPriorityTabHook, upcomingCondition, &upcomingToActive)
	if err != nil {
		logger.CtxLogErrorf(ctx, "UpdatePriorityStatus| take record err:%v", err)
		return err
	}
	fmt.Println("check ", upcomingToActive)
	if len(upcomingToActive) == 0 {
		logger.CtxLogInfof(ctx, "len(upcomingToActive) == 0")
		return nil
	}

	activeToExpired := []*productpriority.LogisticProductPriorityTab{}
	for _, record := range upcomingToActive {
		// querys
		row := []*productpriority.LogisticProductPriorityTab{}
		wheres := map[string]interface{}{}
		wheres["config_status = ?"] = entity.Active
		wheres["mask_product_id = ?"] = record.MaskProductID
		wheres["shop_group_id = ? "] = record.ShopGroupID
		err = dbutil.Select(ctx, productpriority.LogisticProductPriorityTabHook, wheres, &row)
		if err != nil {
			logger.CtxLogErrorf(ctx, "UpdatePriorityStatus| take record err:%v", err)
			return err
		}
		activeToExpired = append(activeToExpired, row...)
	}
	fmt.Printf("cond %v %v\n", getCondition(upcomingToActive), getCondition(activeToExpired))
	logger.CtxLogInfof(ctx, "cond %v %v\n", getCondition(upcomingToActive), getCondition(activeToExpired))
	ctx = scormv2.BindContext(ctx, dbConn)
	err = scormv2.PropagationRequired(ctx, func(ctx context.Context) error {
		tx := scormv2.Context(ctx)
		obj := productpriority.LogisticProductPriorityTab{ConfigStatus: int32(entity.Active), Mtime: now}
		upd := dbutil.UpdateIncludeMapper(&obj, "config_status", "mtime")
		if err1 := tx.Table(productpriority.LogisticProductPriorityTabHook.TableName()).Debug().Where("id in (?)", getCondition(upcomingToActive)).Updates(upd).GetError(); err1 != nil {
			return err1
		}
		//
		if len(activeToExpired) > 0 {
			obj := productpriority.LogisticProductPriorityTab{ConfigStatus: int32(entity.Expired), Mtime: now}
			upd := dbutil.UpdateIncludeMapper(&obj, "config_status", "mtime")
			if err1 := tx.Table(productpriority.LogisticProductPriorityTabHook.TableName()).Debug().Where("id in (?)", getCondition(activeToExpired)).Updates(upd).GetError(); err1 != nil {
				return err1
			}
		}

		return nil

	})
	return err
}

func getCondition(rows []*productpriority.LogisticProductPriorityTab) []uint64 {
	idlist := []uint64{}
	for _, row := range rows {
		idlist = append(idlist, row.ID)
	}
	//
	return idlist
}
