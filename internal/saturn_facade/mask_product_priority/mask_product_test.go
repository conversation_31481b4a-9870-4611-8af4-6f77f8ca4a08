package mask_product_priority

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/chassis"
	"git.garena.com/shopee/bg-logistics/go/chassis-saturn-server/saturn"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"testing"
)

func initdepends() {
	// init
	if err := chassis.Init(
		chassis.WithChassisConfigPrefix("admin_server"),
		chassis.WithDefaultProviderHandlerChain(
		//chassis_middleware.JwtTokenHandleKey,
		),
	); err != nil {
		panic(err)
	}
	err := configutil.Init()
	if err != nil {
		panic(err)
	}

	dbutil.InitTaskDb()
}

func TestName(t *testing.T) {
	initdepends()
	obj := new(MaskProductPriorityJob)
	err := obj.RpcHandle(context.TODO(), saturn.JobArgs{})
	fmt.Println(err)

}
