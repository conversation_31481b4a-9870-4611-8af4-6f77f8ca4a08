package schedule_visual

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis-saturn-server/saturn"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	internal_constant "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	svService "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/schedule_visual"
)

type ClearScheduleCountTask struct {
	ScheduleVisualTaskService svService.ScheduleVisualTaskServiceInterface
}

func NewClearScheduleCountTask(scheduleVisualTaskService svService.ScheduleVisualTaskServiceInterface) *ClearScheduleCountTask {
	return &ClearScheduleCountTask{
		ScheduleVisualTaskService: scheduleVisualTaskService,
	}
}

func (s *ClearScheduleCountTask) Name() string {
	return internal_constant.TaskClearScheduleVisualCount
}

func (s *ClearScheduleCountTask) RpcHandle(ctx context.Context, args saturn.JobArgs) error {
	logger.CtxLogInfof(ctx, "ClearScheduleCountTask|start|ShardingParam:%v", args.ShardingParam)
	err := s.ScheduleVisualTaskService.ClearScheduleCount(ctx)
	if err != nil {
		logger.CtxLogErrorf(ctx, "ClearScheduleCountTask|clear schedule count err:%v", err)
		return err
	}
	logger.CtxLogInfof(ctx, "ClearScheduleCountTask|end")
	return nil
}
