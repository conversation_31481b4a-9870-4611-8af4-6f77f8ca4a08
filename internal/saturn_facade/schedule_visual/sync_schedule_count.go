package schedule_visual

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis-saturn-server/saturn"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	internal_constant "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/schedule_visual/schedule_stat"
	svService "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/schedule_visual"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	"strings"
	"time"
)

type SyncScheduleCountTask struct {
	ScheduleVisualTaskService svService.ScheduleVisualTaskServiceInterface
}

func NewSyncScheduleCountTask(scheduleVisualTaskService svService.ScheduleVisualTaskServiceInterface) *SyncScheduleCountTask {
	return &SyncScheduleCountTask{
		ScheduleVisualTaskService: scheduleVisualTaskService,
	}
}

func (s *SyncScheduleCountTask) Name() string {
	return internal_constant.TaskSyncScheduleVisualCount
}

func (s *SyncScheduleCountTask) RpcHandle(ctx context.Context, args saturn.JobArgs) error {
	logger.CtxLogInfof(ctx, "SyncScheduleCountTask|start|ShardingParam:%v", args.ShardingParam)
	statTimeList := strings.Split(args.ShardingParam, ",")
	if len(statTimeList) < 1 || statTimeList[0] == "" {
		statTimeList = []string{}
		currentHour := timeutil.FormatDateTimeByFormat(timeutil.GetLocalTime(ctx), schedule_stat.MaskingScheduleStatHourTimeFormat)
		preHour := timeutil.FormatDateTimeByFormat(timeutil.GetLocalTime(ctx).Add(-1*3600*time.Second), schedule_stat.MaskingScheduleStatHourTimeFormat)
		statTimeList = append(statTimeList, preHour, currentHour)
	}
	err := s.ScheduleVisualTaskService.SyncScheduleCount(ctx, statTimeList)
	if err != nil {
		logger.CtxLogErrorf(ctx, "SyncScheduleCountTask|sync schedule count err:%v", err)
		return err
	}
	logger.CtxLogInfof(ctx, "SyncScheduleCountTask|end")
	return nil
}
