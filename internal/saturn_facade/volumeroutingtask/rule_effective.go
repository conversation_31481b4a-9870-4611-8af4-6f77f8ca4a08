package volumeroutingtask

import (
	"context"
	"errors"
	"git.garena.com/shopee/bg-logistics/go/chassis-saturn-server/saturn"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/volumerouting"
)

type RuleEffectiveTask struct {
	ZoneMgr volumerouting.ZoneRuleMgr
}

func NewRuleEffectiveTask(ZoneMgr volumerouting.ZoneRuleMgr) *RuleEffectiveTask {
	return &RuleEffectiveTask{
		ZoneMgr: ZoneMgr,
	}
}
func (p *RuleEffectiveTask) Name() string {
	return constant.TaskNameRuleEffective
}

// configuration example: 0=0##1##2##3,1=4##5##6##7,2=8##9
func (p *RuleEffectiveTask) RpcHandle(ctx context.Context, args saturn.JobArgs) error {
	srerr := p.ZoneMgr.HandleRuleEffective(ctx)
	if srerr != nil {
		return errors.New(srerr.Error())
	}
	return nil
}
