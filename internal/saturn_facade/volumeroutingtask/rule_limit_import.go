package volumeroutingtask

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/chassis-saturn-server/saturn"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/taskschema"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/volumerouting"
	jsoniter "github.com/json-iterator/go"
)

type RuleLimitImportTask struct {
	RuleMgr volumerouting.ZoneRuleMgr
}

func NewRuleLimitImportTask(RuleMgr volumerouting.ZoneRuleMgr) *RuleLimitImportTask {
	return &RuleLimitImportTask{
		RuleMgr: RuleMgr,
	}
}
func (p *RuleLimitImportTask) Name() string {
	return constant.TaskNameZoneRuleLimitImport
}

func (p *RuleLimitImportTask) MsgHandle(ctx context.Context, message *saturn.SaturnMessage) *saturn.SaturnReply {
	var taskParam taskschema.VolumeTaskParam
	if err := jsoniter.Unmarshal(message.MsgText, &taskParam); err != nil {
		return &saturn.SaturnReply{
			Retcode: -1,
			Message: fmt.Sprintf("message unmarshal failed | msg_text=%s | err=%s", string(message.MsgText), err),
		}
	}
	_ = p.RuleMgr.HandleZoneRuleLimitImportTask(ctx, taskParam.TaskId)
	return &saturn.SaturnReply{
		Retcode: saturn.SUCCESS,
		Message: "SUCCESS",
	}
}
