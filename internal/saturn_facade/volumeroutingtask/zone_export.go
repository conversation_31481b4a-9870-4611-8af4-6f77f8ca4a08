package volumeroutingtask

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/chassis-saturn-server/saturn"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/taskschema"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/volumerouting"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
	jsoniter "github.com/json-iterator/go"
)

type ZoneExportTask struct {
	ZoneMgr volumerouting.ZoneManager
}

func NewZoneExportTask(ZoneMgr volumerouting.ZoneManager) *ZoneExportTask {
	return &ZoneExportTask{
		ZoneMgr: ZoneMgr,
	}
}

func (p *ZoneExportTask) Name() string {
	return constant.TaskNameZoneExport
}

func (p *ZoneExportTask) MsgHandle(ctx context.Context, message *saturn.SaturnMessage) *saturn.SaturnReply {
	var taskParam taskschema.VolumeTaskParam
	if err := jsoniter.Unmarshal(message.MsgText, &taskParam); err != nil {
		logger.CtxLogErrorf(ctx, "unmarshal volume task param fail, message:%s, err:%+v", objutil.JsonString(message), err)
		return &saturn.SaturnReply{
			Retcode: -1,
			Message: fmt.Sprintf("message unmarshal failed | msg_text=%s | err=%s", string(message.MsgText), err),
		}
	}
	ctx = logger.NewLogContext(ctx, taskParam.TaskId)
	_ = p.ZoneMgr.HandleZoneExportTask(ctx, taskParam.TaskId)
	return &saturn.SaturnReply{
		Retcode: saturn.SUCCESS,
		Message: "SUCCESS",
	}
}
