package volumeroutingtask

import (
	"context"
	"errors"
	"git.garena.com/shopee/bg-logistics/go/chassis-saturn-server/saturn"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/volumerouting"
)

type ZoneImportTask struct {
	ZoneMgr volumerouting.ZoneManager
}

func NewZoneImportTask(ZoneMgr volumerouting.ZoneManager) *ZoneImportTask {
	return &ZoneImportTask{
		ZoneMgr: ZoneMgr,
	}
}
func (p *ZoneImportTask) Name() string {
	return constant.TaskNameZoneImport
}

// configuration example: 0=0##1##2##3,1=4##5##6##7,2=8##9
// 对task_id 取摸，0~9, 最大并发为10 ，configured-cks

func (p *ZoneImportTask) RpcHandle(ctx context.Context, args saturn.JobArgs) error {
	srerr := p.ZoneMgr.HandleExecutableZoneImportTask(ctx, args)
	if srerr != nil {
		return errors.New(srerr.Error())
	}
	return nil
}
