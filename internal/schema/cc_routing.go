package schema

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/cc_routing_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/cc_routing"
)

type CreateCCRoutingRuleReq struct {
	ProductId   int                                 `json:"product_id" validate:"required"`
	RoutingType cc_routing_rule.CCRoutingType       `json:"routing_type" validate:"required,min=1"`
	RuleDetail  cc_routing_rule.CCRoutingRuleDetail `json:"rule_detail" validate:"required"`
}

type DeleteCCRoutingRuleReq struct {
	Id int `json:"id" validate:"required"`
}

type UpdateCCRoutingRuleReq struct {
	Id          int                                 `json:"id" validate:"required"`
	RoutingType cc_routing_rule.CCRoutingType       `json:"routing_type" validate:"required,min=1"`
	RuleDetail  cc_routing_rule.CCRoutingRuleDetail `json:"rule_detail" validate:"required"`
}

type GetCCRoutingRuleReq struct {
	Id int `json:"id" validate:"required"`
}

type ListCCRoutingRulesReq struct {
	ProductId int   `json:"product_id"`
	Offset    int64 `json:"offset" validate:"min=0"`
	Size      int64 `json:"size" validate:"required"`
}

type ListCCRoutingRulesRsp struct {
	List   []cc_routing_rule.CCRoutingRule `json:"list"`
	Offset int64                           `json:"offset"`
	Total  int64                           `json:"total"`
	Size   int64                           `json:"size"`
}

// 批量导入规则请求
type ImportRuleReq struct {
	ProductId    int                           `form:"product_id" validate:"required"`
	RoutingType  cc_routing_rule.CCRoutingType `form:"routing_type" validate:"required,oneof=3 4 5"` // 3=ShopGroup, 4=Category, 5=WeightCategory
	ValidateOnly bool                          `form:"validate_only"`                                // 是否只验证不保存（默认false-正式导入）
	FileData     []byte                        `json:"-"`                                            // 文件内容，从multipart form读取
}

// 下载模板请求
type DownloadTemplateReq struct {
	RoutingType cc_routing_rule.CCRoutingType `form:"routing_type" json:"routing_type" validate:"required,oneof=3 4 5"` // 3=ShopGroup, 4=Category, 5=WeightCategory
}

// CC列表响应（与服务层对接）
type CCListResp struct {
	List []*cc_routing.CCListItem `json:"list"`
}
