package select_lane

import (
	"context"
	"fmt"
	"strconv"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/fulfilment_event_trace/trace_sdk"
	pb "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing-protocol/protocol/go"
	"google.golang.org/protobuf/proto"

	entity2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/lh_capacity/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/locationzone"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/routing_log"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/routing_role"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/external_gateway/lfslib"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/traceutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/grpc_util"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/monitoring"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil/str"
)

func (s *SmartRoutingServiceImpl) ILHRouting(ctx context.Context, request *pb.ILHRoutingReq) (*pb.ILHRoutingRsp, error) {

	var (
		rsp       = &pb.ILHRoutingRsp{}
		requestId = request.GetHeader().GetRequestId()
		err       *srerr.Error
		logEntry  = &routing_log.RoutingLog{}
	)

	logEntry.ProductId = int(request.GetProductId())
	logEntry.FOrderId = request.GetPackageNo()

	reportData := fmt.Sprintf("Product:%d,PackageNo:%s", request.GetProductId(), request.GetPackageNo())
	reportCtx, endReport := traceutil.StartReport(ctx, requestId, trace_sdk.SMRRoutingEvent)
	defer func() {
		if err := traceutil.ReportPool.Submit(func() {
			endReport(reportCtx, request.GetPackageNo(), err)
		}); err != nil {
			logger.CtxLogErrorf(ctx, "exception report err=%v", err)
		}
	}()
	// 对CC Mode进行标记上报
	monitoring.ReportSuccess(ctx, monitoring.CatModuleILHRoutingCCMode, request.GetCcMode().String(), reportData)

	// check routing config and lanes
	if len(request.GetLaneList()) == 0 {
		monitoring.ReportError(ctx, monitoring.CatILHRoutingApi, monitoring.ILHRoutingError, fmt.Sprintf("%s,Error:%s", reportData, "lane list is empty"))
		rsp.Header = grpc_util.GenerateRespHeader(requestId, srerr.New(srerr.ParamErr, request.GetProductId(), "No any lane in request"))
		return rsp, nil
	}

	// CC Mode下需要检查参数是否完备
	if err = checkCCRoutingModeParameter(request); err != nil {
		monitoring.ReportError(ctx, monitoring.CatILHRoutingApi, monitoring.ILHRoutingError, fmt.Sprintf("%s,Error:%s", reportData, err))
		rsp.Header = grpc_util.GenerateRespHeader(requestId, err)
		return rsp, nil
	}

	var routingConfig *rule.RoutingConfig
	routingConfig, err = s.RoutingConfig.GetRoutingConfigCacheByProductID(ctx, int(request.GetProductId()))
	if err != nil {
		monitoring.ReportError(ctx, monitoring.CatILHRoutingApi, monitoring.ILHRoutingError, fmt.Sprintf("%s,Error:%s", reportData, err))
		rsp.Header = grpc_util.GenerateRespHeader(requestId, err)
		return rsp, nil
	}

	if !routingConfig.IsIlhSmartRouting() {
		// if no need ilh smart routing and only one lane, return this lane directly
		if len(request.GetLaneList()) == 1 {
			monitoring.ReportSuccess(ctx, monitoring.CatILHRoutingApi, monitoring.ILHRoutingSuccess, reportData)
			rsp.Header = grpc_util.GenerateRespHeader(requestId, nil)
			rsp.RoutingResult = proto.String(request.GetLaneList()[0])
		} else {
			// else return error
			err = srerr.New(srerr.TooManyAvailableLanes, request.GetProductId(), "Too may lanes, need smart routing")
			monitoring.ReportError(ctx, monitoring.CatILHRoutingApi, monitoring.ILHRoutingError, fmt.Sprintf("%s,Error:%s", reportData, "Too may lanes, need smart routing"))
			rsp.Header = grpc_util.GenerateRespHeader(requestId, err)
		}

		return rsp, nil
	}

	// 1 get lane info
	routingLaneInfos := make([]*rule.RoutingLaneInfo, 0, len(request.GetLaneList()))
	destinationPorts := make([]string, 0, len(request.GetLaneList()))
	for _, laneCode := range request.GetLaneList() {
		var routingLaneInfo *rule.RoutingLaneInfo
		routingLaneInfo, err = s.RoutingService.LoadRoutingLaneInfoByLaneCode(ctx, laneCode)
		if err != nil {
			monitoring.ReportError(ctx, monitoring.CatILHRoutingApi, monitoring.ILHRoutingError, fmt.Sprintf("%s,Error:%s", reportData, "Lane info not found"))
			rsp.Header = grpc_util.GenerateRespHeader(requestId, srerr.New(srerr.DataErr, nil, "Load lane info fail|lane code = %s", laneCode))
			return rsp, nil
		}

		// set line dg flag from request
		for _, lineInfo := range routingLaneInfo.LineList {
			// 当Line是DG相关的时候才需要赋值，否则统一赋值为Undefined
			if lineInfo.DgRelated == rule.DgRelated {
				lineInfo.DGFlag = rule.DGFlag(request.GetDgFlag())
			} else {
				lineInfo.DGFlag = rule.UndefinedDGFlag
			}
		}

		routingLaneInfos = append(routingLaneInfos, routingLaneInfo)
		destinationPorts = append(destinationPorts, routingLaneInfo.DestinationPort)
	}
	destinationPorts = objutil.RemoveDuplicateString(destinationPorts)
	roleMap := routing_role.UpdateLineResourceType(ctx, int(request.GetProductId()), rule.IlhRoutingType, routingLaneInfos, true, logEntry)

	if configutil.IsRevampILHRoutingEnabled(ctx, request.GetProductId()) {
		finalResult, err := s.revampILHRouting(ctx, request, routingLaneInfos, destinationPorts)
		if err != nil {
			monitoring.ReportError(ctx, monitoring.CatILHRoutingApi, monitoring.ILHRoutingError, fmt.Sprintf("%s,Error:%s", reportData, err))
			rsp.Header = grpc_util.GenerateRespHeader(requestId, srerr.New(srerr.RuleNotFound, request.GetProductId(), "revamp ilh routing fail"))
			return rsp, nil
		}
		rsp.Header = grpc_util.GenerateRespHeader(requestId, nil)
		rsp.RoutingResult = proto.String(finalResult.LaneCode)
		return rsp, nil
	}

	// 2 match routing rules
	ruleParam := routing.RuleMatchParam{
		ProductId:        request.GetProductId(),
		WhsID:            request.GetTwsCode(),
		DgType:           int(request.GetDgFlag()),
		DestinationPorts: destinationPorts,
		RoutingType:      rule.IlhRoutingType,
		IsMultiProduct:   true,
		ZoneType:         locationzone.RunningZoneType,
	}
	var routingRules []*rule.RoutingRuleParsed
	routingRules, err = s.RoutingRule.MatchRoutingRules(ctx, ruleParam)
	if err != nil {
		monitoring.ReportError(ctx, monitoring.CatILHRoutingApi, monitoring.ILHRoutingError, fmt.Sprintf("%s,Error:%s", reportData, "rule not found"))
		rsp.Header = grpc_util.GenerateRespHeader(requestId, srerr.New(srerr.RuleNotFound, request.GetProductId(), "rule not found"))
		return rsp, nil
	}

	var lmId string
	// 只有CC Routing Mode的时候才需要获取Sorting Code信息
	if request.GetCcMode() == pb.CCMode_CCRouting {
		lmId, err = s.getLmIDByServiceCode(ctx, request.GetServiceCode())
		if err != nil {
			errMsg := "load sorting code fail"
			monitoring.ReportError(ctx, monitoring.CatILHRoutingApi, monitoring.ILHRoutingError, fmt.Sprintf("%s,Error:%s", reportData, err))
			rsp.Header = grpc_util.GenerateRespHeader(requestId, srerr.New(srerr.RuleNotFound, request.GetProductId(), errMsg))
			return rsp, nil
		}
	}

	// 3 loop routing until success
	orderData := &rule.SmartRoutingOrderData{
		TwsCode:        request.GetTwsCode(),
		OrderWeight:    int(request.GetWeight()),
		ParcelQuantity: int(request.GetParcelQuantity()),
		LmId:           lmId,
		CCMode:         request.GetCcMode(),
	}
	var routingResult []*rule.RoutingLaneInfo
	for _, routingRule := range routingRules {
		// 3.1 get intersection lanes by destination ports
		ruleRoutingLanes := FilterLanesByDestPort(routingLaneInfos, routingRule.DestinationPort)
		if len(ruleRoutingLanes) == 0 {
			logger.CtxLogErrorf(ctx, "Rule [%d] have no destination port intersection", routingRule.ID)
			continue
		}

		// 3.2 routing
		SetupILHRoutingLogBasicInfo(logEntry, routingRule, roleMap)
		checkRuleCCModeStatus(ctx, request, routingRule)
		// 根据规则来判断要不要走CC Routing Mode，新的Rule兼容老的非CC Routing模式
		if routingRule.IsCCRoutingMode() {
			routingResult, err = s.RoutingService.CCModeILHRouting(ctx, int(request.GetProductId()), ruleRoutingLanes, routingRule, orderData, logEntry)
		} else {
			notOnlySPX := false
			routingResult, err = s.RoutingService.Routing(ctx, int(request.GetProductId()), true, ruleRoutingLanes, routingRule, orderData, logEntry, notOnlySPX)
		}
		if err != nil {
			logger.CtxLogErrorf(ctx, "Routing fail using rule [%d], routing lanes [%s]", routingRule.ID, str.JsonString(ruleRoutingLanes))
			continue
		}

		// break when get routing result
		if len(routingResult) != 0 {
			logger.CtxLogInfof(ctx, "Routing success by rule [%d], routing result [%s]", routingRule.ID, routingResult[0].LaneCode)
			break
		}
	}

	if len(routingResult) != 0 {
		rsp.Header = grpc_util.GenerateRespHeader(requestId, nil)
		rsp.RoutingResult = proto.String(routingResult[0].LaneCode)
		setupFinalRes(logEntry, routingResult)
		monitoring.ReportSuccess(ctx, monitoring.CatILHRoutingApi, monitoring.ILHRoutingSuccess, reportData)
	} else {
		rsp.Header = grpc_util.GenerateRespHeader(requestId, err)
		monitoring.ReportError(ctx, monitoring.CatILHRoutingApi, monitoring.ILHRoutingError, fmt.Sprintf("%s,Error:%s", reportData, err))
	}

	logger.CtxLogInfof(ctx, "Routing Log: %s", str.JsonString(logEntry))

	return rsp, nil
}

// checkCCRoutingModeParameter 校验CC Routing Mode下Parcel Quantity和Service Code是否有值
func checkCCRoutingModeParameter(request *pb.ILHRoutingReq) *srerr.Error {
	// 非CC Routing Mode不需要校验
	if request.GetCcMode() != pb.CCMode_CCRouting {
		return nil
	}

	if request.GetParcelQuantity() == 0 {
		return srerr.New(srerr.ParamInvalid, request.GetParcelQuantity(), "parcel quantity is 0")
	}
	if request.GetServiceCode() == "" {
		return srerr.New(srerr.ParamInvalid, request.GetServiceCode(), "service code is empty")
	}

	return nil
}

// checkRuleCCModeStatus 检查Rule的CC Mode是否适配了流量
func checkRuleCCModeStatus(ctx context.Context, req *pb.ILHRoutingReq, routingRule *rule.RoutingRuleParsed) {
	// 如果开启了CC Routing Mode流量但是Rule未按照CC Routing Mode去配置的时候，需要上报错误
	// 因为配置CC Routing Mode的Rule是开启CC Routing Mode流量之前的一个前置步骤
	if req.GetCcMode() == pb.CCMode_CCRouting && routingRule.CCMode != rule.CCModeCCRouting {
		monitoring.ReportError(ctx, monitoring.CatModuleILHRoutingCCMode, monitoring.CCRoutingRuleNotReady, strconv.Itoa(int(routingRule.ID)))
	}
}

func (s *SmartRoutingServiceImpl) getLmIDByServiceCode(ctx context.Context, serviceCode string) (string, *srerr.Error) {
	sortingCodeInfo, err := s.LfsCli.GetSortingCodeInfo(ctx, serviceCode)
	if err != nil {
		return "", err
	}

	return sortingCodeInfo.GetLmId(), nil
}

func (s *SmartRoutingServiceImpl) revampILHRouting(
	ctx context.Context, req *pb.ILHRoutingReq, availableLanes []*rule.RoutingLaneInfo, destinationPorts []string,
) (*rule.RoutingLaneInfo, *srerr.Error) {

	// Common request parameters
	var (
		productID   = int(req.GetProductId())
		twsCode     = req.GetTwsCode()
		dgType      = int(req.GetDgFlag())
		orderWeight = req.GetWeight()
	)

	lmID, err := s.getLmIDByServiceCode(ctx, req.GetServiceCode())
	if err != nil {
		return nil, err
	}

	// Get available LH configuration
	availableLHRule, err := s.availableLhService.GetAvailableLHRule(ctx, productID, dgType, twsCode, destinationPorts)
	if err != nil {
		logger.CtxLogErrorf(ctx, "Failed to get available LH configuration, err=%v", err)
		monitoring.ReportError(ctx, monitoring.CatModuleRevampILHRouting, monitoring.AvailableLHRuleNotFound, err.Error())
		return nil, err
	}

	// Get capacity settings for all ILH lines
	ilhLines := routing.ExtractResourceLineIDs(availableLanes, lfslib.ILHLine)
	ilhCapacitySettingMap := s.getILHCapacitySettings(ctx, ilhLines, twsCode, dgType, destinationPorts)

	routingReq := routing.RevampILHRoutingRequest{
		PackageNo:             req.GetPackageNo(),
		ProductID:             productID,
		LMID:                  lmID,
		DGType:                dgType,
		TWSCode:               twsCode,
		OrderWeight:           orderWeight,
		OrderTime:             timeutil.GetCurrentUnixTimeStamp(ctx),
		AvailableLanes:        availableLanes,
		AvailableLHRule:       availableLHRule,
		ILHCapacitySettingMap: ilhCapacitySettingMap,
	}

	routingResult, err := s.ilhRoutingService.RevampILHRouting(ctx, routingReq)
	if err != nil {
		return nil, err
	}

	return routingResult.Lane, nil
}

// getILHCapacitySettings retrieves capacity settings for all ILH lines
func (s *SmartRoutingServiceImpl) getILHCapacitySettings(
	ctx context.Context,
	ilhLines []string,
	twsCode string,
	dgType int,
	destinationPorts []string,
) map[string]entity2.ILHCapacitySettingInfo {
	ilhCapacitySettingMap := make(map[string]entity2.ILHCapacitySettingInfo)

	for _, ilh := range ilhLines {
		// Get LH capacity configuration
		capacitySettingInfo, err := s.lhCapacityService.GetLHCapacitySettingInfo(ctx, ilh, dgType, twsCode, destinationPorts)
		if err != nil {
			logger.CtxLogErrorf(ctx, "Failed to get LH capacity setting for ilh=%s, dgType=%d, twsCode=%s, destinationPorts=%v: %v",
				ilh, dgType, twsCode, destinationPorts, err)
			monitoring.ReportError(ctx, monitoring.CatModuleRevampILHRouting, monitoring.ILHCapacitySettingNotFound, ilh)
			continue
		}
		ilhCapacitySettingMap[ilh] = capacitySettingInfo
		logger.CtxLogInfof(ctx, "Added capacity setting for ilh=%s: time interval type=%s, bsa=%v, adhoc=%v",
			ilh, capacitySettingInfo.CapacitySetting.TimeIntervalType, capacitySettingInfo.CapacitySetting.BSAWeight, capacitySettingInfo.CapacitySetting.AdhocWeight)
	}

	logger.CtxLogInfof(ctx, "ILH Capacity Settings: %s", objutil.JsonString(ilhCapacitySettingMap))

	return ilhCapacitySettingMap
}
