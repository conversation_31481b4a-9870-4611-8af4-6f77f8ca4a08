package clickhouseutil

import (
	"context"
	"fmt"

	"git.garena.com/shopee/bg-logistics/go/gocommon/monitor"
	dbr "github.com/mailru/dbr"
	_ "github.com/mailru/go-clickhouse"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
)

var ckConn *dbr.Connection

func InitClickHouse() error {
	//host := configutil.GetClickHouseConfig(context.TODO()).Host
	//db, ckErr := dbr.Open("clickhouse", host, nil)
	//if ckErr != nil {
	//	return ckErr
	//}
	//if pingErr := db.Ping(); pingErr != nil {
	//	return pingErr
	//}
	//ckConn = db
	return nil
}

func Select(ctx context.Context, sql string, result interface{}) error {
	_, endFunc := monitor.AwesomeReportTransactionStart2(ctx)
	_, err := ckConn.NewSessionContext(ctx, nil).SelectBySql(sql).Load(result)
	if err != nil {
		endFunc(ClickhouseModule, ClickhouseRead, "error", fmt.Sprintf("clickhouse read err=%v", err))
		return err
	}
	endFunc(ClickhouseModule, ClickhouseRead, "0", "")
	return nil
}

func GetClickhouseDbAndTable() (string, string) {
	clickhouseConf := configutil.GetClickHouseConfig(context.TODO())
	return clickhouseConf.Db, clickhouseConf.Table
}
