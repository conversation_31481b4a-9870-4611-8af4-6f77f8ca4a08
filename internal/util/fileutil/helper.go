package fileutil

import (
	"bytes"
	"encoding/csv"
	"fmt"
	"strings"

	"github.com/360EntSecGroup-Skylar/excelize/v2"
)

// ParseExcelToMap 解析Excel文件到map数组，每行数据以map[列名]值的形式返回
func ParseExcelToMap(fileData []byte) ([]map[string]string, error) {
	// 首先尝试解析为Excel文件
	file, err := excelize.OpenReader(bytes.NewReader(fileData))
	if err != nil {
		// 如果Excel解析失败，尝试解析为CSV
		return parseCSVToMap(fileData)
	}
	// excelize doesn't have Close method

	sheets := file.GetSheetList()
	if len(sheets) == 0 {
		return nil, fmt.Errorf("no sheets found in file")
	}

	// 读取第一个sheet的数据
	rows, err := file.GetRows(sheets[0])
	if err != nil {
		return nil, err
	}

	if len(rows) < 2 {
		return nil, fmt.Errorf("file must have at least header and one data row")
	}

	// 第一行作为header
	header := rows[0]
	result := make([]map[string]string, 0, len(rows)-1)

	// 从第二行开始解析数据
	for i := 1; i < len(rows); i++ {
		row := rows[i]
		rowMap := make(map[string]string)

		for j, colName := range header {
			var cellValue string
			if j < len(row) {
				cellValue = strings.TrimSpace(row[j])
			}
			rowMap[colName] = cellValue
		}

		result = append(result, rowMap)
	}

	return result, nil
}

// parseCSVToMap 解析CSV文件到map数组
func parseCSVToMap(fileData []byte) ([]map[string]string, error) {
	reader := csv.NewReader(bytes.NewReader(fileData))
	records, err := reader.ReadAll()
	if err != nil {
		return nil, err
	}

	if len(records) < 2 {
		return nil, fmt.Errorf("CSV must have at least header and one data row")
	}

	// 第一行作为header
	header := records[0]
	// 清理BOM头
	if len(header) > 0 && strings.Contains(header[0], "\ufeff") {
		header[0] = strings.Replace(header[0], "\ufeff", "", 1)
	}

	result := make([]map[string]string, 0, len(records)-1)

	// 从第二行开始解析数据
	for i := 1; i < len(records); i++ {
		row := records[i]
		rowMap := make(map[string]string)

		for j, colName := range header {
			var cellValue string
			if j < len(row) {
				cellValue = strings.TrimSpace(row[j])
			}
			rowMap[colName] = cellValue
		}

		result = append(result, rowMap)
	}

	return result, nil
}

// GenerateCSV 生成CSV文件内容
func GenerateCSV(headers []string, rows [][]string) ([]byte, error) {
	buf := &bytes.Buffer{}
	writer := csv.NewWriter(buf)

	// 写入header
	if err := writer.Write(headers); err != nil {
		return nil, err
	}

	// 写入数据行
	for _, row := range rows {
		if err := writer.Write(row); err != nil {
			return nil, err
		}
	}

	writer.Flush()
	if err := writer.Error(); err != nil {
		return nil, err
	}

	return buf.Bytes(), nil
}
