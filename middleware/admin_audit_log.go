package middleware

import (
	"bytes"
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/chassis/core/handler"
	"git.garena.com/shopee/bg-logistics/go/chassis/core/invocation"
	chassisRestful "git.garena.com/shopee/bg-logistics/go/chassis/protocol/server/restful"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/audit_log"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/apiutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"github.com/emicklei/go-restful"
	jsoniter "github.com/json-iterator/go"
	"io/ioutil"
	"runtime"
	"strings"
)

const (
	AdminAuditLogHandlerName = "admin-audit-log"
)

var (
	defaultOperationList = []string{"create", "update", "delete", "edit", "submit", "copy", "save", "disable", "rollback", "confirm_as"}
)

func RegisterAdminAuditLogHandler() {
	_ = handler.RegisterHandler(AdminAuditLogHandlerName, NewAdminAuditLogHandler)
}

type AdminAuditLogHandler struct{}

func NewAdminAuditLogHandler() handler.Handler {
	return &AdminAuditLogHandler{}
}

func (a *AdminAuditLogHandler) Handle(i *invocation.Invocation) {
	var (
		ok   bool
		req  *restful.Request
		resp *restful.Response
		ctx  *chassisRestful.Context
		//err     error
		reqBody          []byte
		url              string
		moduleType       string
		auditLogCtxParam *dbutil.AuditLogCtxParam
	)
	req, ok = i.Args.(*restful.Request)
	if !ok {
		return
	}
	if resp, ok = i.Reply.(*restful.Response); !ok {
		return
	}
	ctx = chassisRestful.NewBaseServer(i.Ctx, req, resp)

	url = req.Request.URL.Path
	moduleType = convertUrlToModuleType(url)

	//获取Apollo配置
	auditLogConf := configutil.GetAuditLogConfig(ctx.Ctx)

	//如果开关开启了并且属于写接口，则复制一份request body
	if auditLogConf.NeedRecord && containsCUDPrefix(ctx.Ctx, url) {
		reqBodyBytes, rErr := ioutil.ReadAll(req.Request.Body)
		//写个error的json返回
		if rErr != nil {
			logger.CtxLogErrorf(i.Ctx, "AdminAuditLogHandler| read request body err:%v", rErr)
			apiutil.FailJSONResp(ctx, srerr.With(srerr.ReadRequestBodyErr, nil, rErr), rErr.Error())
			return
		}
		reqBodyReader := bytes.NewBuffer(reqBodyBytes) //由于把request的body读出来了，这里需要重新写入
		//注：chassis不支持get，post外的方法
		req.Request.Body = ioutil.NopCloser(reqBodyReader)
		reqBody = reqBodyBytes
	}

	i.Ctx = context.WithValue(
		i.Ctx,
		dbutil.AuditLogAfterKey,
		&dbutil.AuditLogCtxParam{},
	)

	i.Next()

	//开关未开启，直接返回
	if !auditLogConf.NeedRecord || !containsCUDPrefix(ctx.Ctx, url) {
		return
	}

	userEmail, _ := apiutil.GetUserInfo(i.Ctx)

	//初始化audit log service
	auditLogRepo := audit_log.NewAuditLogRepoImpl()
	auditLogService := audit_log.NewAuditLogServiceImpl(auditLogRepo)

	//根据AuditLogAfterKey获取audit log信息对象并转换成对应的模型
	if entityInterface := i.Ctx.Value(dbutil.AuditLogAfterKey); entityInterface != nil {
		if auditLogCtxParam, ok = entityInterface.(*dbutil.AuditLogCtxParam); ok {
			logger.CtxLogInfof(i.Ctx, "AdminAuditLogHandler| get audit log param from ctx success, audit log param:%v", auditLogCtxParam)
		} else {
			logger.CtxLogInfof(i.Ctx, "AdminAuditLogHandler| get empty audit log param from ctx, will just return")
			return
		}
	}

	if auditLogCtxParam == nil {
		logger.CtxLogInfof(i.Ctx, "AdminAuditLogHandler| audit log param is nil, will just return")
		return
	}
	if auditLogCtxParam.AuditLogExtendInfo.Message != "" {
		logger.CtxLogInfof(i.Ctx, "audit log ctx message:%v", auditLogCtxParam.AuditLogExtendInfo.Message)
	}
	//开启go routine进行写入，避免影响主流程
	go func() {
		defer func() {
			if err := recover(); err != nil {
				var buf [4096]byte
				n := runtime.Stack(buf[:], false)
				errMsg := fmt.Sprintf("[Recovery] panic recovered:\n%s\n%s", err, string(buf[:n]))
				logger.CtxLogErrorf(i.Ctx, errMsg)
			}
		}()

		var auditLogTabs []audit_log.AudiLogTab
		for _, modelInfo := range auditLogCtxParam.ModelNameInfoMap {
			auditLogTab := audit_log.AudiLogTab{
				Operator:             userEmail,
				ModelName:            modelInfo.ModelName,
				ModelId:              modelInfo.Id,
				MaskProductId:        modelInfo.MaskProductId,
				FulfillmentProductId: modelInfo.FulfillmentProductId,
				RuleId:               modelInfo.RuleId,
				RuleVolumeId:         modelInfo.RuleVolumeId,
				TaskId:               modelInfo.TaskId,
				ModuleType:           moduleType,
				Interface:            url,
				RequestBody:          string(reqBody),
				Ctime:                recorder.Now(i.Ctx).Unix(),
				Mtime:                recorder.Now(i.Ctx).Unix(),
			}
			extendInfoString, _ := jsoniter.MarshalToString(modelInfo.ExtendInfo)
			auditLogTab.ExtendInfo = extendInfoString
			auditLogTabs = append(auditLogTabs, auditLogTab)
		}
		//audit log tab数组为空，直接返回。因为当底层table黑名单过滤以后，有可能出现空的audit log tab数组，这时无需记录
		if len(auditLogTabs) == 0 {
			logger.CtxLogInfof(i.Ctx, "AdminAuditLogHandler|empty audit log tab list, just return")
			return
		}
		//批量插入audit log对象
		ids, cErr := auditLogService.BatchCreateAuditLogRecord(i.Ctx, auditLogTabs)
		if cErr != nil {
			logger.CtxLogErrorf(i.Ctx, "AdminAuditLogHandler|batch create audit log record err:%v", cErr)
			return
		}
		logger.CtxLogInfof(i.Ctx, "AdminAuditLogHandler|batch create audit log success, ids:%v", ids)
	}()
}

func (a *AdminAuditLogHandler) Name() string {
	return AdminAuditLogHandlerName
}

// 判断url是否需要记录变更信息（只有更改操作需要记录
func containsCUDPrefix(ctx context.Context, url string) bool {
	var containsPrefix, containsOperation bool
	auditLogConf := configutil.GetAuditLogConfig(ctx)

	//SSCSMR-1386:正则+白名单
	var operationList = defaultOperationList
	if len(auditLogConf.OperationTypeList) != 0 {
		operationList = auditLogConf.OperationTypeList
	}

	//在入口处根据白名单放行
	for _, whiteUrl := range auditLogConf.WhiteUrls {
		if whiteUrl == url {
			return true
		}
	}

	//根据url prefix判断是否要进行记录, 不符合则直接return false
	for _, prefix := range auditLogConf.Prefixes {
		if strings.HasPrefix(url, prefix) {
			containsPrefix = true
			break
		}
	}
	//根据操作类型判断
	for _, operationType := range operationList {
		if strings.Contains(url, operationType) {
			containsOperation = true
			break
		}
	}
	return containsPrefix && containsOperation
}

// 根据url返回对应的模块名字
func convertUrlToModuleType(url string) string {
	if strings.Contains(url, "/api/admin/allocation") {
		return "3pl masking allocation"
	} else if strings.Contains(url, "/api/admin/allocate_forecast") {
		return "3pl masking forecast"
	} else if strings.Contains(url, "/api/admin/smart_routing") && !strings.Contains(url, "task") {
		return "smart routing soft criteria"
	} else if strings.Contains(url, "/api/admin/smart_routing") && strings.Contains(url, "task") {
		return "smart routing forecast"
	} else if strings.Contains(url, "/api/admin/rule_volume") {
		return "3pl masking rule volume"
	} else if strings.Contains(url, "/api/admin/product_priority") {
		return "masking product priority"
	} else if strings.Contains(url, "/api/admin/volume_routing") {
		return "volume routing"
	} else {
		return "unknown type"
	}
}
