/*
@Time : 2020/12/3 5:47 下午
<AUTHOR> shuoxiao
@File : grpc_logger
*/
package chassis_middleware

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/monitor"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	pb "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing-protocol/protocol/go"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/mockutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/requestid"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/monitoring"
	uuid "github.com/satori/go.uuid"
	"google.golang.org/grpc"
	"io/ioutil"
	"runtime"
	"runtime/debug"
	"strconv"
)

var (
	dunno     = []byte("???")
	centerDot = []byte("·")
	dot       = []byte(".")
	slash     = []byte("/")
)

type CommonRequest interface {
	GetHeader() *pb.ReqHeader
}

type CommonResponse interface {
	GetHeader() *pb.RespHeader
}

func LogRequest(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
	// todo get logger_id from req
	requestId := requestid.GenRequestId()
	if request, ok := req.(CommonRequest); ok && request.GetHeader() != nil {
		requestId = *(request.GetHeader().RequestId)
		ctx = mockutil.PatchGrpcMockCtx(ctx, request.GetHeader().GetMockReqid(), request.GetHeader().GetMockSystemsKey())
	}

	if len(requestId) == 0 {
		requestId = uuid.NewV4().String() // nolint
	}
	//logger.SetLogId(requestId)
	//defer logger.UnsetLogId()

	//log 取ID 更加频繁，放在后面，只需要取1次，
	ctx = requestid.SetToCtx(ctx, requestId)
	ctx = logger.NewLogContext(ctx, requestId)

	// SSCSMR-3546: add url to ctx
	if info != nil {
		url := info.FullMethod
		if ctx.Value(constant.URLKey) == nil {
			ctx = context.WithValue(ctx, constant.URLKey, url)
		}
	}

	resp, err := handler(ctx, req)

	if response, ok := resp.(CommonResponse); ok {
		response.GetHeader().RequestId = &requestId
	}

	return resp, err
}

func Recovery(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (resp interface{}, err error) {
	defer func() {
		if e := recover(); e != nil {
			stack := stack(5)
			monitoring.ReportError(ctx, monitoring.CatPanic, monitoring.ServicePanic, fmt.Sprintf("[Recovery]panic recovered:\n%s\n%s\n%s", e, stack, debug.Stack()))
			logger.CtxLogErrorf(ctx, "[Recovery]panic recovered:\n%s\n%s\n%s", e, stack, debug.Stack())
			err = errors.New("server internal panic error")
		}
	}()
	resp, err = handler(ctx, req)
	return
}

func Metrics(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
	// 开始CAT上报
	monContext := monitor.AwesomeReportTransactionStart(ctx)
	handlerStatus := constant.StatusSuccess
	reportData := ""
	clientIP := ""
	if request, ok := req.(CommonRequest); ok {
		clientIP = *(request.GetHeader().CallerIp)
	}
	start := recorder.Now(ctx)
	resp, err := handler(monContext, req)
	end := recorder.Now(ctx)
	latency := end.Sub(start)
	if err != nil {
		handlerStatus = constant.StatusError
		reportData = fmt.Sprintf("clientIp=%s||latency=%s||msg=%s", clientIP, latency, err.Error())
	}
	if res, ok := resp.(CommonResponse); ok {
		handlerStatus = strconv.Itoa(int(res.GetHeader().GetRetcode()))
		reportData = fmt.Sprintf("clientIp=%s||latency=%s", clientIP, latency)
	}
	//if config.GetPrometheusReportConfig().Switch {
	//	err = metrics.CounterIncr(constant.MetricReqStatus, map[string]string{"url": info.FullMethod, "rsp_status": handlerStatus})
	//	if err != nil {
	//		logger.CtxLogErrorf(ctx, "metrics.CounterIncr error: %v", err)
	//	}
	//}
	// 结束CAT上报
	monitor.AwesomeReportTransactionEnd(monContext, "RPC.Client", info.FullMethod, handlerStatus, reportData)

	return resp, err
}

func stack(skip int) []byte {
	buf := new(bytes.Buffer) // the returned data
	// As we loop, we open files and read them. These variables record the currently
	// loaded file.
	var lines [][]byte
	var lastFile string
	for i := skip; ; i++ { // Skip the expected number of frames
		pc, file, line, ok := runtime.Caller(i)
		if !ok {
			break
		}
		// Print this much at least.  If we can't find the source, it won't show.
		fmt.Fprintf(buf, "%s:%d (0x%x)\n", file, line, pc)
		if file != lastFile {
			data, err := ioutil.ReadFile(file)
			if err != nil {
				continue
			}
			lines = bytes.Split(data, []byte{'\n'})
			lastFile = file
		}
		fmt.Fprintf(buf, "\t%s: %s\n", function(pc), source(lines, line))
	}
	return buf.Bytes()
}

func function(pc uintptr) []byte {
	fn := runtime.FuncForPC(pc)
	if fn == nil {
		return []byte("???")
	}
	name := []byte(fn.Name())
	// The name includes the path name to the package, which is unnecessary
	// since the file name is already included.  Plus, it has center dots.
	// That is, we see
	//	runtime/debug.*T·ptrmethod
	// and want
	//	*T.ptrmethod
	// Also the package path might contains dot (e.g. code.google.com/...),
	// so first eliminate the path prefix
	if lastslash := bytes.LastIndex(name, slash); lastslash >= 0 {
		name = name[lastslash+1:]
	}
	if period := bytes.Index(name, dot); period >= 0 {
		name = name[period+1:]
	}
	name = bytes.Replace(name, centerDot, dot, -1)
	return name
}

// source returns a space-trimmed slice of the n'th line.
func source(lines [][]byte, n int) []byte {
	n-- // in stack trace, lines are 1-indexed but our array is 0-indexed
	if n < 0 || n >= len(lines) {
		return dunno
	}
	return bytes.TrimSpace(lines[n])
}
