package middleware

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	chassis_middleware "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/middleware/grpc_logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/middleware/traffic_playback"
	jsoniter "github.com/json-iterator/go"
	"github.com/panjf2000/ants/v2"
	"google.golang.org/grpc"
	"os"
	"strings"
)

var (
	goroutinePool     *ants.Pool
	goroutinePoolSize = 16
)

func init() {
	p, err := ants.NewPool(goroutinePoolSize, ants.WithNonblocking(true))
	if err != nil {
		logger.LogErrorf("New goroutine pool fail | err=%v", err)
		panic(err)
	}

	goroutinePool = p
}

type TrafficCollectManager struct {
	CollectVersion     int
	SwitchToUseCollect map[string]bool
}

func GrpcTrafficCollect(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
	resp, err := handler(ctx, req)
	// 这里不需要处理err，因为err也需要被收集，为了不影响主流程，这里直接用goroutine
	handlerErr := err
	if IsReqOpenCollect(ctx, info.FullMethod) {
		requestId, requestIp := "", ""
		if request, ok := req.(chassis_middleware.CommonRequest); ok {
			requestId = *(request.GetHeader().RequestId)
			requestIp = *(request.GetHeader().CallerIp)
		}
		bList, mErr := jsoniter.Marshal(resp)
		if mErr != nil {
			logger.CtxLogErrorf(ctx, "traffic collect | json marshal err: %v", mErr)
			return resp, handlerErr
		}
		submitErr := goroutinePool.Submit(func() {
			sErr := traffic_playback.SendGRPC(ctx, &traffic_playback.GRPCForSend{
				Req:        req,
				RespBody:   bList,
				FullMethod: info.FullMethod,
				RequestId:  requestId,
				CallerIp:   requestIp,
			})
			if sErr != nil {
				logger.CtxLogErrorf(ctx, "traffic collect | send grpc err:%v", sErr)
			}
		})
		if submitErr != nil {
			logger.CtxLogErrorf(ctx, "traffic collect | submit task failed, err:%v", submitErr)
		}
	}
	return resp, handlerErr
}

func IsReqOpenCollect(ctx context.Context, methodName string) bool {
	trafficCollectConfig := configutil.GetTrafficCollectConfig(ctx)
	return trafficCollectConfig.SwitchUseCollectMap[methodName] && CheckCollectTypeUse(ctx, trafficCollectConfig)
}

func CheckCollectTypeUse(ctx context.Context, trafficCollectConfig configutil.TrafficCollectConfig) bool {
	switch strings.ToUpper(trafficCollectConfig.CollectType) {
	case "ALL":
		return true
	case "CLOSE":
		return false
	case "IP":
		return strings.Contains(trafficCollectConfig.CollectIps, os.Getenv("POD_IP")) // nolint
	default:
		return false
	}
}
