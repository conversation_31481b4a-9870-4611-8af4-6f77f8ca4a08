package middleware

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis/core/handler"
	"git.garena.com/shopee/bg-logistics/go/chassis/core/invocation"
	chassisRestful "git.garena.com/shopee/bg-logistics/go/chassis/protocol/server/restful"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/apiutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/jwtutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/envvar"
	"github.com/emicklei/go-restful"
	"regexp"
	"sync"
)

const (
	JwtTokenHandleKey = "jwt_token_handle_key"
)

var (
	jwtUrlPatternExp     []*regexp.Regexp
	jwtUrlPatternExpOnce sync.Once
)

func RegisterJwtTokenHandler() {
	_ = handler.RegisterHandler(JwtTokenHandleKey, NewJwtTokenHandler)
}

type JwtTokenHandle struct {
}

func (j JwtTokenHandle) Handle(invocation *invocation.Invocation) {
	var (
		ok   bool
		req  *restful.Request
		resp *restful.Response
		ctx  *chassisRestful.Context
	)

	jwtUrlPatternExpOnce.Do(func() {
		jwtUrlPatternExp = genJwtPathReg(invocation.Ctx)
	})

	req, ok = invocation.Args.(*restful.Request)
	if resp, ok = invocation.Reply.(*restful.Response); !ok {
		return
	}
	ctx = chassisRestful.NewBaseServer(invocation.Ctx, req, resp)

	url := req.Request.URL.Path
	if nil == ctx.Ctx.Value(constant.URLKey) {
		invocation.Ctx = context.WithValue(ctx.Ctx, constant.URLKey, url)
	}

	for _, exp := range jwtUrlPatternExp {
		if exp.MatchString(url) {
			jwtConfig := configutil.GetJWTConf(ctx.Ctx)
			if url == "/api/admin/business_audit/listener" {
				invocation.Ctx = ctx.Ctx
				break
			}
			token := ctx.ReadHeader(constant.HeaderJWTToken)
			info, err := jwtutil.ParseJWT(ctx.Ctx, token, jwtConfig.Secret, jwtConfig.Optr)
			if err == nil {
				ctx.Ctx = context.WithValue(ctx.Ctx, constant.EmailKey, info.Info.User.Email)
				ctx.Ctx = context.WithValue(ctx.Ctx, constant.AccountKey, info.Info.User.Name)
				ctx.Ctx = context.WithValue(ctx.Ctx, constant.ReqCountryKey, info.Info.Entity.Country)
				ctx.Ctx = context.WithValue(ctx.Ctx, constant.ReqTimeZoneKey, info.Info.Entity.TimeZone)
				ctx.Ctx = context.WithValue(ctx.Ctx, constant.CategoryKey, info.Info.User.Category)
				invocation.Ctx = ctx.Ctx
			} else if !jwtConfig.Closed {
				apiutil.FailJSONResp(ctx, srerr.With(srerr.JwtTokenErr, nil, err), err.Error())
				return
			} else {
				logger.CtxLogErrorf(ctx.Ctx, "parse jwt token err %v", err)
				ctx.Ctx = context.WithValue(ctx.Ctx, constant.EmailKey, "<EMAIL>")
				ctx.Ctx = context.WithValue(ctx.Ctx, constant.AccountKey, "smartrouting")
				ctx.Ctx = context.WithValue(ctx.Ctx, constant.ReqCountryKey, envvar.GetCID())
				ctx.Ctx = context.WithValue(ctx.Ctx, constant.ReqTimeZoneKey, 0)
				invocation.Ctx = ctx.Ctx
			}
			break

		}
	}

	invocation.Next()
}

func (j JwtTokenHandle) Name() string {
	return JwtTokenHandleKey
}

func NewJwtTokenHandler() handler.Handler {
	return &JwtTokenHandle{}
}

func genJwtPathReg(ctx context.Context) []*regexp.Regexp {
	needJwtTokenUrlPattern := configutil.GetJWTConf(ctx).NeedJwtPaths
	var pathExps = make([]*regexp.Regexp, len(needJwtTokenUrlPattern))
	for i, s := range needJwtTokenUrlPattern {
		pathExps[i] = regexp.MustCompile(s)
	}
	return pathExps
}
