package middleware

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/chassis/core/handler"
	"git.garena.com/shopee/bg-logistics/go/chassis/core/invocation"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/requestid"
	"github.com/emicklei/go-restful"
	"io/ioutil"
	"net"
	"net/http"
	"strings"
)

const (
	RequestContextHandlerName = "request-context"
)

func RegisterRequestContextHandler() {
	_ = handler.RegisterHandler(RequestContextHandlerName, NewRequestContextHandler)
}

func GetRequestIp(ctx context.Context, req *restful.Request) string {
	requestIp := req.HeaderParameter(constant.HeaderRequestIP)
	if requestIp == "" {
		ip, _, err := net.SplitHostPort(req.Request.RemoteAddr)
		if err != nil {
			logger.CtxLogErrorf(ctx, "split ip failed,RemoteAddr:%s", req.Request.RemoteAddr)
		} else {
			requestIp = ip
		}
	}
	return requestIp
}

func GetRetCode(ctx context.Context, url string, resp *restful.Response) string {
	retCode := resp.Header().Get(constant.HeaderRetCode)
	if retCode == "" {
		logger.CtxLogInfof(ctx, "%s not exists Ret-Code header", url)
		retCode = constant.StatusSuccess
	}
	return retCode
}

type ResponseCapture struct {
	http.ResponseWriter
	wroteHeader bool
	status      int
	body        *bytes.Buffer
}

func NewResponseCapture(w http.ResponseWriter, requestId string) *ResponseCapture {
	w.Header().Set("X-REQUEST-ID", requestId)
	return &ResponseCapture{
		ResponseWriter: w,
		wroteHeader:    false,
		body:           new(bytes.Buffer),
	}
}

func (c ResponseCapture) Header() http.Header {
	return c.ResponseWriter.Header()
}

func (c ResponseCapture) Write(data []byte) (int, error) {
	logger.CtxLogInfof(context.TODO(), "debugheader %v", c.wroteHeader)

	if !c.wroteHeader {
		c.WriteHeader(http.StatusOK)
	}
	c.body.Write(data)
	return c.ResponseWriter.Write(data)
}

func (c *ResponseCapture) WriteHeader(statusCode int) {
	//??
	logger.CtxLogInfof(context.TODO(), "debugheader %v", statusCode)
	c.status = statusCode
	c.wroteHeader = true
	c.ResponseWriter.WriteHeader(statusCode)
}

func (c ResponseCapture) Bytes() []byte {
	return c.body.Bytes()
}

func (c ResponseCapture) StatusCode() int {
	return c.status
}

func getRequestBody(req *restful.Request) (*restful.Request, []byte, error) {
	r := req.Request
	body, bodyErr := ioutil.ReadAll(r.Body)
	if bodyErr != nil {
		return req, nil, bodyErr
	}

	dup := make([]byte, len(body))
	copy(dup, body)
	r.Body = ioutil.NopCloser(bytes.NewBuffer(body))
	return req, dup, nil
}

type RequestContextHandler struct{}

func NewRequestContextHandler() handler.Handler {
	return &RequestContextHandler{}
}

func (th *RequestContextHandler) Handle(i *invocation.Invocation) {
	var (
		ok   bool
		req  *restful.Request
		resp *restful.Response
		err  error
	)
	req, ok = i.Args.(*restful.Request)
	if !ok {
		return
	}
	// replace
	requestId := requestid.GetRequestID(req)
	logger.SetLogId(requestId)
	requestIP := GetRequestIp(i.Ctx, req)
	defer logger.UnsetLogId()
	ctx := logger.NewLogContext(i.Ctx, requestId)
	ctx = requestid.SetToCtx(ctx, requestId)

	// 获取上游通过header头传递的cat上下文信息
	//ctx, endFunc := monitoring.RequestStartReport(ctx, req)
	i.Ctx = ctx

	// 获取request body，需要在 rest api 解析之前
	start := recorder.Now(ctx)
	req, body, err := getRequestBody(req)
	if err != nil {
		logger.CtxLogErrorf(ctx, "middleware logger, getRequestBody error: %v", err)
	}

	resp, ok = i.Reply.(*restful.Response)
	c := NewResponseCapture(resp.ResponseWriter, requestId)
	resp.ResponseWriter = c
	i.Next()
	end := recorder.Now(ctx)
	latency := end.Sub(start)
	//queryParameter := req.Request.URL.RawQuery

	logger.CtxLogInfof(ctx, "%v|%v|%s|%s| %s - %s|request: %q|response: %d - %s",
		end.Format("2006/01/02 - 15:04:05"),
		latency,
		requestId,
		requestIP,
		req.Request.Method,
		req.Request.URL.RequestURI(),
		compactBody(body),
		resp.StatusCode(),
		compactBody(c.Bytes()),
	)

	//结束CAT上报
	//monitoring.DiagnoseResponseReport(ctx, endFunc, resp.StatusCode(), path, queryParameter, compactBody(body), retCode, requestId)
	return
}

func compactBody(body []byte) string {
	buf := new(bytes.Buffer)
	if err := json.Compact(buf, body); err != nil {
		return string(body)
	}
	return buf.String()
}

func (th *RequestContextHandler) Name() string {
	return RequestContextHandlerName
}

func headerToJson(header http.Header) string {
	valueSet := map[string]string{}
	for key, values := range header {
		val := strings.Join(values[:], ";")
		valueSet[key] = val
	}

	data, err := json.Marshal(valueSet)
	if err != nil {
		return ""
	}
	return fmt.Sprintf(string(data))
}
