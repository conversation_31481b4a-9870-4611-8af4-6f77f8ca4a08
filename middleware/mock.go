package middleware

import (
	"context"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant/enum"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/mockutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/envvar"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"
)

func GrpcMockCtx(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
	// 仅针对Non Live测试场景
	if envvar.GetEnv() == enum.LIVE {
		return handler(ctx, req)
	}

	md, ok := metadata.FromIncomingContext(ctx)
	if ok {
		mockFlag := md.Get(mockutil.MockAutoTestKey)
		if len(mockFlag) > 0 && mockFlag[0] == mockutil.DoMockFlag {
			var mockRequestID, mockSystemKey, mockTypeKey string
			if v := md.Get(mockutil.MockRequestID); len(v) > 0 {
				mockRequestID = v[0]
			}
			if v := md.Get(mockutil.MockSystemsKey); len(v) > 0 {
				mockSystemKey = v[0]
			}
			if v := md.Get(mockutil.MockTypeKey); len(v) > 0 {
				mockTypeKey = v[0]
			}

			mockValues := map[string]string{
				mockutil.MockRequestID:  mockRequestID,
				mockutil.MockSystemsKey: mockSystemKey,
				mockutil.MockTypeKey:    mockTypeKey,
			}

			// 在这里把mock信息写到ctx
			ctx = context.WithValue(ctx, mockutil.CtxMockDef{}, mockValues)
		}
	}

	return handler(ctx, req)
}
