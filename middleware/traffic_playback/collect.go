package traffic_playback

const (
	TestAddr = "127.0.0.1:8002"
	LiveAddr = "proxy.comparator.i.sls.shopee.io:19556"
	IP       = "ip"
	ALL      = "all"
	CLOSE    = "close"
)

type CollectOfRequest struct {
	Method string              `json:"method"`
	URI    string              `json:"uri"`
	Header map[string]string   `json:"header"`
	Body   string              `json:"body"`
	Get    map[string][]string `json:"get"`
	IP     string              `json:"ip"`
}

type CollectOfResponse struct {
	StatusCode int    `json:"status_code"`
	Content    string `json:"content"`
}

type CollectType struct {
	RequestData *CollectOfRequest  `json:"request"`
	Response    *CollectOfResponse `json:"response"`
	Exception   string             `json:"exception"`
}
