package traffic_playback

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/comparator/sender"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant/enum"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/envvar"
	"net/http"
	"os"
	"strings"
	"time"
)

const (
	JSONContentType = "application/json; charset=utf-8"
	ProjectName     = "smartrouting"
	ModuleName      = "grpc"
)

var (
	grpcS         *sender.Sender
	domainForGrpc string
)

type GRPCForSend struct {
	Req        interface{}
	RespBody   []byte
	FullMethod string
	RequestId  string
	CallerIp   string
}

func InitTrafficCollect() error {
	addr := LiveAddr
	env := strings.ToUpper(envvar.GetEnv())
	switch env {
	case enum.LIVE:
	case enum.TEST, enum.UAT, enum.STAGING:
		addr = TestAddr
	default:
		return nil
	}

	grpcSender, err := sender.New(
		sender.WithDialTimeout(time.Millisecond*200),
		sender.WithSendTimeout(time.Millisecond*200),
		sender.WithMaxConn(25), // 一个协程默认处理QPS为40，流量最多的id市场日常单机流量不超过800
		sender.WithBufferCap(10000),
		sender.WithAddress(addr),
		sender.WithMaxConnLifeTime(time.Second*30),
	)
	if err != nil {
		logger.LogErrorf("collect fail, err=%v\n", err)
		return err
	} else {
		logger.LogInfof("collect grpc success\n")
		grpcS = grpcSender
	}
	projectName := ProjectName
	if os.Getenv("PROJECT_NAME") != "" { // nolint
		projectName = os.Getenv("PROJECT_NAME") // nolint
	}
	moduleName := ModuleName
	if os.Getenv("MODULE_NAME") != "" { // nolint
		moduleName = os.Getenv("MODULE_NAME") // nolint
	}
	domainForGrpc = fmt.Sprintf("%s-%s-%s-%s",
		projectName, moduleName, os.Getenv("ENV"), os.Getenv("CID")) // nolint
	return nil
}

func SendGRPC(ctx context.Context, info *GRPCForSend) error {
	if info == nil {
		return errors.New("GRPCForSend info is nil")
	}
	headers := map[string]string{"X-Request-Id": info.RequestId}
	requestBody, _ := json.Marshal(info.Req)
	headers["Content-Type"] = JSONContentType
	collectReq := &CollectOfRequest{
		Method: "GRPC",
		URI:    info.FullMethod,
		Header: headers,
		Body:   string(requestBody),
		IP:     info.CallerIp,
	}
	collectRes := &CollectOfResponse{
		StatusCode: http.StatusOK,
		Content:    string(info.RespBody),
	}
	collect := &CollectType{
		RequestData: collectReq,
		Response:    collectRes,
	}
	byteStr, _ := json.Marshal(collect)
	if grpcS == nil {
		return errors.New("grpcS still nil")
	}
	return grpcS.SendData(domainForGrpc, info.FullMethod, byteStr)
}
