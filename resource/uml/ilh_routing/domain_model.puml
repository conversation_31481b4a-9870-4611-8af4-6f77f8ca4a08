@startuml

' 定义类
class LhCapacityConfig {
    - CapacityID : Integer
    - CapacityName : String
    - IlhVendorName : String
    - LineID : Integer
    - LineName : String
    - DgType : Integer
    - TWS : String
    - DestinationPort : String
    - DefaultBsaWeight : Double
    - DefaultAdhocWeight : Double
    - SpecialDateBsaWeight : Double
    - SpecialTimeSlotBsaWeight : Double
    - SpecialDateAndTimeSlotBsaWeight : Double
    - Status : Integer
    - EffectiveStartTime: Date
}

class ProductAvailableLhConfig {
    - ProductID : Integer
    - MultiRules : Boolean
    - AvailableLh : String
    - AvailableCc : String
    - EffectiveStartTime: Date
}

class ForecastTask {
    - TaskID : Integer
    - TaskName : String
    - Ilh : String
    - ProductID : Integer
    - ForecastStatus : Integer
    - DeployStatus : Integer
    - ShipmentResource : Integer
    - StartDate : Date
    - EndDate : Date
}

class ForecastLhCapacityConfig {
    - TaskID : Integer
    - CapacityID : Integer
    - CapacityName : String
    - IlhVendorName : String
    - LineID : Integer
    - LineName : String
    - DgType : Integer
    - TWS : String
    - DestinationPort : String
    - DefaultBsaWeight : Double
    - DefaultAdhocWeight : Double
    - SpecialDateBsaWeight : Double
    - SpecialTimeSlotBsaWeight : Double
    - SpecialDateAndTimeSlotBsaWeight : Double
}

class ForecastProductAvailableLhConfig {
    - TaskID : Integer
    - ProductID : Integer
    - MultiRules : Boolean
    - AvailableLh : String
    - AvailableCc : String
}

class ForecastResult {
    - TaskID : Integer
    - ProductID : Integer
    - RuleID : Integer
    - LH : String
    - CC : String
    - DgType : Integer
    - AssignedBsaWeight : Double
    - AssignedAdhocWeight : Double
}

' 定义关系
ForecastTask "1" -- "*" ForecastLhCapacityConfig : contains
ForecastTask "1" -- "*" ForecastProductAvailableLhConfig : contains
ForecastTask "1" -- "*" ForecastResult : contains
ForecastLhCapacityConfig "1" -- "1" LhCapacityConfig : deploy
ForecastProductAvailableLhConfig "1" -- "1" ProductAvailableLhConfig : deploy

@enduml