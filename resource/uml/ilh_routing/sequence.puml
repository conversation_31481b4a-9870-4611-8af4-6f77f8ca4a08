@startuml

start

:Step 1: 检查 BSA 预留资源;
repeat
  :查询所有可用的 Line 和其 BSA 预留资源;
  if (BSA 预留资源满足 Carton 重量?) then (是)
    :分配资源;
    stop
  endif
repeat while (所有 Line 检查完毕?)

:Step 2: 纳入 BSA 非预留资源;
repeat
  :查询所有 Line 的 BSA 非预留资源;
  :计算每条 Line 的 BSA 综合评分;
  if (BSA 总资源足够?) then (是)
    :分配资源;
    stop
  endif
repeat while (所有 Line 检查完毕?)

:Step 3: 纳入 Adhoc 资源;
repeat
  :查询所有 Line 的 Adhoc 资源;
  :计算每条 Line 的综合评分;
  if (BSA + Adhoc 资源足够?) then (是)
    :分配资源;
    stop
  endif
repeat while (所有 Line 检查完毕?)

:Step 4: 进入兜底逻辑;
:根据product配置权重分配一条LH Line;

:Step 5: 调度CC;
repeat
  :检查是否已经有达到最大货量的CC;
  if (有达到最大货量的CC?) then (是)
    :过滤掉这些CC;
  endif
  :检查是否存在还未满足最小货量的CC;
  if (存在未满足最小货量的CC?) then (是)
    :按权重随机分配一个CC;
  else (否)
    :在全部CC中按权重随机分配一个CC;
  endif
repeat while (所有 CC 检查完毕?)

:Step 6: 更新资源状态;
:实时更新 BSA 和 Adhoc 的剩余资源;
:记录分配日志;

stop

@enduml
