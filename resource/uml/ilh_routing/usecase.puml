@startuml
actor User

usecase "Configure LH Capacity" as UC1
usecase "Configure Product Rules" as UC2

package ForecastTask {
    usecase "Configure Forecast Task" as UC3
    usecase "Execute Forecast Task" as UC4
    usecase "Export Forecast Results" as UC5
    usecase "Deploy Forecast Task" as UC9
}

usecase "Update ILH & CC Smart Routing" as UC6
usecase "Allocate LH" as UC7
usecase "Allocate CC" as UC8

User --> UC1
User --> UC2
User --> UC3
User --> UC5
User --> UC9
UC3 --> UC4 : triggers
UC1 --> UC6 : triggers
UC2 --> UC6 : triggers
UC6 --> UC7 : includes
UC6 --> UC8 : includes
UC9 --> UC6 : triggers

@enduml
